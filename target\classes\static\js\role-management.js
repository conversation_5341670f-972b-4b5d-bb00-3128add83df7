// 角色管理JavaScript
let currentPage = 1;
let pageSize = 10;
let totalPages = 0;
let selectedRoles = [];
let allMenus = [];
let menuTree = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadRoles();
    loadAllMenus();
});

// 加载角色列表
function loadRoles() {
    const params = {
        current: currentPage,
        size: pageSize,
        roleName: document.getElementById('searchRoleName').value,
        roleCode: document.getElementById('searchRoleCode').value,
        status: document.getElementById('searchStatus').value
    };

    axios.get('/api/system/role/page', { params })
        .then(response => {
            if (response.data.code === 200) {
                const data = response.data.data;
                renderRoleTable(data.records);
                updatePagination(data);
            } else {
                showMessage('加载角色列表失败：' + response.data.message, 'error');
            }
        })
        .catch(error => {
            console.error('加载角色列表失败:', error);
            showMessage('加载角色列表失败', 'error');
        });
}

// 渲染角色表格
function renderRoleTable(roles) {
    const tbody = document.getElementById('roleTableBody');
    tbody.innerHTML = '';

    roles.forEach(role => {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-50';
        
        const statusBadge = role.status === '1' 
            ? '<span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">启用</span>'
            : '<span class="px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full">禁用</span>';

        row.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap">
                <input type="checkbox" class="role-checkbox rounded" value="${role.id}" onchange="updateSelectedRoles()">
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${role.roleName}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${role.roleCode}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${role.description || ''}</td>
            <td class="px-6 py-4 whitespace-nowrap">${statusBadge}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${role.sort || 0}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${formatDate(role.createTime)}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-2">
                    <button onclick="editRole(${role.id})" class="text-blue-600 hover:text-blue-900" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button onclick="assignMenus(${role.id}, '${role.roleName}')" class="text-green-600 hover:text-green-900" title="分配菜单">
                        <i class="fas fa-sitemap"></i>
                    </button>
                    <button onclick="copyRole(${role.id})" class="text-purple-600 hover:text-purple-900" title="复制角色">
                        <i class="fas fa-copy"></i>
                    </button>
                    <button onclick="deleteRole(${role.id})" class="text-red-600 hover:text-red-900" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// 更新分页信息
function updatePagination(data) {
    totalPages = data.pages;
    document.getElementById('pageStart').textContent = (currentPage - 1) * pageSize + 1;
    document.getElementById('pageEnd').textContent = Math.min(currentPage * pageSize, data.total);
    document.getElementById('totalCount').textContent = data.total;

    // 更新分页按钮状态
    document.getElementById('prevBtn').disabled = currentPage <= 1;
    document.getElementById('nextBtn').disabled = currentPage >= totalPages;

    // 生成页码按钮
    generatePageNumbers();
}

// 生成页码按钮
function generatePageNumbers() {
    const pageNumbers = document.getElementById('pageNumbers');
    pageNumbers.innerHTML = '';

    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
        const button = document.createElement('button');
        button.textContent = i;
        button.className = `px-3 py-2 text-sm rounded-md transition-colors ${
            i === currentPage 
                ? 'bg-blue-600 text-white' 
                : 'bg-gray-200 text-gray-600 hover:bg-gray-300'
        }`;
        button.onclick = () => goToPage(i);
        pageNumbers.appendChild(button);
    }
}

// 跳转到指定页
function goToPage(page) {
    currentPage = page;
    loadRoles();
}

// 上一页
function previousPage() {
    if (currentPage > 1) {
        currentPage--;
        loadRoles();
    }
}

// 下一页
function nextPage() {
    if (currentPage < totalPages) {
        currentPage++;
        loadRoles();
    }
}

// 搜索角色
function searchRoles() {
    currentPage = 1;
    loadRoles();
}

// 重置搜索
function resetSearch() {
    document.getElementById('searchRoleName').value = '';
    document.getElementById('searchRoleCode').value = '';
    document.getElementById('searchStatus').value = '';
    currentPage = 1;
    loadRoles();
}

// 全选/取消全选
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.role-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    
    updateSelectedRoles();
}

// 更新选中的角色
function updateSelectedRoles() {
    const checkboxes = document.querySelectorAll('.role-checkbox:checked');
    selectedRoles = Array.from(checkboxes).map(cb => parseInt(cb.value));
    
    const batchDeleteBtn = document.getElementById('batchDeleteBtn');
    batchDeleteBtn.disabled = selectedRoles.length === 0;
    
    // 更新全选状态
    const allCheckboxes = document.querySelectorAll('.role-checkbox');
    const selectAll = document.getElementById('selectAll');
    selectAll.checked = allCheckboxes.length > 0 && selectedRoles.length === allCheckboxes.length;
}

// 打开新增角色模态框
function openAddRoleModal() {
    document.getElementById('modalTitle').textContent = '新增角色';
    document.getElementById('roleForm').reset();
    document.getElementById('roleId').value = '';
    document.getElementById('roleModal').classList.add('show');
}

// 编辑角色
function editRole(id) {
    axios.get(`/api/system/role/${id}`)
        .then(response => {
            if (response.data.code === 200) {
                const role = response.data.data;
                document.getElementById('modalTitle').textContent = '编辑角色';
                document.getElementById('roleId').value = role.id;
                document.getElementById('roleName').value = role.roleName;
                document.getElementById('roleCode').value = role.roleCode;
                document.getElementById('description').value = role.description || '';
                document.getElementById('status').value = role.status;
                document.getElementById('sort').value = role.sort || 0;
                document.getElementById('remark').value = role.remark || '';
                document.getElementById('roleModal').classList.add('show');
            } else {
                showMessage('获取角色信息失败：' + response.data.message, 'error');
            }
        })
        .catch(error => {
            console.error('获取角色信息失败:', error);
            showMessage('获取角色信息失败', 'error');
        });
}

// 关闭角色模态框
function closeRoleModal() {
    document.getElementById('roleModal').classList.remove('show');
}

// 保存角色
function saveRole() {
    const form = document.getElementById('roleForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const roleId = document.getElementById('roleId').value;
    const roleData = {
        roleName: document.getElementById('roleName').value,
        roleCode: document.getElementById('roleCode').value,
        description: document.getElementById('description').value,
        status: document.getElementById('status').value,
        sort: parseInt(document.getElementById('sort').value) || 0,
        remark: document.getElementById('remark').value
    };

    if (roleId) {
        roleData.id = parseInt(roleId);
    }

    const url = '/api/system/role';
    const method = roleId ? 'put' : 'post';

    axios[method](url, roleData)
        .then(response => {
            if (response.data.code === 200) {
                showMessage(roleId ? '角色更新成功' : '角色创建成功', 'success');
                closeRoleModal();
                loadRoles();
            } else {
                showMessage(response.data.message, 'error');
            }
        })
        .catch(error => {
            console.error('保存角色失败:', error);
            showMessage('保存角色失败', 'error');
        });
}

// 删除角色
function deleteRole(id) {
    if (confirm('确定要删除这个角色吗？')) {
        axios.delete(`/api/system/role/${id}`)
            .then(response => {
                if (response.data.code === 200) {
                    showMessage('角色删除成功', 'success');
                    loadRoles();
                } else {
                    showMessage('删除失败：' + response.data.message, 'error');
                }
            })
            .catch(error => {
                console.error('删除角色失败:', error);
                showMessage('删除角色失败', 'error');
            });
    }
}

// 批量删除角色
function batchDeleteRoles() {
    if (selectedRoles.length === 0) {
        showMessage('请选择要删除的角色', 'warning');
        return;
    }

    if (confirm(`确定要删除选中的 ${selectedRoles.length} 个角色吗？`)) {
        axios.delete('/api/system/role/batch', { data: selectedRoles })
            .then(response => {
                if (response.data.code === 200) {
                    showMessage('批量删除成功', 'success');
                    selectedRoles = [];
                    document.getElementById('selectAll').checked = false;
                    loadRoles();
                } else {
                    showMessage('批量删除失败：' + response.data.message, 'error');
                }
            })
            .catch(error => {
                console.error('批量删除失败:', error);
                showMessage('批量删除失败', 'error');
            });
    }
}

// 复制角色
function copyRole(id) {
    const newRoleName = prompt('请输入新角色名称:');
    if (newRoleName) {
        axios.post(`/api/system/role/${id}/copy`, null, {
            params: { newRoleName }
        })
            .then(response => {
                if (response.data.code === 200) {
                    showMessage('角色复制成功', 'success');
                    loadRoles();
                } else {
                    showMessage('角色复制失败：' + response.data.message, 'error');
                }
            })
            .catch(error => {
                console.error('角色复制失败:', error);
                showMessage('角色复制失败', 'error');
            });
    }
}

// 加载所有菜单
function loadAllMenus() {
    axios.get('/api/system/menu/tree')
        .then(response => {
            if (response.data.code === 200) {
                allMenus = response.data.data;
                menuTree = buildMenuTree(allMenus);
            }
        })
        .catch(error => {
            console.error('加载菜单列表失败:', error);
        });
}

// 构建菜单树
function buildMenuTree(menus) {
    const tree = [];
    const map = {};
    
    // 创建映射
    menus.forEach(menu => {
        map[menu.id] = { ...menu, children: [] };
    });
    
    // 构建树结构
    menus.forEach(menu => {
        if (menu.parentId === 0) {
            tree.push(map[menu.id]);
        } else if (map[menu.parentId]) {
            map[menu.parentId].children.push(map[menu.id]);
        }
    });
    
    return tree;
}

// 分配菜单
function assignMenus(roleId, roleName) {
    document.getElementById('menuRoleId').value = roleId;
    document.getElementById('menuRoleName').textContent = roleName;
    
    // 获取角色当前菜单
    axios.get(`/api/system/role/${roleId}/menus`)
        .then(response => {
            if (response.data.code === 200) {
                const roleMenuIds = response.data.data;
                renderMenuTree(roleMenuIds);
                document.getElementById('menuModal').classList.add('show');
            } else {
                showMessage('获取角色菜单失败：' + response.data.message, 'error');
            }
        })
        .catch(error => {
            console.error('获取角色菜单失败:', error);
            showMessage('获取角色菜单失败', 'error');
        });
}

// 渲染菜单树
function renderMenuTree(roleMenuIds) {
    const menuTreeContainer = document.getElementById('menuTree');
    menuTreeContainer.innerHTML = '';
    
    function renderNode(node, level = 0) {
        const div = document.createElement('div');
        div.className = `menu-node level-${level}`;
        div.style.marginLeft = `${level * 20}px`;
        
        const checked = roleMenuIds.includes(node.id) ? 'checked' : '';
        const hasChildren = node.children && node.children.length > 0;
        
        div.innerHTML = `
            <div class="flex items-center py-1">
                ${hasChildren ? `<i class="fas fa-chevron-right expand-icon mr-2 cursor-pointer" onclick="toggleNode(this)"></i>` : '<span class="w-4 mr-2"></span>'}
                <input type="checkbox" id="menu_${node.id}" value="${node.id}" ${checked} class="menu-checkbox rounded mr-2" onchange="updateMenuSelection(this)">
                <i class="${node.icon || 'fas fa-circle'} mr-2 text-gray-500"></i>
                <label for="menu_${node.id}" class="text-sm text-gray-700">${node.menuName}</label>
            </div>
        `;
        
        menuTreeContainer.appendChild(div);
        
        if (hasChildren) {
            const childrenContainer = document.createElement('div');
            childrenContainer.className = 'children-container';
            childrenContainer.style.display = 'none';
            
            node.children.forEach(child => {
                const childElement = renderNode(child, level + 1);
                childrenContainer.appendChild(childElement);
            });
            
            menuTreeContainer.appendChild(childrenContainer);
        }
        
        return div;
    }
    
    menuTree.forEach(node => renderNode(node));
}

// 切换节点展开/收起
function toggleNode(icon) {
    const isExpanded = icon.classList.contains('fa-chevron-down');
    const childrenContainer = icon.closest('.menu-node').nextElementSibling;
    
    if (isExpanded) {
        icon.classList.remove('fa-chevron-down');
        icon.classList.add('fa-chevron-right');
        childrenContainer.style.display = 'none';
    } else {
        icon.classList.remove('fa-chevron-right');
        icon.classList.add('fa-chevron-down');
        childrenContainer.style.display = 'block';
    }
}

// 更新菜单选择
function updateMenuSelection(checkbox) {
    const isChecked = checkbox.checked;
    const menuId = parseInt(checkbox.value);
    
    // 递归选中/取消选中子菜单
    function updateChildren(parentId, checked) {
        const childCheckboxes = document.querySelectorAll(`input[data-parent="${parentId}"]`);
        childCheckboxes.forEach(child => {
            child.checked = checked;
            updateChildren(parseInt(child.value), checked);
        });
    }
    
    updateChildren(menuId, isChecked);
    
    // 更新父菜单状态
    function updateParent(childId) {
        const menu = allMenus.find(m => m.id === childId);
        if (menu && menu.parentId !== 0) {
            const parentCheckbox = document.getElementById(`menu_${menu.parentId}`);
            if (parentCheckbox) {
                const siblings = allMenus.filter(m => m.parentId === menu.parentId);
                const checkedSiblings = siblings.filter(s => document.getElementById(`menu_${s.id}`).checked);
                
                if (checkedSiblings.length === siblings.length) {
                    parentCheckbox.checked = true;
                } else if (checkedSiblings.length === 0) {
                    parentCheckbox.checked = false;
                } else {
                    parentCheckbox.indeterminate = true;
                }
                
                updateParent(menu.parentId);
            }
        }
    }
    
    updateParent(menuId);
}

// 展开所有菜单
function expandAllMenus() {
    const expandIcons = document.querySelectorAll('.expand-icon');
    expandIcons.forEach(icon => {
        if (icon.classList.contains('fa-chevron-right')) {
            toggleNode(icon);
        }
    });
}

// 收起所有菜单
function collapseAllMenus() {
    const expandIcons = document.querySelectorAll('.expand-icon');
    expandIcons.forEach(icon => {
        if (icon.classList.contains('fa-chevron-down')) {
            toggleNode(icon);
        }
    });
}

// 关闭菜单模态框
function closeMenuModal() {
    document.getElementById('menuModal').classList.remove('show');
}

// 保存角色菜单
function saveRoleMenus() {
    const roleId = document.getElementById('menuRoleId').value;
    const checkboxes = document.querySelectorAll('.menu-checkbox:checked');
    const menuIds = Array.from(checkboxes).map(cb => parseInt(cb.value));
    
    axios.post(`/api/system/role/${roleId}/menus`, menuIds)
        .then(response => {
            if (response.data.code === 200) {
                showMessage('菜单分配成功', 'success');
                closeMenuModal();
            } else {
                showMessage('菜单分配失败：' + response.data.message, 'error');
            }
        })
        .catch(error => {
            console.error('菜单分配失败:', error);
            showMessage('菜单分配失败', 'error');
        });
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// 显示消息
function showMessage(message, type = 'info') {
    // 创建消息元素
    const messageDiv = document.createElement('div');
    messageDiv.className = `fixed top-4 right-4 px-4 py-2 rounded-md text-white z-50 ${
        type === 'success' ? 'bg-green-500' :
        type === 'error' ? 'bg-red-500' :
        type === 'warning' ? 'bg-yellow-500' :
        'bg-blue-500'
    }`;
    messageDiv.textContent = message;
    
    document.body.appendChild(messageDiv);
    
    // 3秒后自动移除
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.parentNode.removeChild(messageDiv);
        }
    }, 3000);
}