<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限查询测试 - CF金融催收管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .permission-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .permission-header {
            background-color: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
            font-weight: bold;
        }
        .permission-content {
            padding: 15px;
        }
        .permission-tree {
            list-style: none;
            padding-left: 0;
        }
        .permission-tree li {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #007bff;
            background-color: #f8f9fa;
        }
        .permission-tree .children {
            margin-left: 20px;
            margin-top: 10px;
        }
        .user-info {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .loading {
            text-align: center;
            padding: 50px;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .success {
            color: #155724;
            background-color: #d4edda;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="fas fa-shield-alt"></i> 权限查询测试
                </h1>
                
                <!-- 用户信息显示 -->
                <div class="user-info">
                    <h5><i class="fas fa-user"></i> 当前用户信息</h5>
                    <p><strong>用户名:</strong> <span th:text="${user != null ? user : '未登录'}">未登录</span></p>
                    <p><strong>真实姓名:</strong> <span th:text="${userInfo != null ? userInfo.realName : '未知'}">未知</span></p>
                </div>

                <!-- 操作按钮 -->
                <div class="mb-4">
                    <button type="button" class="btn btn-primary" onclick="loadCurrentUserPermissions()">
                        <i class="fas fa-search"></i> 查询当前用户权限
                    </button>
                    <button type="button" class="btn btn-success" onclick="loadAdminPermissions()">
                        <i class="fas fa-crown"></i> 查询超级管理员权限
                    </button>
                    <button type="button" class="btn btn-info" onclick="checkSpecificPermission()">
                        <i class="fas fa-check"></i> 检查特定权限
                    </button>
                </div>

                <!-- 结果显示区域 -->
                <div id="resultArea"></div>
            </div>
        </div>
    </div>

    <!-- 权限检查模态框 -->
    <div class="modal fade" id="permissionCheckModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">检查特定权限</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="permissionInput" class="form-label">权限标识:</label>
                        <input type="text" class="form-control" id="permissionInput" 
                               placeholder="例如: system:user:list" value="system:user:list">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="doCheckPermission()">检查权限</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 加载当前用户权限
        function loadCurrentUserPermissions() {
            showLoading();
            fetch('/api/permission/current')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayCurrentUserPermissions(data);
                    } else {
                        showError(data.message || '获取用户权限失败');
                    }
                })
                .catch(error => {
                    showError('请求失败: ' + error.message);
                });
        }

        // 加载超级管理员权限
        function loadAdminPermissions() {
            showLoading();
            fetch('/api/permission/admin')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayAdminPermissions(data);
                    } else {
                        showError(data.message || '获取管理员权限失败');
                    }
                })
                .catch(error => {
                    showError('请求失败: ' + error.message);
                });
        }

        // 检查特定权限
        function checkSpecificPermission() {
            const modal = new bootstrap.Modal(document.getElementById('permissionCheckModal'));
            modal.show();
        }

        // 执行权限检查
        function doCheckPermission() {
            const permission = document.getElementById('permissionInput').value.trim();
            if (!permission) {
                alert('请输入权限标识');
                return;
            }

            fetch(`/api/permission/check?permission=${encodeURIComponent(permission)}`)
                .then(response => response.json())
                .then(data => {
                    const modal = bootstrap.Modal.getInstance(document.getElementById('permissionCheckModal'));
                    modal.hide();
                    
                    if (data.success) {
                        const hasPermission = data.hasPermission;
                        const message = hasPermission ? 
                            `✅ 您拥有权限: ${permission}` : 
                            `❌ 您没有权限: ${permission}`;
                        
                        if (hasPermission) {
                            showSuccess(message);
                        } else {
                            showError(message);
                        }
                    } else {
                        showError(data.message || '权限检查失败');
                    }
                })
                .catch(error => {
                    showError('请求失败: ' + error.message);
                });
        }

        // 显示当前用户权限
        function displayCurrentUserPermissions(data) {
            let html = `
                <div class="permission-card">
                    <div class="permission-header">
                        <i class="fas fa-user"></i> 当前用户权限信息
                    </div>
                    <div class="permission-content">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>用户信息:</h6>
                                <p><strong>ID:</strong> ${data.user.id}</p>
                                <p><strong>用户名:</strong> ${data.user.username}</p>
                                <p><strong>真实姓名:</strong> ${data.user.realName || '未设置'}</p>
                                <p><strong>状态:</strong> ${data.user.status === 1 ? '启用' : '禁用'}</p>
                            </div>
                            <div class="col-md-6">
                                <h6>权限统计:</h6>
                                <p><strong>是否管理员:</strong> ${data.isAdmin ? '是' : '否'}</p>
                                <p><strong>用户权限数:</strong> ${data.userPermissions ? data.userPermissions.length : 0}</p>
                                <p><strong>用户菜单数:</strong> ${data.userMenus ? data.userMenus.length : 0}</p>
                            </div>
                        </div>
                        
                        ${data.userPermissions && data.userPermissions.length > 0 ? `
                        <h6 class="mt-3">用户权限列表:</h6>
                        <div class="permission-tree">
                            ${data.userPermissions.map(perm => `<li><code>${perm}</code></li>`).join('')}
                        </div>
                        ` : '<p class="text-muted">暂无权限</p>'}
                    </div>
                </div>
            `;
            
            document.getElementById('resultArea').innerHTML = html;
        }

        // 显示超级管理员权限
        function displayAdminPermissions(data) {
            let html = `
                <div class="permission-card">
                    <div class="permission-header">
                        <i class="fas fa-crown"></i> 超级管理员权限信息
                    </div>
                    <div class="permission-content">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>角色信息:</h6>
                                <p><strong>角色名称:</strong> ${data.adminRole.roleName}</p>
                                <p><strong>角色编码:</strong> ${data.adminRole.roleCode}</p>
                                <p><strong>角色描述:</strong> ${data.adminRole.description || '未设置'}</p>
                            </div>
                            <div class="col-md-6">
                                <h6>权限统计:</h6>
                                <p><strong>总权限数:</strong> ${data.totalPermissionCount}</p>
                                <p><strong>菜单数:</strong> ${data.allPermissions ? data.allPermissions.length : 0}</p>
                            </div>
                        </div>
                        
                        ${data.allPermissions && data.allPermissions.length > 0 ? `
                        <h6 class="mt-3">所有权限菜单:</h6>
                        <div class="permission-tree">
                            ${buildPermissionTree(data.allPermissions)}
                        </div>
                        ` : '<p class="text-muted">暂无权限</p>'}
                    </div>
                </div>
            `;
            
            document.getElementById('resultArea').innerHTML = html;
        }

        // 构建权限树
        function buildPermissionTree(permissions) {
            return permissions.map(perm => {
                let html = `
                    <li>
                        <strong>${perm.name}</strong> 
                        ${perm.permission ? `<code>${perm.permission}</code>` : ''}
                        <small class="text-muted">(${perm.type === 1 ? '目录' : perm.type === 2 ? '菜单' : '按钮'})</small>
                `;
                
                if (perm.children && perm.children.length > 0) {
                    html += `<div class="children">${buildPermissionTree(perm.children)}</div>`;
                }
                
                html += '</li>';
                return html;
            }).join('');
        }

        // 显示加载状态
        function showLoading() {
            document.getElementById('resultArea').innerHTML = `
                <div class="loading">
                    <i class="fas fa-spinner fa-spin fa-2x"></i>
                    <p class="mt-2">加载中...</p>
                </div>
            `;
        }

        // 显示错误信息
        function showError(message) {
            document.getElementById('resultArea').innerHTML = `
                <div class="error">
                    <i class="fas fa-exclamation-triangle"></i> ${message}
                </div>
            `;
        }

        // 显示成功信息
        function showSuccess(message) {
            document.getElementById('resultArea').innerHTML = `
                <div class="success">
                    <i class="fas fa-check-circle"></i> ${message}
                </div>
            `;
        }

        // 页面加载完成后自动加载当前用户权限
        document.addEventListener('DOMContentLoaded', function() {
            loadCurrentUserPermissions();
        });
    </script>
</body>
</html>
