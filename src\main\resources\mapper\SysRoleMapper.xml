<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cf.financing.mapper.SysRoleMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.cf.financing.entity.SysRole">
        <id column="id" property="id" />
        <result column="role_code" property="roleCode" />
        <result column="role_name" property="roleName" />
        <result column="description" property="description" />
        <result column="status" property="status" />
        <result column="sort_order" property="sortOrder" />
        <result column="data_scope" property="dataScope" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 扩展结果映射（包含创建人和更新人信息） -->
    <resultMap id="ExtendedResultMap" type="com.cf.financing.entity.SysRole" extends="BaseResultMap">
        <result column="create_by_name" property="createByName" />
        <result column="update_by_name" property="updateByName" />
    </resultMap>

    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        id, role_code, role_name, description, status, sort_order, data_scope,
        create_by, create_time, update_by, update_time, deleted, remark
    </sql>

    <!-- 分页查询角色列表 -->
    <select id="selectRolePage" resultMap="ExtendedResultMap">
        SELECT 
            r.id, r.role_code, r.role_name, r.description, r.status, r.sort_order, r.data_scope,
            r.create_by, r.create_time, r.update_by, r.update_time, r.remark,
            cu.real_name as create_by_name,
            uu.real_name as update_by_name
        FROM sys_role r
        LEFT JOIN sys_user cu ON r.create_by = cu.id
        LEFT JOIN sys_user uu ON r.update_by = uu.id
        <where>
            r.deleted = 0
            <if test="roleName != null and roleName != ''">
                AND r.role_name LIKE CONCAT('%', #{roleName}, '%')
            </if>
            <if test="roleCode != null and roleCode != ''">
                AND r.role_code LIKE CONCAT('%', #{roleCode}, '%')
            </if>
            <if test="status != null">
                AND r.status = #{status}
            </if>
        </where>
        ORDER BY r.sort_order, r.create_time DESC
    </select>

    <!-- 根据用户ID查询角色列表 -->
    <select id="selectRolesByUserId" resultMap="BaseResultMap">
        SELECT 
            r.id, r.role_code, r.role_name, r.description, r.status, r.sort_order, r.data_scope,
            r.create_by, r.create_time, r.update_by, r.update_time, r.remark
        FROM sys_role r
        INNER JOIN sys_user_role ur ON r.id = ur.role_id
        WHERE ur.user_id = #{userId} AND r.status = 1 AND r.deleted = 0
        ORDER BY r.sort_order
    </select>

    <!-- 查询所有启用的角色 -->
    <select id="selectEnabledRoles" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_role
        WHERE status = 1 AND deleted = 0
        ORDER BY sort_order
    </select>

    <!-- 根据角色编码查询角色 -->
    <select id="selectByRoleCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_role
        WHERE role_code = #{roleCode} AND deleted = 0
    </select>

    <!-- 检查角色编码是否存在 -->
    <select id="checkRoleCodeExists" resultType="int">
        SELECT COUNT(*) FROM sys_role 
        WHERE role_code = #{roleCode} 
        AND deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 检查角色名称是否存在 -->
    <select id="checkRoleNameExists" resultType="int">
        SELECT COUNT(*) FROM sys_role 
        WHERE role_name = #{roleName} 
        AND deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 根据角色ID列表查询角色 -->
    <select id="selectRolesByIds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_role
        WHERE id IN
        <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
        AND deleted = 0
        ORDER BY sort_order
    </select>

    <!-- 查询角色的菜单权限ID列表 -->
    <select id="selectMenuIdsByRoleId" resultType="java.lang.Long">
        SELECT menu_id FROM sys_role_menu WHERE role_id = #{roleId}
    </select>

    <!-- 删除角色的菜单权限 -->
    <delete id="deleteRoleMenuByRoleId">
        DELETE FROM sys_role_menu WHERE role_id = #{roleId}
    </delete>

    <!-- 统计角色下的用户数量 -->
    <select id="countUsersByRoleId" resultType="int">
        SELECT COUNT(*) FROM sys_user_role WHERE role_id = #{roleId}
    </select>

    <!-- 查询角色统计信息 -->
    <select id="selectRoleStatistics" resultType="java.util.Map">
        SELECT 
            COUNT(*) as total_roles,
            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as enabled_roles,
            SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as disabled_roles
        FROM sys_role 
        WHERE deleted = 0
    </select>

    <!-- 导出角色数据 -->
    <select id="exportRoles" resultMap="ExtendedResultMap">
        SELECT 
            r.id, r.role_code, r.role_name, r.description, r.status, r.sort_order, r.data_scope,
            r.create_by, r.create_time, r.update_by, r.update_time, r.remark,
            cu.real_name as create_by_name,
            uu.real_name as update_by_name
        FROM sys_role r
        LEFT JOIN sys_user cu ON r.create_by = cu.id
        LEFT JOIN sys_user uu ON r.update_by = uu.id
        <where>
            r.deleted = 0
            <if test="roleName != null and roleName != ''">
                AND r.role_name LIKE CONCAT('%', #{roleName}, '%')
            </if>
            <if test="roleCode != null and roleCode != ''">
                AND r.role_code LIKE CONCAT('%', #{roleCode}, '%')
            </if>
            <if test="status != null">
                AND r.status = #{status}
            </if>
        </where>
        ORDER BY r.sort_order, r.create_time DESC
    </select>

</mapper>
