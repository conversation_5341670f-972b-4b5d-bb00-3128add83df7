package com.cf.financing.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cf.financing.entity.CasePool;
import com.cf.financing.service.ICasePoolService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 案池管理控制器
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/api/case-pool")
public class CasePoolController {

    @Autowired
    private ICasePoolService casePoolService;

    /**
     * 分页查询案池数据
     */
    @GetMapping("/page")
    public Map<String, Object> getCasePoolPage(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "20") Long size,
            @RequestParam(required = false) String clientName,
            @RequestParam(required = false) String clientId,
            @RequestParam(required = false) String city,
            @RequestParam(required = false) String configStatus,
            @RequestParam(required = false) String caseType,
            @RequestParam(required = false) String batchNumber,
            @RequestParam(required = false) String cardholderCode,
            @RequestParam(required = false) String specialType,
            @RequestParam(required = false) String repairResult,
            @RequestParam(required = false) String startDateBegin,
            @RequestParam(required = false) String startDateEnd,
            @RequestParam(required = false) String endDateBegin,
            @RequestParam(required = false) String endDateEnd) {
        
        Map<String, Object> params = new HashMap<>();
        params.put("clientName", clientName);
        params.put("clientId", clientId);
        params.put("city", city);
        params.put("configStatus", configStatus);
        params.put("caseType", caseType);
        params.put("batchNumber", batchNumber);
        params.put("cardholderCode", cardholderCode);
        params.put("specialType", specialType);
        params.put("repairResult", repairResult);
        params.put("startDateBegin", startDateBegin);
        params.put("startDateEnd", startDateEnd);
        params.put("endDateBegin", endDateBegin);
        params.put("endDateEnd", endDateEnd);

        IPage<CasePool> page = casePoolService.getCasePoolPage(current, size, params);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("data", page.getRecords());
        result.put("total", page.getTotal());
        result.put("current", page.getCurrent());
        result.put("size", page.getSize());
        result.put("pages", page.getPages());
        
        return result;
    }

    /**
     * 获取案池统计信息
     */
    @GetMapping("/statistics")
    public Map<String, Object> getCasePoolStatistics(
            @RequestParam(required = false) String clientName,
            @RequestParam(required = false) String configStatus) {
        
        Map<String, Object> params = new HashMap<>();
        params.put("clientName", clientName);
        params.put("configStatus", configStatus);
        
        Map<String, Object> statistics = casePoolService.getCasePoolStatistics(params);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("data", statistics);
        
        return result;
    }

    /**
     * 批量锁定案件
     */
    @PostMapping("/batch-lock")
    public Map<String, Object> batchLockCases(@RequestBody List<String> clientIds) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean success = casePoolService.batchLockCases(clientIds);
            result.put("success", success);
            result.put("message", success ? "锁定成功" : "锁定失败");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "锁定失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 批量解锁案件
     */
    @PostMapping("/batch-unlock")
    public Map<String, Object> batchUnlockCases(@RequestBody List<String> clientIds) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean success = casePoolService.batchUnlockCases(clientIds);
            result.put("success", success);
            result.put("message", success ? "解锁成功" : "解锁失败");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "解锁失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 批量分派案件
     */
    @PostMapping("/batch-assign")
    public Map<String, Object> batchAssignCases(
            @RequestBody Map<String, Object> requestData) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            @SuppressWarnings("unchecked")
            List<String> clientIds = (List<String>) requestData.get("clientIds");
            Long assignUserId = Long.valueOf(requestData.get("assignUserId").toString());
            
            boolean success = casePoolService.batchAssignCases(clientIds, assignUserId);
            result.put("success", success);
            result.put("message", success ? "分派成功" : "分派失败");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "分派失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 根据客户索引号查询案件详情
     */
    @GetMapping("/detail/{clientId}")
    public Map<String, Object> getCaseDetail(@PathVariable String clientId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Map<String, Object> detailInfo = casePoolService.getCaseDetailInfo(clientId);
            result.put("success", true);
            result.put("data", detailInfo);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 导出案池数据
     */
    @GetMapping("/export")
    public Map<String, Object> exportCasePoolData(
            @RequestParam(required = false) String clientName,
            @RequestParam(required = false) String configStatus) {
        
        Map<String, Object> params = new HashMap<>();
        params.put("clientName", clientName);
        params.put("configStatus", configStatus);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<CasePool> exportData = casePoolService.exportCasePoolData(params);
            result.put("success", true);
            result.put("data", exportData);
            result.put("message", "导出成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "导出失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取图表数据
     */
    @GetMapping("/chart-data")
    public Map<String, Object> getCasePoolChartData() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Map<String, Object> chartData = casePoolService.getCasePoolChartData();
            result.put("success", true);
            result.put("data", chartData);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取图表数据失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 更新跟进状态
     */
    @PostMapping("/update-followup")
    public Map<String, Object> updateFollowupStatus(
            @RequestBody Map<String, Object> requestData) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            String clientId = requestData.get("clientId").toString();
            String followupResult = requestData.get("followupResult").toString();
            
            boolean success = casePoolService.updateFollowupStatus(clientId, followupResult);
            result.put("success", success);
            result.put("message", success ? "更新成功" : "更新失败");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "更新失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 新增案池数据
     */
    @PostMapping("/add")
    public Map<String, Object> addCasePool(@RequestBody CasePool casePool) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean success = casePoolService.save(casePool);
            result.put("success", success);
            result.put("message", success ? "新增成功" : "新增失败");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "新增失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 更新案池数据
     */
    @PutMapping("/update")
    public Map<String, Object> updateCasePool(@RequestBody CasePool casePool) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean success = casePoolService.updateById(casePool);
            result.put("success", success);
            result.put("message", success ? "更新成功" : "更新失败");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "更新失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 删除案池数据
     */
    @DeleteMapping("/delete/{id}")
    public Map<String, Object> deleteCasePool(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean success = casePoolService.removeById(id);
            result.put("success", success);
            result.put("message", success ? "删除成功" : "删除失败");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "删除失败：" + e.getMessage());
        }
        
        return result;
    }
}
