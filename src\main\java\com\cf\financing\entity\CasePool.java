package com.cf.financing.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 案池管理实体类
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("case_pool")
public class CasePool implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 案池ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 客户姓名
     */
    @TableField("client_name")
    private String clientName;

    /**
     * 客户索引号
     */
    @TableField("client_id")
    private String clientId;

    /**
     * 持卡人代码
     */
    @TableField("cardholder_code")
    private String cardholderCode;

    /**
     * 委托金额
     */
    @TableField("entrusted_amount")
    private BigDecimal entrustedAmount;

    /**
     * 委托本金
     */
    @TableField("entrusted_principal")
    private BigDecimal entrustedPrincipal;

    /**
     * 批次号
     */
    @TableField("batch_number")
    private String batchNumber;

    /**
     * 入案池次数
     */
    @TableField("pool_count")
    private Integer poolCount;

    /**
     * 未跟进天数
     */
    @TableField("unfollowed_days")
    private Integer unfollowedDays;

    /**
     * 配置状态(已锁定/未锁定)
     */
    @TableField("config_status")
    private String configStatus;

    /**
     * 余额OPS
     */
    @TableField("balance_ops")
    private BigDecimal balanceOps;

    /**
     * 本金OPS
     */
    @TableField("principal_ops")
    private BigDecimal principalOps;

    /**
     * 案件类型
     */
    @TableField("case_type")
    private String caseType;

    /**
     * 专项类型
     */
    @TableField("special_type")
    private String specialType;

    /**
     * 失联修复结果
     */
    @TableField("repair_result")
    private String repairResult;

    /**
     * 委托开始日
     */
    @TableField("entrust_start_date")
    private LocalDate entrustStartDate;

    /**
     * 委托结束日
     */
    @TableField("entrust_end_date")
    private LocalDate entrustEndDate;

    /**
     * 保留次数
     */
    @TableField("retention_count")
    private Integer retentionCount;

    /**
     * 开卡日期
     */
    @TableField("card_opening_date")
    private LocalDate cardOpeningDate;

    /**
     * 信用额度
     */
    @TableField("credit_limit")
    private BigDecimal creditLimit;

    /**
     * 最后缴款日期
     */
    @TableField("last_payment_date")
    private LocalDate lastPaymentDate;

    /**
     * 委托时逾期时段
     */
    @TableField("overdue_period_at_entrust")
    private String overduePeriodAtEntrust;

    /**
     * 目标时段
     */
    @TableField("target_period")
    private String targetPeriod;

    /**
     * 分案城市
     */
    @TableField("city")
    private String city;

    /**
     * 最后跟进日期
     */
    @TableField("last_followup_date")
    private LocalDate lastFollowupDate;

    /**
     * 新旧案标志
     */
    @TableField("case_flag")
    private String caseFlag;

    /**
     * 当前逾期时段
     */
    @TableField("current_overdue_period")
    private String currentOverduePeriod;

    /**
     * 户籍城市
     */
    @TableField("residence_city")
    private String residenceCity;

    /**
     * 客户年龄
     */
    @TableField("client_age")
    private Integer clientAge;

    /**
     * 职业
     */
    @TableField("occupation")
    private String occupation;

    /**
     * 账户号后7位
     */
    @TableField("account_last_7")
    private String accountLast7;

    /**
     * 委托时月龄分档
     */
    @TableField("month_range_at_entrust")
    private String monthRangeAtEntrust;

    /**
     * 委托时金额段
     */
    @TableField("amount_range_at_entrust")
    private String amountRangeAtEntrust;

    /**
     * 委托时佣金分档
     */
    @TableField("commission_range_at_entrust")
    private String commissionRangeAtEntrust;

    /**
     * 评分档
     */
    @TableField("rating")
    private String rating;

    /**
     * 是否诉讼(含风险代理)
     */
    @TableField("is_litigation")
    private String isLitigation;

    /**
     * 客户当月主动还款金额
     */
    @TableField("current_month_payment")
    private BigDecimal currentMonthPayment;

    /**
     * 客户前日主动还款金额
     */
    @TableField("previous_day_payment")
    private BigDecimal previousDayPayment;

    /**
     * 个性化分期状态
     */
    @TableField("installment_status")
    private String installmentStatus;

    /**
     * 个性化分期履约状态
     */
    @TableField("installment_performance")
    private String installmentPerformance;

    /**
     * 投诉标签
     */
    @TableField("complaint_tag")
    private String complaintTag;

    /**
     * 个分履约标签
     */
    @TableField("installment_performance_tag")
    private String installmentPerformanceTag;

    /**
     * 诉讼标签
     */
    @TableField("litigation_tag")
    private String litigationTag;

    /**
     * 智能语音标签
     */
    @TableField("voice_tag")
    private String voiceTag;

    /**
     * 专项标签
     */
    @TableField("special_tag")
    private String specialTag;

    /**
     * 是否诉讼(结佣)
     */
    @TableField("is_litigation_commission")
    private String isLitigationCommission;

    /**
     * 委托机构
     */
    @TableField("institution")
    private String institution;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标记(0:正常,1:删除)
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}
