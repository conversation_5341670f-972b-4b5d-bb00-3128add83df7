<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cf.financing.mapper.RepaymentRecordMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.cf.financing.entity.RepaymentRecord">
        <id column="id" property="id" />
        <result column="client_id" property="clientId" />
        <result column="client_name" property="clientName" />
        <result column="id_card" property="idCard" />
        <result column="phone" property="phone" />
        <result column="repayment_no" property="repaymentNo" />
        <result column="entrusted_amount" property="entrustedAmount" />
        <result column="overdue_amount" property="overdueAmount" />
        <result column="repayment_amount" property="repaymentAmount" />
        <result column="principal_repayment" property="principalRepayment" />
        <result column="interest_repayment" property="interestRepayment" />
        <result column="fee_repayment" property="feeRepayment" />
        <result column="repayment_date" property="repaymentDate" />
        <result column="repayment_time" property="repaymentTime" />
        <result column="repayment_type" property="repaymentType" />
        <result column="repayment_channel" property="repaymentChannel" />
        <result column="repayment_status" property="repaymentStatus" />
        <result column="transaction_no" property="transactionNo" />
        <result column="bank_serial_no" property="bankSerialNo" />
        <result column="receive_account" property="receiveAccount" />
        <result column="receive_bank" property="receiveBank" />
        <result column="pay_account" property="payAccount" />
        <result column="pay_bank" property="payBank" />
        <result column="payment_type" property="paymentType" />
        <result column="is_overdue" property="isOverdue" />
        <result column="overdue_days" property="overdueDays" />
        <result column="reduction_amount" property="reductionAmount" />
        <result column="actual_amount" property="actualAmount" />
        <result column="handling_fee" property="handlingFee" />
        <result column="remark" property="remark" />
        <result column="operator_id" property="operatorId" />
        <result column="audit_status" property="auditStatus" />
        <result column="auditor_id" property="auditorId" />
        <result column="audit_time" property="auditTime" />
        <result column="audit_remark" property="auditRemark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 扩展结果映射（包含案件和客户信息） -->
    <resultMap id="ExtendedResultMap" type="com.cf.financing.entity.RepaymentRecord" extends="BaseResultMap">
        <result column="case_no" property="caseNo" />
        <result column="customer_name" property="customerName" />
        <result column="operator_name" property="operatorName" />
    </resultMap>

    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        id, case_id, repayment_no, repayment_amount, repayment_date, repayment_type, 
        repayment_status, transaction_no, remark, operator_id, create_time, update_time
    </sql>

    <!-- 分页查询还款记录（新版本） -->
    <select id="selectRepaymentPage" resultMap="BaseResultMap">
        SELECT * FROM repayment_record
        WHERE deleted = 0
        <if test="params.clientName != null and params.clientName != ''">
            AND client_name LIKE CONCAT('%', #{params.clientName}, '%')
        </if>
        <if test="params.clientId != null and params.clientId != ''">
            AND client_id LIKE CONCAT('%', #{params.clientId}, '%')
        </if>
        <if test="params.repaymentNo != null and params.repaymentNo != ''">
            AND repayment_no LIKE CONCAT('%', #{params.repaymentNo}, '%')
        </if>
        <if test="params.repaymentStatus != null and params.repaymentStatus != ''">
            AND repayment_status = #{params.repaymentStatus}
        </if>
        <if test="params.repaymentType != null and params.repaymentType != ''">
            AND repayment_type = #{params.repaymentType}
        </if>
        <if test="params.auditStatus != null and params.auditStatus != ''">
            AND audit_status = #{params.auditStatus}
        </if>
        <if test="params.paymentType != null and params.paymentType != ''">
            AND payment_type = #{params.paymentType}
        </if>
        <if test="params.operatorId != null">
            AND operator_id = #{params.operatorId}
        </if>
        <if test="params.startDate != null">
            AND repayment_date >= #{params.startDate}
        </if>
        <if test="params.endDate != null">
            AND repayment_date &lt;= #{params.endDate}
        </if>
        <if test="params.minAmount != null">
            AND repayment_amount >= #{params.minAmount}
        </if>
        <if test="params.maxAmount != null">
            AND repayment_amount &lt;= #{params.maxAmount}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 根据案件ID查询还款记录 -->
    <select id="selectRepaymentsByCaseId" resultMap="ExtendedResultMap">
        SELECT 
            r.id, r.case_id, r.repayment_no, r.repayment_amount, r.repayment_date, r.repayment_type,
            r.repayment_status, r.transaction_no, r.remark, r.operator_id, r.create_time, r.update_time,
            c.case_no, cu.customer_name, u.real_name as operator_name
        FROM repayment_record r
        LEFT JOIN case_info c ON r.case_id = c.id
        LEFT JOIN customer_info cu ON c.customer_id = cu.id
        LEFT JOIN sys_user u ON r.operator_id = u.id
        WHERE r.case_id = #{caseId}
        ORDER BY r.create_time DESC
    </select>

    <!-- 根据客户ID查询还款记录 -->
    <select id="selectRepaymentsByCustomerId" resultMap="ExtendedResultMap">
        SELECT 
            r.id, r.case_id, r.repayment_no, r.repayment_amount, r.repayment_date, r.repayment_type,
            r.repayment_status, r.transaction_no, r.remark, r.operator_id, r.create_time, r.update_time,
            c.case_no, cu.customer_name, u.real_name as operator_name
        FROM repayment_record r
        INNER JOIN case_info c ON r.case_id = c.id
        LEFT JOIN customer_info cu ON c.customer_id = cu.id
        LEFT JOIN sys_user u ON r.operator_id = u.id
        WHERE c.customer_id = #{customerId}
        ORDER BY r.create_time DESC
    </select>

    <!-- 根据还款编号查询还款记录 -->
    <select id="selectRepaymentByNo" resultMap="ExtendedResultMap">
        SELECT 
            r.id, r.case_id, r.repayment_no, r.repayment_amount, r.repayment_date, r.repayment_type,
            r.repayment_status, r.transaction_no, r.remark, r.operator_id, r.create_time, r.update_time,
            c.case_no, cu.customer_name, u.real_name as operator_name
        FROM repayment_record r
        LEFT JOIN case_info c ON r.case_id = c.id
        LEFT JOIN customer_info cu ON c.customer_id = cu.id
        LEFT JOIN sys_user u ON r.operator_id = u.id
        WHERE r.repayment_no = #{repaymentNo}
    </select>

    <!-- 根据交易流水号查询还款记录 -->
    <select id="selectRepaymentByTransactionNo" resultMap="ExtendedResultMap">
        SELECT 
            r.id, r.case_id, r.repayment_no, r.repayment_amount, r.repayment_date, r.repayment_type,
            r.repayment_status, r.transaction_no, r.remark, r.operator_id, r.create_time, r.update_time,
            c.case_no, cu.customer_name, u.real_name as operator_name
        FROM repayment_record r
        LEFT JOIN case_info c ON r.case_id = c.id
        LEFT JOIN customer_info cu ON c.customer_id = cu.id
        LEFT JOIN sys_user u ON r.operator_id = u.id
        WHERE r.transaction_no = #{transactionNo}
    </select>

    <!-- 获取案件的总还款金额 -->
    <select id="selectTotalRepaymentByCaseId" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(repayment_amount), 0)
        FROM repayment_record
        WHERE case_id = #{caseId} AND repayment_status = 'SUCCESS'
    </select>

    <!-- 获取客户的总还款金额 -->
    <select id="selectTotalRepaymentByCustomerId" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(r.repayment_amount), 0)
        FROM repayment_record r
        INNER JOIN case_info c ON r.case_id = c.id
        WHERE c.customer_id = #{customerId} AND r.repayment_status = 'SUCCESS'
    </select>

    <!-- 获取还款统计信息 -->
    <select id="selectRepaymentStatistics" resultType="map">
        SELECT 
            COUNT(*) as totalRecords,
            COUNT(CASE WHEN repayment_status = 'SUCCESS' THEN 1 END) as successRecords,
            COUNT(CASE WHEN repayment_status = 'PENDING' THEN 1 END) as pendingRecords,
            COUNT(CASE WHEN repayment_status = 'FAILED' THEN 1 END) as failedRecords,
            COALESCE(SUM(CASE WHEN repayment_status = 'SUCCESS' THEN repayment_amount ELSE 0 END), 0) as totalAmount,
            COALESCE(AVG(CASE WHEN repayment_status = 'SUCCESS' THEN repayment_amount ELSE NULL END), 0) as avgAmount,
            COUNT(CASE WHEN DATE(repayment_date) = CURDATE() AND repayment_status = 'SUCCESS' THEN 1 END) as todayRecords,
            COALESCE(SUM(CASE WHEN DATE(repayment_date) = CURDATE() AND repayment_status = 'SUCCESS' THEN repayment_amount ELSE 0 END), 0) as todayAmount
        FROM repayment_record
        <where>
            <if test="operatorId != null">
                AND operator_id = #{operatorId}
            </if>
            <if test="startDate != null">
                AND DATE(repayment_date) &gt;= #{startDate}
            </if>
            <if test="endDate != null">
                AND DATE(repayment_date) &lt;= #{endDate}
            </if>
        </where>
    </select>

    <!-- 获取还款趋势数据 -->
    <select id="selectRepaymentTrendData" resultType="map">
        SELECT 
            DATE(repayment_date) as repaymentDate,
            COUNT(CASE WHEN repayment_status = 'SUCCESS' THEN 1 END) as recordCount,
            COALESCE(SUM(CASE WHEN repayment_status = 'SUCCESS' THEN repayment_amount ELSE 0 END), 0) as totalAmount
        FROM repayment_record
        WHERE repayment_date &gt;= DATE_SUB(CURDATE(), INTERVAL #{days} DAY)
        <if test="operatorId != null">
            AND operator_id = #{operatorId}
        </if>
        GROUP BY DATE(repayment_date)
        ORDER BY repaymentDate
    </select>

    <!-- 获取还款方式统计 -->
    <select id="selectRepaymentTypeStats" resultType="map">
        SELECT 
            repayment_type as repaymentType,
            COUNT(*) as recordCount,
            COALESCE(SUM(CASE WHEN repayment_status = 'SUCCESS' THEN repayment_amount ELSE 0 END), 0) as totalAmount,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM repayment_record 
                WHERE repayment_status = 'SUCCESS'
                <if test="startDate != null">
                    AND DATE(repayment_date) &gt;= #{startDate}
                </if>
                <if test="endDate != null">
                    AND DATE(repayment_date) &lt;= #{endDate}
                </if>
            ), 2) as percentage
        FROM repayment_record
        WHERE repayment_status = 'SUCCESS'
        <if test="startDate != null">
            AND DATE(repayment_date) &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND DATE(repayment_date) &lt;= #{endDate}
        </if>
        GROUP BY repayment_type
        ORDER BY totalAmount DESC
    </select>

    <!-- 获取今日还款记录 -->
    <select id="selectTodayRepayments" resultMap="ExtendedResultMap">
        SELECT 
            r.id, r.case_id, r.repayment_no, r.repayment_amount, r.repayment_date, r.repayment_type,
            r.repayment_status, r.transaction_no, r.remark, r.operator_id, r.create_time, r.update_time,
            c.case_no, cu.customer_name, u.real_name as operator_name
        FROM repayment_record r
        LEFT JOIN case_info c ON r.case_id = c.id
        LEFT JOIN customer_info cu ON c.customer_id = cu.id
        LEFT JOIN sys_user u ON r.operator_id = u.id
        WHERE DATE(r.repayment_date) = #{repaymentDate}
        <if test="operatorId != null">
            AND r.operator_id = #{operatorId}
        </if>
        ORDER BY r.create_time DESC
    </select>

    <!-- 获取待确认的还款记录 -->
    <select id="selectPendingRepayments" resultMap="ExtendedResultMap">
        SELECT 
            r.id, r.case_id, r.repayment_no, r.repayment_amount, r.repayment_date, r.repayment_type,
            r.repayment_status, r.transaction_no, r.remark, r.operator_id, r.create_time, r.update_time,
            c.case_no, cu.customer_name, u.real_name as operator_name
        FROM repayment_record r
        LEFT JOIN case_info c ON r.case_id = c.id
        LEFT JOIN customer_info cu ON c.customer_id = cu.id
        LEFT JOIN sys_user u ON r.operator_id = u.id
        WHERE r.repayment_status = 'PENDING'
        <if test="operatorId != null">
            AND r.operator_id = #{operatorId}
        </if>
        ORDER BY r.create_time ASC
    </select>

    <!-- 批量更新还款状态 -->
    <update id="batchUpdateRepaymentStatus">
        UPDATE repayment_record 
        SET repayment_status = #{repaymentStatus},
            update_time = NOW()
        WHERE id IN
        <foreach collection="repaymentIds" item="repaymentId" open="(" separator="," close=")">
            #{repaymentId}
        </foreach>
        <if test="operatorId != null">
            AND operator_id = #{operatorId}
        </if>
    </update>

    <!-- 检查还款编号是否存在 -->
    <select id="checkRepaymentNoExists" resultType="int">
        SELECT COUNT(*) 
        FROM repayment_record 
        WHERE repayment_no = #{repaymentNo}
        <if test="excludeRepaymentId != null">
            AND id != #{excludeRepaymentId}
        </if>
    </select>

    <!-- 检查交易流水号是否存在 -->
    <select id="checkTransactionNoExists" resultType="int">
        SELECT COUNT(*) 
        FROM repayment_record 
        WHERE transaction_no = #{transactionNo}
        <if test="excludeRepaymentId != null">
            AND id != #{excludeRepaymentId}
        </if>
    </select>

    <!-- 获取月度还款统计 -->
    <select id="selectMonthlyRepaymentStats" resultType="map">
        SELECT 
            DATE_FORMAT(repayment_date, '%Y-%m') as month,
            COUNT(CASE WHEN repayment_status = 'SUCCESS' THEN 1 END) as recordCount,
            COALESCE(SUM(CASE WHEN repayment_status = 'SUCCESS' THEN repayment_amount ELSE 0 END), 0) as totalAmount
        FROM repayment_record
        WHERE repayment_date &gt;= DATE_SUB(NOW(), INTERVAL #{months} MONTH)
        GROUP BY DATE_FORMAT(repayment_date, '%Y-%m')
        ORDER BY month
    </select>

    <!-- 获取操作员还款业绩排行 -->
    <select id="selectOperatorRepaymentRanking" resultType="map">
        SELECT
            u.real_name as operatorName,
            COUNT(CASE WHEN r.repayment_status = 'SUCCESS' THEN 1 END) as recordCount,
            COALESCE(SUM(CASE WHEN r.repayment_status = 'SUCCESS' THEN r.repayment_amount ELSE 0 END), 0) as totalAmount
        FROM repayment_record r
        INNER JOIN sys_user u ON r.operator_id = u.id
        WHERE DATE(r.repayment_date) &gt;= #{startDate}
        AND DATE(r.repayment_date) &lt;= #{endDate}
        GROUP BY r.operator_id, u.real_name
        ORDER BY totalAmount DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 统计还款总数（新版本） -->
    <select id="countRepayment" resultType="java.lang.Long">
        SELECT COUNT(*) FROM repayment_record
        WHERE deleted = 0
        <if test="params.clientName != null and params.clientName != ''">
            AND client_name LIKE CONCAT('%', #{params.clientName}, '%')
        </if>
        <if test="params.repaymentStatus != null and params.repaymentStatus != ''">
            AND repayment_status = #{params.repaymentStatus}
        </if>
    </select>

    <!-- 统计还款总金额（新版本） -->
    <select id="sumRepaymentAmount" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(repayment_amount), 0) FROM repayment_record
        WHERE deleted = 0
        <if test="params.clientName != null and params.clientName != ''">
            AND client_name LIKE CONCAT('%', #{params.clientName}, '%')
        </if>
        <if test="params.repaymentStatus != null and params.repaymentStatus != ''">
            AND repayment_status = #{params.repaymentStatus}
        </if>
    </select>

    <!-- 根据客户索引号查询还款记录 -->
    <select id="selectByClientId" resultMap="BaseResultMap">
        SELECT * FROM repayment_record
        WHERE client_id = #{clientId} AND deleted = 0
        ORDER BY repayment_date DESC
    </select>

    <!-- 批量审核还款记录 -->
    <update id="batchAuditRepayment">
        UPDATE repayment_record SET
            audit_status = #{auditStatus},
            auditor_id = #{auditorId},
            audit_time = NOW(),
            audit_remark = #{auditRemark},
            update_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted = 0
    </update>

</mapper>