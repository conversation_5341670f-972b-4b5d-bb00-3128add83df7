package com.cf.financing.controller;

import com.cf.financing.entity.SysUser;
import com.cf.financing.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;

import javax.servlet.http.HttpSession;
import java.util.Random;

/**
 * Web控制器 - 处理页面路由
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Controller
public class WebController {

    @Autowired
    private ISysUserService userService;

    /**
     * 首页重定向到登录页
     */
    @GetMapping("/")
    public String index() {
        return "redirect:/login";
    }

    /**
     * 登录页面
     */
    @GetMapping("/login")
    public String loginPage(Model model, HttpSession session) {
        // 生成验证码
        String captcha = generateCaptcha();
        session.setAttribute("captcha", captcha);
        model.addAttribute("captcha", captcha);
        return "login";
    }

    // 移除自定义登录处理，使用Spring Security默认处理

    /**
     * 首页仪表板
     */
    @GetMapping("/dashboard")
    public String dashboard(Model model) {
        // 获取当前登录用户信息
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated()) {
            String username = authentication.getName();
            if (!"anonymousUser".equals(username)) {
                SysUser user = userService.getUserByUsername(username);
                if (user != null) {
                    // 将用户真实姓名传递给前端
                    model.addAttribute("user", user.getRealName() != null ? user.getRealName() : user.getUsername());
                    model.addAttribute("userInfo", user);
                } else {
                    model.addAttribute("user", username);
                }
            }
        }
        return "dashboard";
    }

    /**
     * 主页面
     */
    @GetMapping("/main")
    public String main(Model model) {
        // 获取当前登录用户信息
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated()) {
            String username = authentication.getName();
            if (!"anonymousUser".equals(username)) {
                SysUser user = userService.getUserByUsername(username);
                if (user != null) {
                    model.addAttribute("user", user.getRealName() != null ? user.getRealName() : user.getUsername());
                    model.addAttribute("userInfo", user);
                } else {
                    model.addAttribute("user", username);
                }
            }
        }
        return "main";
    }

    /**
     * 首页内容页面（用于iframe嵌入）
     */
    @GetMapping("/home")
    public String home(Model model) {
        return "home";
    }

    /**
     * 案池页面
     */
    @GetMapping("/case-pool")
    public String casePool(Model model) {
        return "case-pool";
    }



    /**
     * 作业清单页面
     */
    @GetMapping("/tasklist")
    public String tasklist(Model model) {
        return "tasklist";
    }



    /**
     * 换单查询页面
     */
    @GetMapping("/exchange")
    public String exchange(Model model) {
        return "exchange";
    }

    /**
     * 新增统计页面
     */
    @GetMapping("/statistics")
    public String statistics(Model model) {
        return "statistics";
    }

    /**
     * 用户管理页面
     */
    @GetMapping("/system/user")
    public String userManagement(Model model) {
        return "system/user-management";
    }

    /**
     * 角色管理页面
     */
    @GetMapping("/system/role")
    public String roleManagement(Model model) {
        return "system/role-management";
    }

    /**
     * 菜单管理页面
     */
    @GetMapping("/system/menu")
    public String menuManagement(Model model) {
        return "system/menu-management";
    }

    /**
     * 权限测试页面
     */
    @GetMapping("/permission-test")
    public String permissionTest(Model model) {
        // 获取当前登录用户信息
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated()) {
            String username = authentication.getName();
            if (!"anonymousUser".equals(username)) {
                SysUser user = userService.getUserByUsername(username);
                if (user != null) {
                    model.addAttribute("user", user.getRealName() != null ? user.getRealName() : user.getUsername());
                    model.addAttribute("userInfo", user);
                } else {
                    model.addAttribute("user", username);
                }
            }
        }
        return "permission-test";
    }

    // 保持旧的路由以兼容性

    @GetMapping("/exchange-query")
    public String exchangeQueryOld(Model model) {
        return "exchange";
    }

    @GetMapping("/new-statistics")
    public String newStatisticsOld(Model model) {
        return "statistics";
    }

    // 登出由Spring Security自动处理

    /**
     * 生成验证码
     */
    private String generateCaptcha() {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        StringBuilder captcha = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < 4; i++) {
            captcha.append(chars.charAt(random.nextInt(chars.length())));
        }
        return captcha.toString();
    }
}
