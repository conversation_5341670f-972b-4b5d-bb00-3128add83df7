// 用户管理JavaScript
let currentPage = 1;
let pageSize = 10;
let totalPages = 0;
let selectedUsers = [];
let allRoles = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function () {
    // 初始化session检测
    initSessionCheck();
    loadUsers();
    loadAllRoles();
});

// 初始化session检测
function initSessionCheck() {
    // 检查是否有全局SessionManager
    if (window.parent && window.parent.SessionManager) {
        console.log('使用父窗口的SessionManager');
        return;
    }

    // 如果没有，创建简单的session检测
    const checkSession = async () => {
        try {
            const response = await fetch('/api/session/check', {
                method: 'GET',
                credentials: 'same-origin',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            if (response.status === 401 || response.status === 403) {
                // Session过期，跳转到登录页面
                window.top.location.href = '/login?expired=true';
                return false;
            }

            return response.ok;
        } catch (error) {
            console.error('Session检查失败:', error);
            return false;
        }
    };

    // 定期检查session
    setInterval(checkSession, 5 * 60 * 1000); // 5分钟检查一次

    // 页面获得焦点时检查
    window.addEventListener('focus', checkSession);
}

// 加载用户列表
function loadUsers() {
    const params = {
        current: currentPage,
        size: pageSize,
        username: document.getElementById('searchUsername').value,
        realName: document.getElementById('searchRealName').value,
        status: document.getElementById('searchStatus').value
    };

    axios.get('/api/system/user/page', { params })
        .then(response => {
            if (response.data.code === 200) {
                const data = response.data.data;
                renderUserTable(data.records);
                updatePagination(data);
            } else {
                showMessage('加载用户列表失败：' + response.data.message, 'error');
            }
        })
        .catch(error => {
            console.error('加载用户列表失败:', error);
            if (error.response && (error.response.status === 401 || error.response.status === 403)) {
                window.top.location.href = '/login?expired=true';
                return;
            }
            showMessage('加载用户列表失败', 'error');
        });
}

// 渲染用户表格
function renderUserTable(users) {
    const tbody = document.getElementById('userTableBody');
    tbody.innerHTML = '';

    users.forEach(user => {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-50';

        const roleNames = user.roleNames ? user.roleNames.join(', ') : '无';
        const statusBadge = user.status === '1'
            ? '<span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">启用</span>'
            : '<span class="px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full">禁用</span>';

        row.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap">
                <input type="checkbox" class="user-checkbox rounded" value="${user.id}" onchange="updateSelectedUsers()">
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${user.username}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${user.realName || ''}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${user.phone || ''}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${user.email || ''}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${roleNames}</td>
            <td class="px-6 py-4 whitespace-nowrap">${statusBadge}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${formatDate(user.createTime)}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-2">
                    <button onclick="editUser(${user.id})" class="text-blue-600 hover:text-blue-900" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button onclick="assignRoles(${user.id}, '${user.username}')" class="text-green-600 hover:text-green-900" title="分配角色">
                        <i class="fas fa-user-tag"></i>
                    </button>
                    <button onclick="resetUserPassword(${user.id})" class="text-yellow-600 hover:text-yellow-900" title="重置密码">
                        <i class="fas fa-key"></i>
                    </button>
                    <button onclick="deleteUser(${user.id})" class="text-red-600 hover:text-red-900" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// 更新分页信息
function updatePagination(data) {
    totalPages = data.pages;
    document.getElementById('pageStart').textContent = (currentPage - 1) * pageSize + 1;
    document.getElementById('pageEnd').textContent = Math.min(currentPage * pageSize, data.total);
    document.getElementById('totalCount').textContent = data.total;

    // 更新分页按钮状态
    document.getElementById('prevBtn').disabled = currentPage <= 1;
    document.getElementById('nextBtn').disabled = currentPage >= totalPages;

    // 生成页码按钮
    generatePageNumbers();
}

// 生成页码按钮
function generatePageNumbers() {
    const pageNumbers = document.getElementById('pageNumbers');
    pageNumbers.innerHTML = '';

    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
        const button = document.createElement('button');
        button.textContent = i;
        button.className = `px-3 py-2 text-sm rounded-md transition-colors ${i === currentPage
            ? 'bg-blue-600 text-white'
            : 'bg-gray-200 text-gray-600 hover:bg-gray-300'
            }`;
        button.onclick = () => goToPage(i);
        pageNumbers.appendChild(button);
    }
}

// 跳转到指定页
function goToPage(page) {
    currentPage = page;
    loadUsers();
}

// 上一页
function previousPage() {
    if (currentPage > 1) {
        currentPage--;
        loadUsers();
    }
}

// 下一页
function nextPage() {
    if (currentPage < totalPages) {
        currentPage++;
        loadUsers();
    }
}

// 搜索用户
function searchUsers() {
    currentPage = 1;
    loadUsers();
}

// 重置搜索
function resetSearch() {
    document.getElementById('searchUsername').value = '';
    document.getElementById('searchRealName').value = '';
    document.getElementById('searchStatus').value = '';
    currentPage = 1;
    loadUsers();
}

// 全选/取消全选
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.user-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });

    updateSelectedUsers();
}

// 更新选中的用户
function updateSelectedUsers() {
    const checkboxes = document.querySelectorAll('.user-checkbox:checked');
    selectedUsers = Array.from(checkboxes).map(cb => parseInt(cb.value));

    const batchDeleteBtn = document.getElementById('batchDeleteBtn');
    batchDeleteBtn.disabled = selectedUsers.length === 0;

    // 更新全选状态
    const allCheckboxes = document.querySelectorAll('.user-checkbox');
    const selectAll = document.getElementById('selectAll');
    selectAll.checked = allCheckboxes.length > 0 && selectedUsers.length === allCheckboxes.length;
}

// 打开新增用户模态框
function openAddUserModal() {
    document.getElementById('modalTitle').textContent = '新增用户';
    document.getElementById('userForm').reset();
    document.getElementById('userId').value = '';
    document.getElementById('passwordField').style.display = 'block';
    document.getElementById('password').required = true;
    document.getElementById('userModal').classList.add('show');
}

// 编辑用户
function editUser(id) {
    axios.get(`/api/system/user/${id}`)
        .then(response => {
            if (response.data.code === 200) {
                const user = response.data.data;
                document.getElementById('modalTitle').textContent = '编辑用户';
                document.getElementById('userId').value = user.id;
                document.getElementById('username').value = user.username;
                document.getElementById('realName').value = user.realName || '';
                document.getElementById('phone').value = user.phone || '';
                document.getElementById('email').value = user.email || '';
                document.getElementById('status').value = user.status;
                document.getElementById('remark').value = user.remark || '';
                document.getElementById('passwordField').style.display = 'none';
                document.getElementById('password').required = false;
                document.getElementById('userModal').classList.add('show');
            } else {
                showMessage('获取用户信息失败：' + response.data.message, 'error');
            }
        })
        .catch(error => {
            console.error('获取用户信息失败:', error);
            showMessage('获取用户信息失败', 'error');
        });
}

// 关闭用户模态框
function closeUserModal() {
    document.getElementById('userModal').classList.remove('show');
}

// 保存用户
function saveUser() {
    const form = document.getElementById('userForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const userId = document.getElementById('userId').value;
    const userData = {
        username: document.getElementById('username').value,
        realName: document.getElementById('realName').value,
        phone: document.getElementById('phone').value,
        email: document.getElementById('email').value,
        status: document.getElementById('status').value,
        remark: document.getElementById('remark').value
    };

    if (!userId) {
        userData.password = document.getElementById('password').value;
    } else {
        userData.id = parseInt(userId);
    }

    const url = userId ? '/api/system/user' : '/api/system/user';
    const method = userId ? 'put' : 'post';

    axios[method](url, userData)
        .then(response => {
            if (response.data.code === 200) {
                showMessage(userId ? '用户更新成功' : '用户创建成功', 'success');
                closeUserModal();
                loadUsers();
            } else {
                showMessage(response.data.message, 'error');
            }
        })
        .catch(error => {
            console.error('保存用户失败:', error);
            showMessage('保存用户失败', 'error');
        });
}

// 删除用户
function deleteUser(id) {
    if (confirm('确定要删除这个用户吗？')) {
        axios.delete(`/api/system/user/${id}`)
            .then(response => {
                if (response.data.code === 200) {
                    showMessage('用户删除成功', 'success');
                    loadUsers();
                } else {
                    showMessage('删除失败：' + response.data.message, 'error');
                }
            })
            .catch(error => {
                console.error('删除用户失败:', error);
                showMessage('删除用户失败', 'error');
            });
    }
}

// 批量删除用户
function batchDeleteUsers() {
    if (selectedUsers.length === 0) {
        showMessage('请选择要删除的用户', 'warning');
        return;
    }

    if (confirm(`确定要删除选中的 ${selectedUsers.length} 个用户吗？`)) {
        axios.delete('/api/system/user/batch', { data: selectedUsers })
            .then(response => {
                if (response.data.code === 200) {
                    showMessage('批量删除成功', 'success');
                    selectedUsers = [];
                    document.getElementById('selectAll').checked = false;
                    loadUsers();
                } else {
                    showMessage('批量删除失败：' + response.data.message, 'error');
                }
            })
            .catch(error => {
                console.error('批量删除失败:', error);
                showMessage('批量删除失败', 'error');
            });
    }
}

// 重置用户密码
function resetUserPassword(id) {
    const newPassword = prompt('请输入新密码:');
    if (newPassword) {
        axios.put(`/api/system/user/${id}/reset-password`, null, {
            params: { newPassword }
        })
            .then(response => {
                if (response.data.code === 200) {
                    showMessage('密码重置成功', 'success');
                } else {
                    showMessage('密码重置失败：' + response.data.message, 'error');
                }
            })
            .catch(error => {
                console.error('密码重置失败:', error);
                showMessage('密码重置失败', 'error');
            });
    }
}

// 加载所有角色
function loadAllRoles() {
    axios.get('/api/system/role/enabled')
        .then(response => {
            if (response.data.code === 200) {
                allRoles = response.data.data;
            }
        })
        .catch(error => {
            console.error('加载角色列表失败:', error);
        });
}

// 分配角色
function assignRoles(userId, username) {
    document.getElementById('roleUserId').value = userId;
    document.getElementById('roleUserName').textContent = username;

    // 获取用户当前角色
    axios.get(`/api/system/user/${userId}/roles`)
        .then(response => {
            if (response.data.code === 200) {
                const userRoles = response.data.data;
                renderRoleList(userRoles);
                document.getElementById('roleModal').classList.add('show');
            } else {
                showMessage('获取用户角色失败：' + response.data.message, 'error');
            }
        })
        .catch(error => {
            console.error('获取用户角色失败:', error);
            showMessage('获取用户角色失败', 'error');
        });
}

// 渲染角色列表
function renderRoleList(userRoles) {
    const roleList = document.getElementById('roleList');
    roleList.innerHTML = '';

    const userRoleIds = userRoles.map(role => role.id);

    allRoles.forEach(role => {
        const div = document.createElement('div');
        div.className = 'flex items-center';

        const checked = userRoleIds.includes(role.id) ? 'checked' : '';

        div.innerHTML = `
            <input type="checkbox" id="role_${role.id}" value="${role.id}" ${checked} class="role-checkbox rounded mr-2">
            <label for="role_${role.id}" class="text-sm text-gray-700">${role.roleName} (${role.roleCode})</label>
        `;

        roleList.appendChild(div);
    });
}

// 关闭角色模态框
function closeRoleModal() {
    document.getElementById('roleModal').classList.remove('show');
}

// 保存用户角色
function saveUserRoles() {
    const userId = document.getElementById('roleUserId').value;
    const checkboxes = document.querySelectorAll('.role-checkbox:checked');
    const roleIds = Array.from(checkboxes).map(cb => parseInt(cb.value));

    axios.post(`/api/system/user/${userId}/roles`, roleIds)
        .then(response => {
            if (response.data.code === 200) {
                showMessage('角色分配成功', 'success');
                closeRoleModal();
                loadUsers();
            } else {
                showMessage('角色分配失败：' + response.data.message, 'error');
            }
        })
        .catch(error => {
            console.error('角色分配失败:', error);
            showMessage('角色分配失败', 'error');
        });
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// 显示消息
function showMessage(message, type = 'info') {
    // 创建消息元素
    const messageDiv = document.createElement('div');
    messageDiv.className = `fixed top-4 right-4 px-4 py-2 rounded-md text-white z-50 ${type === 'success' ? 'bg-green-500' :
        type === 'error' ? 'bg-red-500' :
            type === 'warning' ? 'bg-yellow-500' :
                'bg-blue-500'
        }`;
    messageDiv.textContent = message;

    document.body.appendChild(messageDiv);

    // 3秒后自动移除
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.parentNode.removeChild(messageDiv);
        }
    }, 3000);
}

// 通用错误处理函数
function handleAjaxError(error, defaultMessage = '操作失败') {
    console.error(defaultMessage + ':', error);

    // 检查是否是session过期
    if (error.response && (error.response.status === 401 || error.response.status === 403)) {
        window.top.location.href = '/login?expired=true';
        return;
    }

    // 显示错误消息
    const message = error.response && error.response.data && error.response.data.message
        ? error.response.data.message
        : defaultMessage;
    showMessage(message, 'error');
}

// 设置axios全局拦截器
if (typeof axios !== 'undefined') {
    // 响应拦截器
    axios.interceptors.response.use(
        response => response,
        error => {
            if (error.response && (error.response.status === 401 || error.response.status === 403)) {
                window.top.location.href = '/login?expired=true';
                return Promise.reject(error);
            }
            return Promise.reject(error);
        }
    );
}