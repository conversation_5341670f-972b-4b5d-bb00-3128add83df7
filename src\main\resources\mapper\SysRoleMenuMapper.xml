<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cf.financing.mapper.SysRoleMenuMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.cf.financing.entity.SysRoleMenu">
        <id column="id" property="id" />
        <result column="role_id" property="roleId" />
        <result column="menu_id" property="menuId" />
        <result column="create_time" property="createTime" />
        <result column="create_by" property="createBy" />
    </resultMap>

    <!-- 根据角色ID删除角色菜单关联 -->
    <delete id="deleteByRoleId">
        DELETE FROM sys_role_menu WHERE role_id = #{roleId}
    </delete>

    <!-- 根据菜单ID删除角色菜单关联 -->
    <delete id="deleteByMenuId">
        DELETE FROM sys_role_menu WHERE menu_id = #{menuId}
    </delete>

    <!-- 批量插入角色菜单关联 -->
    <insert id="insertRoleMenuBatch">
        INSERT INTO sys_role_menu (role_id, menu_id, create_by, create_time)
        VALUES
        <foreach collection="menuIds" item="menuId" separator=",">
            (#{roleId}, #{menuId}, #{createBy}, NOW())
        </foreach>
    </insert>

    <!-- 根据角色ID查询菜单ID列表 -->
    <select id="selectMenuIdsByRoleId" resultType="java.lang.Long">
        SELECT menu_id FROM sys_role_menu WHERE role_id = #{roleId}
    </select>

    <!-- 根据菜单ID查询角色ID列表 -->
    <select id="selectRoleIdsByMenuId" resultType="java.lang.Long">
        SELECT role_id FROM sys_role_menu WHERE menu_id = #{menuId}
    </select>

    <!-- 检查角色是否拥有指定菜单权限 -->
    <select id="checkRoleHasMenu" resultType="int">
        SELECT COUNT(*) FROM sys_role_menu 
        WHERE role_id = #{roleId} AND menu_id = #{menuId}
    </select>

    <!-- 根据角色ID列表删除角色菜单关联 -->
    <delete id="deleteByRoleIds">
        DELETE FROM sys_role_menu 
        WHERE role_id IN
        <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </delete>

    <!-- 根据菜单ID列表删除角色菜单关联 -->
    <delete id="deleteByMenuIds">
        DELETE FROM sys_role_menu 
        WHERE menu_id IN
        <foreach collection="menuIds" item="menuId" open="(" separator="," close=")">
            #{menuId}
        </foreach>
    </delete>

    <!-- 查询角色菜单关联详情 -->
    <select id="selectRoleMenuDetails" resultMap="BaseResultMap">
        SELECT 
            rm.id, rm.role_id, rm.menu_id, rm.create_time, rm.create_by
        FROM sys_role_menu rm
        WHERE rm.role_id = #{roleId}
        ORDER BY rm.menu_id
    </select>

    <!-- 查询菜单角色关联详情 -->
    <select id="selectMenuRoleDetails" resultMap="BaseResultMap">
        SELECT 
            rm.id, rm.role_id, rm.menu_id, rm.create_time, rm.create_by
        FROM sys_role_menu rm
        WHERE rm.menu_id = #{menuId}
        ORDER BY rm.role_id
    </select>

    <!-- 批量更新角色菜单关联（先删除后插入） -->
    <update id="updateRoleMenus">
        <!-- 先删除原有关联 -->
        DELETE FROM sys_role_menu WHERE role_id = #{roleId};
        
        <!-- 如果有新菜单，则插入 -->
        <if test="menuIds != null and menuIds.size() > 0">
            INSERT INTO sys_role_menu (role_id, menu_id, create_by, create_time)
            VALUES
            <foreach collection="menuIds" item="menuId" separator=",">
                (#{roleId}, #{menuId}, #{createBy}, NOW())
            </foreach>
        </if>
    </update>

    <!-- 根据用户ID查询菜单权限 -->
    <select id="selectMenusByUserId" resultType="com.cf.financing.entity.SysMenu">
        SELECT DISTINCT 
            m.id, m.parent_id, m.menu_name, m.menu_code, m.menu_type, 
            m.path, m.component, m.permission, m.icon, m.sort_order,
            m.visible, m.status, m.is_frame, m.is_cache, m.remark
        FROM sys_menu m
        INNER JOIN sys_role_menu rm ON m.id = rm.menu_id
        INNER JOIN sys_user_role ur ON rm.role_id = ur.role_id
        WHERE ur.user_id = #{userId} AND m.status = 1
        ORDER BY m.sort_order
    </select>

</mapper>
