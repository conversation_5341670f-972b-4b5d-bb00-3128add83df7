/**
 * Session管理工具
 * 用于全局session检测和处理
 */
(function(window) {
    'use strict';

    // Session管理器
    const SessionManager = {
        // 配置选项
        config: {
            checkInterval: 5 * 60 * 1000, // 5分钟检查一次
            warningTime: 2 * 60 * 1000,   // 剩余2分钟时警告
            autoExtend: true,              // 自动延长session
            redirectUrl: '/login?expired=true'
        },

        // 状态变量
        isChecking: false,
        checkTimer: null,
        warningShown: false,

        /**
         * 初始化Session管理器
         */
        init: function(options = {}) {
            // 合并配置
            Object.assign(this.config, options);
            
            // 启动定时检查
            this.startPeriodicCheck();
            
            // 监听页面可见性变化
            this.setupVisibilityListener();
            
            // 拦截所有Ajax请求
            this.setupAjaxInterceptor();
            
            console.log('SessionManager已初始化');
        },

        /**
         * 检查session是否有效
         */
        checkSession: async function() {
            if (this.isChecking) {
                return true; // 避免重复检查
            }

            this.isChecking = true;
            
            try {
                const response = await fetch('/api/session/check', {
                    method: 'GET',
                    credentials: 'same-origin',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Cache-Control': 'no-cache'
                    }
                });

                this.isChecking = false;

                if (response.status === 401 || response.status === 403) {
                    this.handleSessionExpired();
                    return false;
                }

                if (!response.ok) {
                    // 检查响应内容是否包含登录页面标识
                    const text = await response.text();
                    if (text.includes('login') || text.includes('登录')) {
                        this.handleSessionExpired();
                        return false;
                    }
                }

                // 检查剩余时间
                const result = await response.json();
                if (result.success && result.data) {
                    this.checkRemainingTime(result.data);
                }

                return true;
            } catch (error) {
                this.isChecking = false;
                console.error('Session检查失败:', error);
                // 网络错误时进行二次检查
                return await this.secondaryCheck();
            }
        },

        /**
         * 二次检查 - 尝试访问一个需要认证的简单接口
         */
        secondaryCheck: async function() {
            try {
                const response = await fetch('/dashboard', {
                    method: 'HEAD',
                    credentials: 'same-origin'
                });

                if (response.status === 401 || response.status === 403) {
                    this.handleSessionExpired();
                    return false;
                }

                return response.ok;
            } catch (error) {
                console.error('二次session检查失败:', error);
                return true; // 网络问题时不强制跳转
            }
        },

        /**
         * 检查剩余时间并显示警告
         */
        checkRemainingTime: function(sessionData) {
            if (sessionData.remainingTime && sessionData.remainingTime < this.config.warningTime) {
                if (!this.warningShown) {
                    this.showSessionWarning(sessionData.remainingTime);
                    this.warningShown = true;
                }
            } else {
                this.warningShown = false;
            }
        },

        /**
         * 显示session即将过期警告
         */
        showSessionWarning: function(remainingTime) {
            const minutes = Math.ceil(remainingTime / 60000);
            
            // 创建警告提示
            const warningDiv = document.createElement('div');
            warningDiv.id = 'sessionWarning';
            warningDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%);
                color: white;
                padding: 20px 25px;
                border-radius: 10px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                z-index: 10000;
                font-family: 'Microsoft YaHei', sans-serif;
                min-width: 300px;
                animation: slideInRight 0.3s ease;
            `;

            warningDiv.innerHTML = `
                <div style="display: flex; align-items: center; margin-bottom: 15px;">
                    <i class="fa fa-clock-o" style="font-size: 20px; margin-right: 10px;"></i>
                    <strong>会话即将过期</strong>
                </div>
                <div style="margin-bottom: 15px; opacity: 0.9;">
                    您的登录会话将在 ${minutes} 分钟后过期
                </div>
                <div style="display: flex; gap: 10px;">
                    <button onclick="SessionManager.extendSession()" style="
                        background: rgba(255,255,255,0.2);
                        border: 1px solid rgba(255,255,255,0.3);
                        color: white;
                        padding: 8px 15px;
                        border-radius: 5px;
                        cursor: pointer;
                        flex: 1;
                    ">延长会话</button>
                    <button onclick="SessionManager.dismissWarning()" style="
                        background: transparent;
                        border: 1px solid rgba(255,255,255,0.3);
                        color: white;
                        padding: 8px 15px;
                        border-radius: 5px;
                        cursor: pointer;
                        flex: 1;
                    ">忽略</button>
                </div>
            `;

            // 添加动画样式
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideInRight {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
            `;
            document.head.appendChild(style);

            document.body.appendChild(warningDiv);

            // 自动消失
            setTimeout(() => {
                this.dismissWarning();
            }, 10000);
        },

        /**
         * 延长session
         */
        extendSession: async function() {
            try {
                const response = await fetch('/api/session/extend', {
                    method: 'GET',
                    credentials: 'same-origin'
                });

                if (response.ok) {
                    this.dismissWarning();
                    this.showSuccessMessage('会话已延长');
                    this.warningShown = false;
                } else {
                    this.handleSessionExpired();
                }
            } catch (error) {
                console.error('延长session失败:', error);
                this.showErrorMessage('延长会话失败');
            }
        },

        /**
         * 关闭警告提示
         */
        dismissWarning: function() {
            const warning = document.getElementById('sessionWarning');
            if (warning) {
                warning.remove();
            }
        },

        /**
         * 处理session过期
         */
        handleSessionExpired: function() {
            console.warn('Session已过期，即将跳转到登录页面');
            
            // 停止定时检查
            this.stopPeriodicCheck();
            
            // 显示提示信息
            this.showSessionExpiredMessage();
            
            // 延迟跳转，让用户看到提示
            setTimeout(() => {
                // 清除所有可能的缓存
                if (typeof(Storage) !== "undefined") {
                    sessionStorage.clear();
                    localStorage.removeItem('userInfo');
                }
                
                // 跳转到登录页面
                window.top.location.href = this.config.redirectUrl;
            }, 2000);
        },

        /**
         * 显示session过期提示
         */
        showSessionExpiredMessage: function() {
            // 移除可能存在的警告
            this.dismissWarning();
            
            // 创建提示框
            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 30px 40px;
                border-radius: 15px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                z-index: 10000;
                text-align: center;
                font-family: 'Microsoft YaHei', sans-serif;
                min-width: 300px;
                backdrop-filter: blur(10px);
            `;
            
            messageDiv.innerHTML = `
                <div style="font-size: 24px; margin-bottom: 15px;">
                    <i class="fa fa-exclamation-triangle" style="color: #ffd700; margin-right: 10px;"></i>
                    会话已过期
                </div>
                <div style="font-size: 16px; margin-bottom: 20px; opacity: 0.9;">
                    您的登录会话已失效，系统将自动跳转到登录页面
                </div>
                <div style="font-size: 14px; opacity: 0.7;">
                    <i class="fa fa-spinner fa-spin" style="margin-right: 8px;"></i>
                    正在跳转...
                </div>
            `;
            
            // 添加遮罩层
            const overlay = document.createElement('div');
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 9999;
                backdrop-filter: blur(3px);
            `;
            
            document.body.appendChild(overlay);
            document.body.appendChild(messageDiv);
            
            // 添加动画效果
            messageDiv.style.opacity = '0';
            messageDiv.style.transform = 'translate(-50%, -50%) scale(0.8)';
            
            setTimeout(() => {
                messageDiv.style.transition = 'all 0.3s ease';
                messageDiv.style.opacity = '1';
                messageDiv.style.transform = 'translate(-50%, -50%) scale(1)';
            }, 100);
        },

        /**
         * 启动定时检查
         */
        startPeriodicCheck: function() {
            this.stopPeriodicCheck(); // 先停止现有的定时器
            
            this.checkTimer = setInterval(() => {
                this.checkSession();
            }, this.config.checkInterval);
        },

        /**
         * 停止定时检查
         */
        stopPeriodicCheck: function() {
            if (this.checkTimer) {
                clearInterval(this.checkTimer);
                this.checkTimer = null;
            }
        },

        /**
         * 设置页面可见性监听
         */
        setupVisibilityListener: function() {
            document.addEventListener('visibilitychange', () => {
                if (!document.hidden) {
                    // 页面变为可见时立即检查session
                    this.checkSession();
                }
            });
        },

        /**
         * 设置Ajax请求拦截器
         */
        setupAjaxInterceptor: function() {
            // 拦截fetch请求
            const originalFetch = window.fetch;
            window.fetch = async function(...args) {
                const response = await originalFetch.apply(this, args);
                
                if (response.status === 401 || response.status === 403) {
                    SessionManager.handleSessionExpired();
                }
                
                return response;
            };

            // 拦截XMLHttpRequest
            const originalOpen = XMLHttpRequest.prototype.open;
            XMLHttpRequest.prototype.open = function(...args) {
                this.addEventListener('readystatechange', function() {
                    if (this.readyState === 4 && (this.status === 401 || this.status === 403)) {
                        SessionManager.handleSessionExpired();
                    }
                });
                
                return originalOpen.apply(this, args);
            };
        },

        /**
         * 显示成功消息
         */
        showSuccessMessage: function(message) {
            this.showMessage(message, 'success');
        },

        /**
         * 显示错误消息
         */
        showErrorMessage: function(message) {
            this.showMessage(message, 'error');
        },

        /**
         * 显示消息
         */
        showMessage: function(message, type = 'info') {
            const colors = {
                success: '#10b981',
                error: '#ef4444',
                info: '#3b82f6'
            };

            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${colors[type]};
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.2);
                z-index: 10000;
                font-family: 'Microsoft YaHei', sans-serif;
                animation: slideInRight 0.3s ease;
            `;

            messageDiv.textContent = message;
            document.body.appendChild(messageDiv);

            setTimeout(() => {
                messageDiv.remove();
            }, 3000);
        }
    };

    // 暴露到全局
    window.SessionManager = SessionManager;

    // 自动初始化（如果页面已加载）
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            SessionManager.init();
        });
    } else {
        SessionManager.init();
    }

})(window);
