package com.cf.financing.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cf.financing.entity.TaskList;
import com.cf.financing.service.ITaskListService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 作业清单控制器
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/api/tasklist")
public class TaskListController {

    @Autowired
    private ITaskListService taskListService;

    /**
     * 分页查询作业清单
     */
    @GetMapping("/page")
    public Map<String, Object> getTaskListPage(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String taskNo,
            @RequestParam(required = false) Long caseId,
            @RequestParam(required = false) Integer taskType,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) Integer priority,
            @RequestParam(required = false) Long assignedUserId,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            Page<TaskList> page = new Page<>(current, size);
            
            LocalDateTime startDateTime = null;
            LocalDateTime endDateTime = null;
            if (startTime != null && !startTime.isEmpty()) {
                startDateTime = LocalDateTime.parse(startTime);
            }
            if (endTime != null && !endTime.isEmpty()) {
                endDateTime = LocalDateTime.parse(endTime);
            }
            
            IPage<TaskList> taskListPage = taskListService.getTaskListPage(page, taskNo, caseId,
                    taskType, status, priority, assignedUserId, startDateTime, endDateTime);
            
            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", taskListPage);
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "查询失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 创建作业任务
     */
    @PostMapping("/create")
    public Map<String, Object> createTask(@RequestBody TaskList taskList) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = taskListService.createTask(taskList);
            if (success) {
                result.put("code", 200);
                result.put("message", "创建成功");
            } else {
                result.put("code", 500);
                result.put("message", "创建失败");
            }
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "创建失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 更新作业任务
     */
    @PutMapping("/update")
    public Map<String, Object> updateTask(@RequestBody TaskList taskList) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = taskListService.updateTask(taskList);
            if (success) {
                result.put("code", 200);
                result.put("message", "更新成功");
            } else {
                result.put("code", 500);
                result.put("message", "更新失败");
            }
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "更新失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 分配任务
     */
    @PutMapping("/assign/{id}")
    public Map<String, Object> assignTask(
            @PathVariable Long id,
            @RequestParam Long assignedUserId,
            @RequestParam(required = false) String assignRemark) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = taskListService.assignTask(id, assignedUserId, assignRemark);
            if (success) {
                result.put("code", 200);
                result.put("message", "分配成功");
            } else {
                result.put("code", 500);
                result.put("message", "分配失败");
            }
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "分配失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 开始任务
     */
    @PutMapping("/start/{id}")
    public Map<String, Object> startTask(@PathVariable Long id, @RequestParam Long userId) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = taskListService.startTask(id, userId);
            if (success) {
                result.put("code", 200);
                result.put("message", "任务已开始");
            } else {
                result.put("code", 500);
                result.put("message", "开始失败");
            }
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "开始失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 完成任务
     */
    @PutMapping("/complete/{id}")
    public Map<String, Object> completeTask(
            @PathVariable Long id,
            @RequestParam Long userId,
            @RequestParam(required = false) String completeRemark) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = taskListService.completeTask(id, userId, completeRemark);
            if (success) {
                result.put("code", 200);
                result.put("message", "任务已完成");
            } else {
                result.put("code", 500);
                result.put("message", "完成失败");
            }
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "完成失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 取消任务
     */
    @PutMapping("/cancel/{id}")
    public Map<String, Object> cancelTask(
            @PathVariable Long id,
            @RequestParam String cancelReason,
            @RequestParam Long operatorId) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = taskListService.cancelTask(id, cancelReason, operatorId);
            if (success) {
                result.put("code", 200);
                result.put("message", "任务已取消");
            } else {
                result.put("code", 500);
                result.put("message", "取消失败");
            }
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "取消失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 获取待处理任务
     */
    @GetMapping("/pending")
    public Map<String, Object> getPendingTasks(@RequestParam Long userId) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<TaskList> tasks = taskListService.getPendingTasks(userId);
            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", tasks);
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "查询失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 获取案件相关任务
     */
    @GetMapping("/case/{caseId}")
    public Map<String, Object> getTasksByCaseId(@PathVariable Long caseId) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<TaskList> tasks = taskListService.getTasksByCaseId(caseId);
            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", tasks);
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "查询失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 获取任务统计
     */
    @GetMapping("/statistics")
    public Map<String, Object> getTaskStatistics(
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) Long departmentId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            LocalDateTime startDateTime = null;
            LocalDateTime endDateTime = null;
            if (startDate != null && !startDate.isEmpty()) {
                startDateTime = LocalDateTime.parse(startDate);
            }
            if (endDate != null && !endDate.isEmpty()) {
                endDateTime = LocalDateTime.parse(endDate);
            }
            
            Map<String, Object> statusStats = taskListService.getTaskStatusStatistics(userId, departmentId, startDateTime, endDateTime);
            Map<String, Object> typeStats = taskListService.getTaskTypeStatistics(userId, departmentId, startDateTime, endDateTime);
            
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("statusStats", statusStats);
            statistics.put("typeStats", typeStats);
            
            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", statistics);
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "查询失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 获取逾期任务
     */
    @GetMapping("/overdue")
    public Map<String, Object> getOverdueTasks(
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) Long departmentId) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            List<TaskList> tasks = taskListService.getOverdueTasks(userId, departmentId);
            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", tasks);
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "查询失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 批量更新任务状态
     */
    @PutMapping("/batch-update-status")
    public Map<String, Object> batchUpdateTaskStatus(
            @RequestBody Map<String, Object> params) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            @SuppressWarnings("unchecked")
            List<Long> ids = (List<Long>) params.get("ids");
            Integer status = (Integer) params.get("status");
            String remark = (String) params.get("remark");
            Long operatorId = Long.valueOf(params.get("operatorId").toString());
            
            boolean success = taskListService.batchUpdateTaskStatus(ids, status, remark, operatorId);
            if (success) {
                result.put("code", 200);
                result.put("message", "批量更新成功");
            } else {
                result.put("code", 500);
                result.put("message", "批量更新失败");
            }
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "批量更新失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 根据案件自动创建任务
     */
    @PostMapping("/auto-create")
    public Map<String, Object> autoCreateTasksByCase(@RequestParam Long caseId) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = taskListService.autoCreateTasksByCase(caseId);
            if (success) {
                result.put("code", 200);
                result.put("message", "自动创建任务成功");
            } else {
                result.put("code", 500);
                result.put("message", "自动创建任务失败");
            }
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "自动创建任务失败: " + e.getMessage());
        }
        return result;
    }
}