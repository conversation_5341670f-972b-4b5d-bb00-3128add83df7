<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org" style="height: 100%;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - CF系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            height: 100vh;
            overflow: hidden;
        }

        .container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            padding: 10px;
            gap: 10px;
        }

        /* 筛选框样式 */
        .filter-box {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            height: 35vh;
            overflow-y: auto;
        }

        .filter-title {
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-label {
            min-width: 80px;
            font-size: 14px;
            color: #555;
            font-weight: 500;
        }

        .filter-control {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .filter-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .filter-btn-group {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        .filter-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: all 0.3s;
        }

        .filter-btn.search {
            background: #3498db;
            color: white;
        }

        .filter-btn.search:hover {
            background: #2980b9;
        }

        .filter-btn.reset {
            background: #95a5a6;
            color: white;
        }

        .filter-btn.reset:hover {
            background: #7f8c8d;
        }

        /* 功能框样式 */
        .function-box {
            background: white;
            border-radius: 8px;
            padding: 15px 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            height: 5vh;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .stats-info {
            display: flex;
            gap: 30px;
            align-items: center;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #555;
        }

        .stat-item i {
            color: #3498db;
        }

        .stat-item strong {
            color: #2c3e50;
            font-weight: 600;
        }

        .function-buttons {
            display: flex;
            gap: 10px;
        }

        .function-btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: all 0.3s;
        }

        .function-btn:hover {
            background: #f8f9fa;
            border-color: #3498db;
            color: #3498db;
        }

        .function-btn.primary {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .function-btn.primary:hover {
            background: #2980b9;
        }

        .function-btn.danger {
            background: #e74c3c;
            color: white;
            border-color: #e74c3c;
        }

        .function-btn.danger:hover {
            background: #c0392b;
        }

        /* 数据展示框样式 */
        .data-box {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            height: 60vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .data-header {
            padding: 15px 20px;
            border-bottom: 1px solid #e1e4e8;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8f9fa;
        }

        .data-title {
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .data-actions {
            display: flex;
            gap: 10px;
        }

        .data-action-btn {
            padding: 6px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
            transition: all 0.3s;
        }

        .data-action-btn:hover {
            background: #f8f9fa;
            border-color: #3498db;
            color: #3498db;
        }

        .data-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .table-wrapper {
            flex: 1;
            overflow: auto;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
        }

        .data-table th,
        .data-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #e1e4e8;
            white-space: nowrap;
        }

        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .data-table tbody tr:hover {
            background: #f8f9fa;
        }

        .status-tag {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
        }

        .status-danger {
            background: #f8d7da;
            color: #721c24;
        }

        .action-btn {
            padding: 4px 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
            background: white;
            cursor: pointer;
            font-size: 12px;
            margin-right: 5px;
            transition: all 0.3s;
        }

        .action-btn:hover {
            background: #f8f9fa;
        }

        .action-btn.edit {
            color: #3498db;
            border-color: #3498db;
        }

        .action-btn.delete {
            color: #e74c3c;
            border-color: #e74c3c;
        }

        .action-btn.assign {
            color: #27ae60;
            border-color: #27ae60;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 8px;
            padding: 20px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e1e4e8;
        }

        .modal-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #999;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            font-size: 14px;
            color: #555;
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .modal-footer {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #e1e4e8;
        }

        .modal-btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }

        .modal-btn.primary {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .modal-btn.primary:hover {
            background: #2980b9;
        }

        .modal-btn.secondary {
            background: #95a5a6;
            color: white;
            border-color: #95a5a6;
        }

        .modal-btn.secondary:hover {
            background: #7f8c8d;
        }
    </style>
</head>
<body>
    <!-- 主容器 -->
    <div class="container">
        <!-- 筛选框 -->
        <div class="filter-box">
            <div class="filter-title">
                <i class="fas fa-filter"></i>
                用户管理 - 筛选条件
            </div>
            <div class="filter-grid">
                <div class="filter-group">
                    <label class="filter-label">用户名:</label>
                    <input type="text" id="searchUsername" placeholder="请输入用户名" class="filter-control">
                </div>
                <div class="filter-group">
                    <label class="filter-label">真实姓名:</label>
                    <input type="text" id="searchRealName" placeholder="请输入真实姓名" class="filter-control">
                </div>
                <div class="filter-group">
                    <label class="filter-label">手机号:</label>
                    <input type="text" id="searchPhone" placeholder="请输入手机号" class="filter-control">
                </div>
                <div class="filter-group">
                    <label class="filter-label">邮箱:</label>
                    <input type="text" id="searchEmail" placeholder="请输入邮箱" class="filter-control">
                </div>
                <div class="filter-group">
                    <label class="filter-label">状态:</label>
                    <select id="searchStatus" class="filter-control">
                        <option value="">全部状态</option>
                        <option value="1">启用</option>
                        <option value="0">禁用</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">部门:</label>
                    <select id="searchDept" class="filter-control">
                        <option value="">全部部门</option>
                    </select>
                </div>
            </div>
            <div class="filter-btn-group">
                <button onclick="searchUsers()" class="filter-btn search">
                    <i class="fas fa-search"></i>搜索
                </button>
                <button onclick="resetSearch()" class="filter-btn reset">
                    <i class="fas fa-refresh"></i>重置
                </button>
            </div>
        </div>

        <!-- 功能框 -->
        <div class="function-box">
            <div class="stats-info">
                <div class="stat-item">
                    <i class="fas fa-users"></i>
                    <span>总用户数: <strong id="totalUsers">0</strong></span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-user-check"></i>
                    <span>启用用户: <strong id="activeUsers">0</strong></span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-user-times"></i>
                    <span>禁用用户: <strong id="inactiveUsers">0</strong></span>
                </div>
            </div>
            <div class="function-buttons">
                <button onclick="openAddUserModal()" class="function-btn primary">
                    <i class="fas fa-plus"></i>新增用户
                </button>
                <button onclick="batchDeleteUsers()" class="function-btn danger" disabled id="batchDeleteBtn">
                    <i class="fas fa-trash"></i>批量删除
                </button>
                <button onclick="exportUsers()" class="function-btn">
                    <i class="fas fa-download"></i>导出
                </button>
            </div>
        </div>

        <!-- 数据展示框 -->
        <div class="data-box">
            <div class="data-header">
                <div class="data-title">
                    <i class="fas fa-users"></i>
                    用户列表
                </div>
                <div class="data-actions">
                    <button onclick="refreshData()" class="data-action-btn">
                        <i class="fas fa-sync-alt"></i>刷新
                    </button>
                </div>
            </div>
            <div class="data-container">
                <div class="table-wrapper">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th width="50">
                                    <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                </th>
                                <th width="120">用户名</th>
                                <th width="120">真实姓名</th>
                                <th width="120">手机号</th>
                                <th width="150">邮箱</th>
                                <th width="120">角色</th>
                                <th width="80">状态</th>
                                <th width="150">创建时间</th>
                                <th width="200">操作</th>
                            </tr>
                        </thead>
                        <tbody id="userTableBody">
                            <!-- 用户数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <div class="pagination">
                    <div class="pagination-info">
                        显示第 <span id="pageStart">1</span> 到 <span id="pageEnd">10</span> 条，共 <span id="totalCount">0</span> 条记录
                    </div>
                    <div class="pagination-controls">
                        <button onclick="previousPage()" id="prevBtn" class="pagination-btn" disabled>
                            <i class="fas fa-chevron-left"></i>上一页
                        </button>
                        <div id="pageNumbers" class="page-numbers">
                            <!-- 页码按钮将动态生成 -->
                        </div>
                        <button onclick="nextPage()" id="nextBtn" class="pagination-btn">
                            下一页<i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增用户模态框 -->
    <div id="addUserModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-user-plus"></i>
                    新增用户
                </h3>
                <button onclick="closeAddUserModal()" class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="addUserForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label required">用户名:</label>
                            <input type="text" id="addUsername" name="username" required class="form-control" placeholder="请输入用户名">
                        </div>
                        <div class="form-group">
                            <label class="form-label required">真实姓名:</label>
                            <input type="text" id="addRealName" name="realName" required class="form-control" placeholder="请输入真实姓名">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label required">密码:</label>
                            <input type="password" id="addPassword" name="password" required class="form-control" placeholder="请输入密码">
                        </div>
                        <div class="form-group">
                            <label class="form-label required">确认密码:</label>
                            <input type="password" id="addConfirmPassword" name="confirmPassword" required class="form-control" placeholder="请再次输入密码">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">邮箱:</label>
                            <input type="email" id="addEmail" name="email" class="form-control" placeholder="请输入邮箱">
                        </div>
                        <div class="form-group">
                            <label class="form-label">手机号:</label>
                            <input type="tel" id="addPhone" name="phone" class="form-control" placeholder="请输入手机号">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">部门:</label>
                            <select id="addDeptId" name="deptId" class="form-control">
                                <option value="">请选择部门</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">状态:</label>
                            <select id="addStatus" name="status" class="form-control">
                                <option value="1">启用</option>
                                <option value="0">禁用</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" onclick="closeAddUserModal()" class="btn btn-secondary">
                    <i class="fas fa-times"></i>取消
                </button>
                <button type="submit" form="addUserForm" class="btn btn-primary">
                    <i class="fas fa-save"></i>保存
                </button>
            </div>
        </div>
    </div>

    <!-- 编辑用户模态框 -->
    <div id="userModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle" class="modal-title">
                    <i class="fas fa-user-edit"></i>
                    编辑用户
                </h3>
                <button onclick="closeUserModal()" class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="userForm">
                    <input type="hidden" id="userId">
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label required">用户名:</label>
                            <input type="text" id="username" required class="form-control" placeholder="请输入用户名">
                        </div>
                        <div class="form-group">
                            <label class="form-label required">真实姓名:</label>
                            <input type="text" id="realName" required class="form-control" placeholder="请输入真实姓名">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label required">手机号:</label>
                            <input type="tel" id="phone" required class="form-control" placeholder="请输入手机号">
                        </div>
                        <div class="form-group">
                            <label class="form-label">邮箱:</label>
                            <input type="email" id="email" class="form-control" placeholder="请输入邮箱">
                        </div>
                    </div>
                    <div class="form-row" id="passwordField">
                        <div class="form-group">
                            <label class="form-label required">密码:</label>
                            <input type="password" id="password" class="form-control" placeholder="请输入密码">
                        </div>
                        <div class="form-group">
                            <label class="form-label">状态:</label>
                            <select id="status" class="form-control">
                                <option value="1">启用</option>
                                <option value="0">禁用</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group full-width">
                            <label class="form-label">备注:</label>
                            <textarea id="remark" rows="3" class="form-control" placeholder="请输入备注信息"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" onclick="closeUserModal()" class="btn btn-secondary">
                    <i class="fas fa-times"></i>取消
                </button>
                <button type="button" onclick="saveUser()" class="btn btn-primary">
                    <i class="fas fa-save"></i>保存
                </button>
            </div>
        </div>
    </div>

    <!-- 角色分配模态框 -->
    <div id="roleModal" class="modal">
        <div class="modal-content modal-sm">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-user-tag"></i>
                    分配角色
                </h3>
                <button onclick="closeRoleModal()" class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="roleUserId">
                <div class="user-info">
                    <p class="info-text">用户：<span id="roleUserName" class="info-value"></span></p>
                </div>
                <div class="role-list" id="roleList">
                    <!-- 角色列表将动态加载 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" onclick="closeRoleModal()" class="btn btn-secondary">
                    <i class="fas fa-times"></i>取消
                </button>
                <button type="button" onclick="saveUserRoles()" class="btn btn-primary">
                    <i class="fas fa-save"></i>保存
                </button>
            </div>
        </div>
    </div>

    <script th:src="@{/js/user-management.js}"></script>
</body>
</html>