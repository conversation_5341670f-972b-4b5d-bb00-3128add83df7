<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap"
        rel="stylesheet">
    <style>
        :root {
            --primary-color: #165DFF;
            --primary-light: #E8F3FF;
            --success-color: #00B42A;
            --success-light: #E8FFEF;
            --danger-color: #F53F3F;
            --danger-light: #FFF2F2;
            --warning-color: #FF7D00;
            --warning-light: #FFF7E8;
            --text-color: #1D2129;
            --text-secondary: #4E5969;
            --bg-color: #F7F8FA;
            --card-bg: #FFFFFF;
            --border-color: #E5E6EB;
            --hover-bg: #F2F3F5;
            --shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            --shadow-lg: 0 4px 16px rgba(0, 0, 0, 0.12);
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Noto Sans SC', 'Microsoft YaHei', sans-serif;
        }

        body {
            background: var(--bg-color);
            color: var(--text-color);
            min-height: 100vh;
            padding: 10px;
            display: flex;
            justify-content: center;
            font-size: 14px;
            overflow: hidden;
        }

        .app-container {
            width: 100%;
            max-width: 1400px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: var(--shadow);
            transition: var(--transition);
            background: var(--card-bg);
            display: flex;
            flex-direction: column;
            height: 100vh;
            max-height: 900px;
        }

        .app-container:hover {
            box-shadow: var(--shadow-lg);
        }

        /* 导航栏 */
        .app-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 14px 20px;
            border-bottom: 1px solid var(--border-color);
            background: var(--card-bg);
            position: relative;
        }

        .app-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--primary-color);
        }

        .nav-actions {
            display: flex;
            gap: 10px;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* 筛选框区域 */
        .query-form {
            padding: 18px 20px;
            background: var(--card-bg);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            position: relative;
            z-index: 10;
        }

        .form-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .form-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-color);
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
        }

        @media (max-width: 1200px) {
            .form-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 480px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .form-group label {
            font-size: 12px;
            color: var(--text-secondary);
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 5px;
            white-space: normal;
        }

        .form-control {
            padding: 8px 12px;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-color);
            font-size: 13px;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(22, 93, 255, 0.2);
        }

        .form-control::placeholder {
            color: #C9CDD4;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            font-weight: 500;
            cursor: pointer;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 5px;
            transition: var(--transition);
            justify-content: center;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #0D47A1;
            box-shadow: 0 2px 8px rgba(22, 93, 255, 0.3);
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: var(--hover-bg);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: #E5E6EB;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background: #009A29;
            box-shadow: 0 2px 8px rgba(0, 180, 42, 0.3);
            transform: translateY(-1px);
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .btn-warning:hover {
            background: #E86F00;
            box-shadow: 0 2px 8px rgba(255, 125, 0, 0.3);
            transform: translateY(-1px);
        }

        .btn-danger {
            background: var(--danger-color);
            color: white;
        }

        .btn-danger:hover {
            background: #E63946;
            box-shadow: 0 2px 8px rgba(245, 63, 63, 0.3);
            transform: translateY(-1px);
        }

        .form-actions {
            display: flex;
            gap: 10px;
            margin-top: 12px;
            justify-content: flex-end;
        }

        /* 操作按钮区域样式 */
        .action-buttons {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 20px 0;
            margin-bottom: 20px;
            border-bottom: 1px solid #e9ecef;
            background: #f8f9fa;
            border-radius: 6px;
            padding-left: 16px;
            padding-right: 16px;
        }

        .action-buttons .btn {
            height: 36px;
            padding: 0 16px;
            font-size: 14px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: all 0.2s ease;
        }

        .action-buttons .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }



        /* 表格容器 */
        .table-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            background: var(--card-bg);
        }

        .table-content {
            flex: 1;
            overflow: auto;
            position: relative;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
            background: var(--card-bg);
        }

        .data-table th,
        .data-table td {
            padding: 10px 8px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
            white-space: nowrap;
            vertical-align: middle;
        }

        .data-table th {
            background: var(--hover-bg);
            font-weight: 600;
            color: var(--text-color);
            position: sticky;
            top: 0;
            z-index: 10;
            font-size: 12px;
        }

        .data-table tbody tr {
            transition: var(--transition);
        }

        .data-table tbody tr:hover {
            background: var(--hover-bg);
        }

        .checkbox-cell {
            width: 40px;
            text-align: center;
        }

        .checkbox-cell input[type="checkbox"] {
            cursor: pointer;
        }

        .highlight {
            color: var(--primary-color);
            font-weight: 600;
        }

        .status-tag {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            display: inline-block;
        }

        .status-tag.success {
            background: var(--success-light);
            color: var(--success-color);
        }

        .status-tag.danger {
            background: var(--danger-light);
            color: var(--danger-color);
        }

        .status-tag.warning {
            background: var(--warning-light);
            color: var(--warning-color);
        }

        .action-btn {
            padding: 4px 8px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: var(--card-bg);
            cursor: pointer;
            font-size: 11px;
            margin-right: 4px;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            gap: 3px;
        }

        .action-btn:hover {
            background: var(--hover-bg);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .action-btn.edit {
            color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .action-btn.delete {
            color: var(--danger-color);
            border-color: var(--danger-color);
        }

        .action-btn.reset {
            color: var(--warning-color);
            border-color: var(--warning-color);
        }

        .action-btn.assign {
            color: var(--info-color);
            border-color: var(--info-color);
        }

        /* 分配角色模态框样式 */
        .user-info-display {
            padding: 8px 12px;
            background: var(--hover-bg);
            border-radius: 4px;
            font-weight: 500;
            color: var(--text-color);
        }

        .role-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 8px;
        }

        .role-item {
            display: flex;
            align-items: center;
            padding: 8px;
            border-radius: 4px;
            margin-bottom: 4px;
            transition: var(--transition);
        }

        .role-item:hover {
            background: var(--hover-bg);
        }

        .role-item input[type="checkbox"] {
            margin-right: 8px;
        }

        .role-item label {
            flex: 1;
            margin: 0;
            cursor: pointer;
            font-weight: normal;
        }

        .role-description {
            font-size: 12px;
            color: var(--text-secondary);
            margin-left: 20px;
        }

        /* 分页样式 */
        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 20px;
            border-top: 1px solid var(--border-color);
            background: var(--card-bg);
        }

        .pagination-info {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .pagination-controls {
            display: flex;
            gap: 4px;
        }

        .pagination-btn {
            padding: 6px 10px;
            border: 1px solid var(--border-color);
            background: var(--card-bg);
            cursor: pointer;
            font-size: 12px;
            border-radius: 4px;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 32px;
        }

        .pagination-btn:hover:not(:disabled) {
            background: var(--hover-bg);
            border-color: var(--primary-color);
        }

        .pagination-btn.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background: var(--card-bg);
            border-radius: 8px;
            padding: 0;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 20px;
            border-bottom: 1px solid var(--border-color);
            background: var(--hover-bg);
        }

        .modal-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-color);
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: var(--text-secondary);
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-close:hover {
            color: var(--text-color);
        }

        .modal-body {
            padding: 20px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .modal-footer {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            padding: 16px 20px;
            border-top: 1px solid var(--border-color);
            background: var(--hover-bg);
        }
    </style>
</head>

<body>
    <div class="app-container">
        <!-- 导航栏 -->
        <div class="app-nav">
            <div class="app-title">
                <i class="fas fa-users"></i>
                用户管理系统
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 筛选框区域 -->
            <div class="query-form">
                <div class="form-header">
                    <div class="form-title">
                        <i class="fas fa-filter"></i>
                        查询条件
                    </div>
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label>用户名</label>
                        <input type="text" class="form-control" placeholder="请输入用户名" id="username">
                    </div>

                    <div class="form-group">
                        <label>真实姓名</label>
                        <input type="text" class="form-control" placeholder="请输入真实姓名" id="realName">
                    </div>

                    <div class="form-group">
                        <label>用户状态</label>
                        <select class="form-control" id="status">
                            <option value="">全部状态</option>
                            <option value="1">启用</option>
                            <option value="0">禁用</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>所属角色</label>
                        <select class="form-control" id="roleId">
                            <option value="">全部角色</option>
                            <option value="1">管理员</option>
                            <option value="2">普通用户</option>
                            <option value="3">客服</option>
                        </select>
                    </div>
                </div>

                <div class="form-actions">
                    <button class="btn btn-secondary" id="resetBtn">
                        <i class="fas fa-undo"></i>
                        重置
                    </button>
                    <button class="btn btn-primary" id="searchBtn">
                        <i class="fas fa-search"></i>
                        查询
                    </button>
                </div>
            </div>

            <!-- 操作按钮区域 -->
            <div class="action-buttons">
                <button class="btn btn-success" id="addUserBtn">
                    <i class="fas fa-plus"></i>
                    新增用户
                </button>
                <button class="btn btn-danger" id="batchDeleteBtn">
                    <i class="fas fa-trash"></i>
                    批量删除
                </button>
            </div>

            <!-- 记录展示区域 -->
            <div class="table-container">
                <div class="table-content">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th class="checkbox-cell">
                                    <input type="checkbox" id="selectAll">
                                </th>
                                <th>用户名</th>
                                <th>真实姓名</th>
                                <th>手机号</th>
                                <th>邮箱</th>
                                <th>角色</th>
                                <th>状态</th>
                                <th>最后登录</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="tableBody">
                            <!-- 数据将通过JavaScript动态生成 -->
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="pagination">
                    <div class="pagination-info">
                        共 <span id="totalRecords">0</span> 条记录，第 <span id="currentPage">1</span> / <span
                            id="totalPages">1</span> 页
                    </div>
                    <div class="pagination-controls">
                        <button class="pagination-btn" id="prevBtn" disabled>
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button class="pagination-btn active">1</button>
                        <button class="pagination-btn">2</button>
                        <button class="pagination-btn">3</button>
                        <button class="pagination-btn" id="nextBtn">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户编辑模态框 -->
    <div class="modal" id="userModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">新增用户</h3>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="userForm">
                    <div class="form-group">
                        <label>用户名 *</label>
                        <input type="text" class="form-control" id="formUsername" required>
                    </div>
                    <div class="form-group">
                        <label>真实姓名 *</label>
                        <input type="text" class="form-control" id="formRealName" required>
                    </div>
                    <div class="form-group">
                        <label>手机号 *</label>
                        <input type="tel" class="form-control" id="formPhone" required>
                    </div>
                    <div class="form-group">
                        <label>邮箱</label>
                        <input type="email" class="form-control" id="formEmail">
                    </div>
                    <div class="form-group">
                        <label>角色 *</label>
                        <select class="form-control" id="formRole" required>
                            <option value="">请选择角色</option>
                            <option value="1">管理员</option>
                            <option value="2">普通用户</option>
                            <option value="3">客服</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>状态</label>
                        <select class="form-control" id="formStatus">
                            <option value="1">启用</option>
                            <option value="0">禁用</option>
                        </select>
                    </div>
                    <div class="form-group" id="passwordGroup">
                        <label>密码 *</label>
                        <input type="password" class="form-control" id="formPassword">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal()">取消</button>
                <button class="btn btn-primary" onclick="saveUser()">保存</button>
            </div>
        </div>
    </div>

    <!-- 分配角色模态框 -->
    <div class="modal" id="roleModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">分配角色</h3>
                <button class="modal-close" onclick="closeRoleModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>用户信息</label>
                    <div class="user-info-display">
                        <span id="assignUserName"></span> (<span id="assignUserRealName"></span>)
                    </div>
                </div>
                <div class="form-group">
                    <label>选择角色</label>
                    <div class="role-list" id="roleList">
                        <!-- 角色列表将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeRoleModal()">取消</button>
                <button class="btn btn-primary" onclick="saveUserRoles()">保存</button>
            </div>
        </div>
    </div>

    <script>
        // 模拟数据
        const mockData = [
            {
                id: 1,
                username: 'admin',
                realName: '系统管理员',
                phone: '13800138000',
                email: '<EMAIL>',
                roleId: 1,
                roleName: '管理员',
                status: 1,
                lastLogin: '2023-12-10 09:30',
                createTime: '2023-01-01 10:00'
            },
            {
                id: 2,
                username: 'zhangsan',
                realName: '张三',
                phone: '13800138001',
                email: '<EMAIL>',
                roleId: 2,
                roleName: '普通用户',
                status: 1,
                lastLogin: '2023-12-09 14:20',
                createTime: '2023-06-15 16:30'
            },
            {
                id: 3,
                username: 'lisi',
                realName: '李四',
                phone: '13800138002',
                email: '<EMAIL>',
                roleId: 3,
                roleName: '客服',
                status: 0,
                lastLogin: '2023-12-08 11:45',
                createTime: '2023-08-20 09:15'
            },
            {
                id: 4,
                username: 'wangwu',
                realName: '王五',
                phone: '13800138003',
                email: '<EMAIL>',
                roleId: 2,
                roleName: '普通用户',
                status: 1,
                lastLogin: '2023-12-10 16:10',
                createTime: '2023-09-10 14:20'
            },
            {
                id: 5,
                username: 'zhaoliu',
                realName: '赵六',
                phone: '13800138004',
                email: '<EMAIL>',
                roleId: 3,
                roleName: '客服',
                status: 1,
                lastLogin: '2023-12-09 08:30',
                createTime: '2023-10-05 11:45'
            }
        ];

        let currentEditId = null;

        // 状态映射
        const statusMap = {
            1: { text: '启用', class: 'success' },
            0: { text: '禁用', class: 'danger' }
        };

        // 渲染表格
        function renderTable() {
            const tbody = document.getElementById('tableBody');
            tbody.innerHTML = '';

            mockData.forEach(item => {
                const status = statusMap[item.status];
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="checkbox-cell">
                        <input type="checkbox" data-id="${item.id}">
                    </td>
                    <td class="highlight">${item.username}</td>
                    <td>${item.realName}</td>
                    <td>${item.phone}</td>
                    <td>${item.email || '-'}</td>
                    <td>${item.roleName}</td>
                    <td>
                        <span class="status-tag ${status.class}">${status.text}</span>
                    </td>
                    <td>${item.lastLogin || '-'}</td>
                    <td>${item.createTime}</td>
                    <td>
                        <button class="action-btn edit" onclick="editUser(${item.id})">
                            <i class="fas fa-edit"></i>
                            编辑
                        </button>
                        <button class="action-btn assign" onclick="assignRole(${item.id})">
                            <i class="fas fa-user-tag"></i>
                            分配角色
                        </button>
                        <button class="action-btn reset" onclick="resetPassword(${item.id})">
                            <i class="fas fa-key"></i>
                            重置密码
                        </button>
                        <button class="action-btn delete" onclick="deleteUser(${item.id})">
                            <i class="fas fa-trash"></i>
                            删除
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });

        }

        // 新增用户
        function addUser() {
            currentEditId = null;
            document.getElementById('modalTitle').textContent = '新增用户';
            document.getElementById('passwordGroup').style.display = 'block';
            document.getElementById('formPassword').required = true;
            document.getElementById('userForm').reset();
            document.getElementById('userModal').classList.add('show');
        }

        // 编辑用户
        function editUser(id) {
            const user = mockData.find(item => item.id === id);
            if (user) {
                currentEditId = id;
                document.getElementById('modalTitle').textContent = '编辑用户';
                document.getElementById('passwordGroup').style.display = 'none';
                document.getElementById('formPassword').required = false;

                document.getElementById('formUsername').value = user.username;
                document.getElementById('formRealName').value = user.realName;
                document.getElementById('formPhone').value = user.phone;
                document.getElementById('formEmail').value = user.email || '';
                document.getElementById('formRole').value = user.roleId;
                document.getElementById('formStatus').value = user.status;

                document.getElementById('userModal').classList.add('show');
            }
        }

        // 保存用户
        function saveUser() {
            const form = document.getElementById('userForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const userData = {
                username: document.getElementById('formUsername').value,
                realName: document.getElementById('formRealName').value,
                phone: document.getElementById('formPhone').value,
                email: document.getElementById('formEmail').value,
                roleId: parseInt(document.getElementById('formRole').value),
                status: parseInt(document.getElementById('formStatus').value)
            };

            // 角色名称映射
            const roleNames = { 1: '管理员', 2: '普通用户', 3: '客服' };
            userData.roleName = roleNames[userData.roleId];

            if (currentEditId) {
                // 编辑模式
                const index = mockData.findIndex(item => item.id === currentEditId);
                if (index > -1) {
                    mockData[index] = { ...mockData[index], ...userData };
                }
                alert('用户信息更新成功！');
            } else {
                // 新增模式
                userData.id = Math.max(...mockData.map(item => item.id)) + 1;
                userData.createTime = new Date().toLocaleString('zh-CN');
                userData.lastLogin = null;
                mockData.push(userData);
                alert('用户创建成功！');
            }

            closeModal();
            renderTable();
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('userModal').classList.remove('show');
            currentEditId = null;
        }

        // 删除用户
        function deleteUser(id) {
            if (confirm('确定要删除这个用户吗？此操作无法撤销。')) {
                const index = mockData.findIndex(item => item.id === id);
                if (index > -1) {
                    mockData.splice(index, 1);
                    renderTable();
                    alert('用户删除成功！');
                }
            }
        }

        // 批量删除用户
        function batchDeleteUsers() {
            const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]:checked');
            if (checkboxes.length === 0) {
                alert('请选择要删除的用户');
                return;
            }

            if (confirm(`确定要删除选中的 ${checkboxes.length} 个用户吗？`)) {
                const idsToDelete = Array.from(checkboxes).map(cb => parseInt(cb.getAttribute('data-id')));

                // 从数据中删除选中的用户
                for (let i = mockData.length - 1; i >= 0; i--) {
                    if (idsToDelete.includes(mockData[i].id)) {
                        mockData.splice(i, 1);
                    }
                }

                renderTable();
                alert(`成功删除 ${idsToDelete.length} 个用户`);
            }
        }

        // 重置密码
        function resetPassword(id) {
            const user = mockData.find(item => item.id === id);
            if (user && confirm(`确定要重置用户 "${user.realName}" 的密码吗？`)) {
                alert('密码重置成功！新密码已发送到用户邮箱。');
            }
        }

        // 分配角色
        let currentAssignUserId = null;
        const availableRoles = [
            { id: 1, name: '管理员', description: '系统管理员，拥有所有权限' },
            { id: 2, name: '普通用户', description: '普通用户，基础功能权限' },
            { id: 3, name: '客服', description: '客服人员，客户服务相关权限' }
        ];

        function assignRole(id) {
            const user = mockData.find(item => item.id === id);
            if (user) {
                currentAssignUserId = id;
                document.getElementById('assignUserName').textContent = user.username;
                document.getElementById('assignUserRealName').textContent = user.realName;

                // 渲染角色列表
                renderRoleList(user.roleId);

                document.getElementById('roleModal').classList.add('show');
            }
        }

        function renderRoleList(currentRoleId) {
            const roleList = document.getElementById('roleList');
            roleList.innerHTML = '';

            availableRoles.forEach(role => {
                const roleItem = document.createElement('div');
                roleItem.className = 'role-item';
                roleItem.innerHTML = `
                    <input type="checkbox" id="role_${role.id}" value="${role.id}" ${role.id === currentRoleId ? 'checked' : ''}>
                    <label for="role_${role.id}">${role.name}</label>
                    <div class="role-description">${role.description}</div>
                `;
                roleList.appendChild(roleItem);
            });
        }

        function saveUserRoles() {
            const checkedRoles = document.querySelectorAll('#roleList input[type="checkbox"]:checked');
            if (checkedRoles.length === 0) {
                alert('请至少选择一个角色');
                return;
            }

            if (checkedRoles.length > 1) {
                alert('当前版本只支持单角色分配，请只选择一个角色');
                return;
            }

            const selectedRoleId = parseInt(checkedRoles[0].value);
            const selectedRole = availableRoles.find(role => role.id === selectedRoleId);

            // 更新用户数据
            const userIndex = mockData.findIndex(item => item.id === currentAssignUserId);
            if (userIndex > -1) {
                mockData[userIndex].roleId = selectedRoleId;
                mockData[userIndex].roleName = selectedRole.name;
            }

            closeRoleModal();
            renderTable();
            alert('角色分配成功！');
        }

        function closeRoleModal() {
            document.getElementById('roleModal').classList.remove('show');
            currentAssignUserId = null;
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function () {
            // 渲染表格
            renderTable();

            // 绑定事件
            document.getElementById('addUserBtn').addEventListener('click', addUser);
            document.getElementById('batchDeleteBtn').addEventListener('click', batchDeleteUsers);

            // 搜索功能
            document.getElementById('searchBtn').addEventListener('click', function () {
                console.log('执行搜索');
                // 这里可以添加实际的搜索逻辑
            });

            // 重置功能
            document.getElementById('resetBtn').addEventListener('click', function () {
                document.getElementById('username').value = '';
                document.getElementById('realName').value = '';
                document.getElementById('status').value = '';
                document.getElementById('roleId').value = '';
            });

            // 全选功能
            document.getElementById('selectAll').addEventListener('change', function () {
                const checkboxes = document.querySelectorAll('#tableBody input[type="checkbox"]');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
            });

            // 点击模态框外部关闭
            document.getElementById('userModal').addEventListener('click', function (e) {
                if (e.target === this) {
                    closeModal();
                }
            });

            // 点击角色模态框外部关闭
            document.getElementById('roleModal').addEventListener('click', function (e) {
                if (e.target === this) {
                    closeRoleModal();
                }
            });
        });
    </script>
</body>

</html>