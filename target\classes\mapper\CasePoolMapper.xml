<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cf.financing.mapper.CasePoolMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.cf.financing.entity.CasePool">
        <id column="id" property="id" />
        <result column="client_name" property="clientName" />
        <result column="client_id" property="clientId" />
        <result column="cardholder_code" property="cardholderCode" />
        <result column="entrusted_amount" property="entrustedAmount" />
        <result column="entrusted_principal" property="entrustedPrincipal" />
        <result column="batch_number" property="batchNumber" />
        <result column="pool_count" property="poolCount" />
        <result column="unfollowed_days" property="unfollowedDays" />
        <result column="config_status" property="configStatus" />
        <result column="balance_ops" property="balanceOps" />
        <result column="principal_ops" property="principalOps" />
        <result column="case_type" property="caseType" />
        <result column="special_type" property="specialType" />
        <result column="repair_result" property="repairResult" />
        <result column="entrust_start_date" property="entrustStartDate" />
        <result column="entrust_end_date" property="entrustEndDate" />
        <result column="retention_count" property="retentionCount" />
        <result column="card_opening_date" property="cardOpeningDate" />
        <result column="credit_limit" property="creditLimit" />
        <result column="last_payment_date" property="lastPaymentDate" />
        <result column="overdue_period_at_entrust" property="overduePeriodAtEntrust" />
        <result column="target_period" property="targetPeriod" />
        <result column="city" property="city" />
        <result column="last_followup_date" property="lastFollowupDate" />
        <result column="case_flag" property="caseFlag" />
        <result column="current_overdue_period" property="currentOverduePeriod" />
        <result column="residence_city" property="residenceCity" />
        <result column="client_age" property="clientAge" />
        <result column="occupation" property="occupation" />
        <result column="account_last_7" property="accountLast7" />
        <result column="month_range_at_entrust" property="monthRangeAtEntrust" />
        <result column="amount_range_at_entrust" property="amountRangeAtEntrust" />
        <result column="commission_range_at_entrust" property="commissionRangeAtEntrust" />
        <result column="rating" property="rating" />
        <result column="is_litigation" property="isLitigation" />
        <result column="current_month_payment" property="currentMonthPayment" />
        <result column="previous_day_payment" property="previousDayPayment" />
        <result column="installment_status" property="installmentStatus" />
        <result column="installment_performance" property="installmentPerformance" />
        <result column="complaint_tag" property="complaintTag" />
        <result column="installment_performance_tag" property="installmentPerformanceTag" />
        <result column="litigation_tag" property="litigationTag" />
        <result column="voice_tag" property="voiceTag" />
        <result column="special_tag" property="specialTag" />
        <result column="is_litigation_commission" property="isLitigationCommission" />
        <result column="institution" property="institution" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 分页查询案池数据 -->
    <select id="selectCasePoolPage" resultMap="BaseResultMap">
        SELECT * FROM case_pool
        WHERE deleted = 0
        <if test="params.clientName != null and params.clientName != ''">
            AND client_name LIKE CONCAT('%', #{params.clientName}, '%')
        </if>
        <if test="params.clientId != null and params.clientId != ''">
            AND client_id LIKE CONCAT('%', #{params.clientId}, '%')
        </if>
        <if test="params.city != null and params.city != ''">
            AND city LIKE CONCAT('%', #{params.city}, '%')
        </if>
        <if test="params.configStatus != null and params.configStatus != ''">
            AND config_status = #{params.configStatus}
        </if>
        <if test="params.caseType != null and params.caseType != ''">
            AND case_type LIKE CONCAT('%', #{params.caseType}, '%')
        </if>
        <if test="params.batchNumber != null and params.batchNumber != ''">
            AND batch_number LIKE CONCAT('%', #{params.batchNumber}, '%')
        </if>
        <if test="params.cardholderCode != null and params.cardholderCode != ''">
            AND cardholder_code LIKE CONCAT('%', #{params.cardholderCode}, '%')
        </if>
        <if test="params.specialType != null and params.specialType != ''">
            AND special_type LIKE CONCAT('%', #{params.specialType}, '%')
        </if>
        <if test="params.repairResult != null and params.repairResult != ''">
            AND repair_result = #{params.repairResult}
        </if>
        <if test="params.startDateBegin != null">
            AND entrust_start_date >= #{params.startDateBegin}
        </if>
        <if test="params.startDateEnd != null">
            AND entrust_start_date &lt;= #{params.startDateEnd}
        </if>
        <if test="params.endDateBegin != null">
            AND entrust_end_date >= #{params.endDateBegin}
        </if>
        <if test="params.endDateEnd != null">
            AND entrust_end_date &lt;= #{params.endDateEnd}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 统计案池总数 -->
    <select id="countCasePool" resultType="java.lang.Long">
        SELECT COUNT(*) FROM case_pool
        WHERE deleted = 0
        <if test="params.clientName != null and params.clientName != ''">
            AND client_name LIKE CONCAT('%', #{params.clientName}, '%')
        </if>
        <if test="params.configStatus != null and params.configStatus != ''">
            AND config_status = #{params.configStatus}
        </if>
    </select>

    <!-- 统计委托总金额 -->
    <select id="sumEntrustedAmount" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(entrusted_amount), 0) FROM case_pool
        WHERE deleted = 0
        <if test="params.clientName != null and params.clientName != ''">
            AND client_name LIKE CONCAT('%', #{params.clientName}, '%')
        </if>
        <if test="params.configStatus != null and params.configStatus != ''">
            AND config_status = #{params.configStatus}
        </if>
    </select>

    <!-- 批量锁定案件 -->
    <update id="batchLockCases">
        UPDATE case_pool SET config_status = '已锁定', update_time = NOW()
        WHERE client_id IN
        <foreach collection="clientIds" item="clientId" open="(" separator="," close=")">
            #{clientId}
        </foreach>
        AND deleted = 0
    </update>

    <!-- 批量解锁案件 -->
    <update id="batchUnlockCases">
        UPDATE case_pool SET config_status = '未锁定', update_time = NOW()
        WHERE client_id IN
        <foreach collection="clientIds" item="clientId" open="(" separator="," close=")">
            #{clientId}
        </foreach>
        AND deleted = 0
    </update>

    <!-- 批量分派案件 -->
    <update id="batchAssignCases">
        UPDATE case_pool SET 
            config_status = '已锁定',
            update_time = NOW()
        WHERE client_id IN
        <foreach collection="clientIds" item="clientId" open="(" separator="," close=")">
            #{clientId}
        </foreach>
        AND deleted = 0
    </update>

    <!-- 根据客户索引号查询案件 -->
    <select id="selectByClientId" resultMap="BaseResultMap">
        SELECT * FROM case_pool
        WHERE client_id = #{clientId} AND deleted = 0
        LIMIT 1
    </select>

    <!-- 获取案池统计数据 -->
    <select id="getCasePoolStatistics" resultType="java.util.Map">
        SELECT 
            COUNT(*) as totalCount,
            COALESCE(SUM(entrusted_amount), 0) as totalAmount,
            COUNT(CASE WHEN config_status = '已锁定' THEN 1 END) as lockedCount,
            COUNT(CASE WHEN config_status = '未锁定' THEN 1 END) as unlockedCount
        FROM case_pool
        WHERE deleted = 0
    </select>

    <!-- 根据配置状态统计案件数量 -->
    <select id="countByConfigStatus" resultType="java.util.Map">
        SELECT 
            config_status as status,
            COUNT(*) as count
        FROM case_pool
        WHERE deleted = 0
        GROUP BY config_status
    </select>

    <!-- 根据案件类型统计案件数量 -->
    <select id="countByCaseType" resultType="java.util.Map">
        SELECT 
            case_type as type,
            COUNT(*) as count
        FROM case_pool
        WHERE deleted = 0 AND case_type IS NOT NULL
        GROUP BY case_type
    </select>

    <!-- 根据城市统计案件数量 -->
    <select id="countByCity" resultType="java.util.Map">
        SELECT 
            city,
            COUNT(*) as count
        FROM case_pool
        WHERE deleted = 0 AND city IS NOT NULL
        GROUP BY city
        ORDER BY count DESC
        LIMIT 10
    </select>

</mapper>
