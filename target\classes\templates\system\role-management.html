<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>角色管理 - CF系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        .table-container {
            max-height: 600px;
            overflow-y: auto;
        }
        .modal {
            display: none;
        }
        .modal.show {
            display: flex;
        }
        .animate-fadeIn {
            animation: fadeIn 0.3s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .tree-node {
            margin-left: 20px;
        }
        .tree-checkbox {
            margin-right: 8px;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- 主容器 -->
    <div class="container mx-auto px-4 py-6">
        <!-- 页面标题 -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">角色管理</h1>
                    <p class="text-gray-600 mt-1">管理系统角色和权限</p>
                </div>
                <button onclick="openAddRoleModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
                    <i class="fas fa-plus"></i>
                    新增角色
                </button>
            </div>
        </div>

        <!-- 搜索和筛选 -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">角色名称</label>
                    <input type="text" id="searchRoleName" placeholder="请输入角色名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">角色编码</label>
                    <input type="text" id="searchRoleCode" placeholder="请输入角色编码" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">状态</label>
                    <select id="searchStatus" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部状态</option>
                        <option value="1">启用</option>
                        <option value="0">禁用</option>
                    </select>
                </div>
                <div class="flex items-end gap-2">
                    <button onclick="searchRoles()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors">
                        <i class="fas fa-search mr-2"></i>搜索
                    </button>
                    <button onclick="resetSearch()" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition-colors">
                        <i class="fas fa-refresh mr-2"></i>重置
                    </button>
                </div>
            </div>
        </div>

        <!-- 角色列表 -->
        <div class="bg-white rounded-lg shadow-sm">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h2 class="text-lg font-semibold text-gray-800">角色列表</h2>
                    <div class="flex items-center gap-2">
                        <button onclick="batchDeleteRoles()" class="bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-md text-sm transition-colors" disabled id="batchDeleteBtn">
                            <i class="fas fa-trash mr-2"></i>批量删除
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="table-container">
                <table class="w-full">
                    <thead class="bg-gray-50 sticky top-0">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()" class="rounded">
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">角色名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">角色编码</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">排序</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">备注</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody id="roleTableBody" class="bg-white divide-y divide-gray-200">
                        <!-- 角色数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        显示第 <span id="pageStart">1</span> 到 <span id="pageEnd">10</span> 条，共 <span id="totalCount">0</span> 条记录
                    </div>
                    <div class="flex items-center gap-2">
                        <button onclick="previousPage()" id="prevBtn" class="px-3 py-2 text-sm bg-gray-200 text-gray-600 rounded-md hover:bg-gray-300 transition-colors" disabled>
                            上一页
                        </button>
                        <div id="pageNumbers" class="flex gap-1">
                            <!-- 页码按钮将动态生成 -->
                        </div>
                        <button onclick="nextPage()" id="nextBtn" class="px-3 py-2 text-sm bg-gray-200 text-gray-600 rounded-md hover:bg-gray-300 transition-colors">
                            下一页
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增/编辑角色模态框 -->
    <div id="roleModal" class="modal fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 animate-fadeIn">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 id="modalTitle" class="text-lg font-semibold text-gray-800">新增角色</h3>
            </div>
            <form id="roleForm" class="p-6">
                <input type="hidden" id="roleId">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">角色名称 <span class="text-red-500">*</span></label>
                        <input type="text" id="roleName" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">角色编码 <span class="text-red-500">*</span></label>
                        <input type="text" id="roleCode" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">排序</label>
                        <input type="number" id="sortOrder" value="0" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">状态</label>
                        <select id="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="1">启用</option>
                            <option value="0">禁用</option>
                        </select>
                    </div>
                </div>
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">备注</label>
                    <textarea id="remark" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                </div>
            </form>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end gap-3">
                <button onclick="closeRoleModal()" class="px-4 py-2 text-gray-600 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors">
                    取消
                </button>
                <button onclick="saveRole()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                    保存
                </button>
            </div>
        </div>
    </div>

    <!-- 菜单权限分配模态框 -->
    <div id="menuModal" class="modal fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-3xl mx-4 animate-fadeIn">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">分配菜单权限</h3>
            </div>
            <div class="p-6">
                <input type="hidden" id="menuRoleId">
                <div class="mb-4">
                    <p class="text-sm text-gray-600">角色：<span id="menuRoleName" class="font-medium"></span></p>
                </div>
                <div class="border border-gray-200 rounded-md p-4 max-h-96 overflow-y-auto">
                    <div class="mb-4">
                        <label class="flex items-center">
                            <input type="checkbox" id="selectAllMenus" onchange="toggleSelectAllMenus()" class="tree-checkbox rounded">
                            <span class="font-medium">全选/取消全选</span>
                        </label>
                    </div>
                    <div id="menuTree" class="space-y-2">
                        <!-- 菜单树将动态加载 -->
                    </div>
                </div>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end gap-3">
                <button onclick="closeMenuModal()" class="px-4 py-2 text-gray-600 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors">
                    取消
                </button>
                <button onclick="saveRoleMenus()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                    保存
                </button>
            </div>
        </div>
    </div>

    <script th:src="@{/js/role-management.js}"></script>
</body>
</html>