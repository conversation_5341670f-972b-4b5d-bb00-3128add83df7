-- 案池管理表
CREATE TABLE `case_pool` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '案池ID',
  `client_name` varchar(100) NOT NULL COMMENT '客户姓名',
  `client_id` varchar(50) NOT NULL COMMENT '客户索引号',
  `cardholder_code` varchar(50) DEFAULT NULL COMMENT '持卡人代码',
  `entrusted_amount` decimal(15,2) DEFAULT NULL COMMENT '委托金额',
  `entrusted_principal` decimal(15,2) DEFAULT NULL COMMENT '委托本金',
  `batch_number` varchar(50) DEFAULT NULL COMMENT '批次号',
  `pool_count` int(11) DEFAULT 1 COMMENT '入案池次数',
  `unfollowed_days` int(11) DEFAULT 0 COMMENT '未跟进天数',
  `config_status` varchar(20) DEFAULT '未锁定' COMMENT '配置状态(已锁定/未锁定)',
  `balance_ops` decimal(15,2) DEFAULT NULL COMMENT '余额OPS',
  `principal_ops` decimal(15,2) DEFAULT NULL COMMENT '本金OPS',
  `case_type` varchar(50) DEFAULT NULL COMMENT '案件类型',
  `special_type` varchar(50) DEFAULT NULL COMMENT '专项类型',
  `repair_result` varchar(20) DEFAULT NULL COMMENT '失联修复结果',
  `entrust_start_date` date DEFAULT NULL COMMENT '委托开始日',
  `entrust_end_date` date DEFAULT NULL COMMENT '委托结束日',
  `retention_count` int(11) DEFAULT 0 COMMENT '保留次数',
  `card_opening_date` date DEFAULT NULL COMMENT '开卡日期',
  `credit_limit` decimal(15,2) DEFAULT NULL COMMENT '信用额度',
  `last_payment_date` date DEFAULT NULL COMMENT '最后缴款日期',
  `overdue_period_at_entrust` varchar(20) DEFAULT NULL COMMENT '委托时逾期时段',
  `target_period` varchar(20) DEFAULT NULL COMMENT '目标时段',
  `city` varchar(50) DEFAULT NULL COMMENT '分案城市',
  `last_followup_date` date DEFAULT NULL COMMENT '最后跟进日期',
  `case_flag` varchar(20) DEFAULT NULL COMMENT '新旧案标志',
  `current_overdue_period` varchar(20) DEFAULT NULL COMMENT '当前逾期时段',
  `residence_city` varchar(50) DEFAULT NULL COMMENT '户籍城市',
  `client_age` int(11) DEFAULT NULL COMMENT '客户年龄',
  `occupation` varchar(50) DEFAULT NULL COMMENT '职业',
  `account_last_7` varchar(10) DEFAULT NULL COMMENT '账户号后7位',
  `month_range_at_entrust` varchar(20) DEFAULT NULL COMMENT '委托时月龄分档',
  `amount_range_at_entrust` varchar(20) DEFAULT NULL COMMENT '委托时金额段',
  `commission_range_at_entrust` varchar(20) DEFAULT NULL COMMENT '委托时佣金分档',
  `rating` varchar(20) DEFAULT NULL COMMENT '评分档',
  `is_litigation` varchar(10) DEFAULT '否' COMMENT '是否诉讼(含风险代理)',
  `current_month_payment` decimal(15,2) DEFAULT 0.00 COMMENT '客户当月主动还款金额',
  `previous_day_payment` decimal(15,2) DEFAULT 0.00 COMMENT '客户前日主动还款金额',
  `installment_status` varchar(20) DEFAULT NULL COMMENT '个性化分期状态',
  `installment_performance` varchar(20) DEFAULT NULL COMMENT '个性化分期履约状态',
  `complaint_tag` varchar(10) DEFAULT '否' COMMENT '投诉标签',
  `installment_performance_tag` varchar(10) DEFAULT '否' COMMENT '个分履约标签',
  `litigation_tag` varchar(20) DEFAULT '无诉讼' COMMENT '诉讼标签',
  `voice_tag` varchar(10) DEFAULT '否' COMMENT '智能语音标签',
  `special_tag` varchar(10) DEFAULT '否' COMMENT '专项标签',
  `is_litigation_commission` varchar(10) DEFAULT '否' COMMENT '是否诉讼(结佣)',
  `institution` varchar(100) DEFAULT NULL COMMENT '委托机构',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '删除标记(0:正常,1:删除)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_client_id` (`client_id`),
  KEY `idx_client_name` (`client_name`),
  KEY `idx_batch_number` (`batch_number`),
  KEY `idx_city` (`city`),
  KEY `idx_config_status` (`config_status`),
  KEY `idx_case_type` (`case_type`),
  KEY `idx_entrust_date` (`entrust_start_date`, `entrust_end_date`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='案池管理表';

-- 插入测试数据
INSERT INTO `case_pool` (
  `client_name`, `client_id`, `cardholder_code`, `entrusted_amount`, `entrusted_principal`,
  `batch_number`, `pool_count`, `unfollowed_days`, `config_status`, `balance_ops`, `principal_ops`,
  `case_type`, `special_type`, `repair_result`, `entrust_start_date`, `entrust_end_date`,
  `retention_count`, `card_opening_date`, `credit_limit`, `last_payment_date`,
  `overdue_period_at_entrust`, `target_period`, `city`, `last_followup_date`, `case_flag`,
  `current_overdue_period`, `residence_city`, `client_age`, `occupation`, `account_last_7`,
  `month_range_at_entrust`, `amount_range_at_entrust`, `commission_range_at_entrust`, `rating`,
  `is_litigation`, `current_month_payment`, `previous_day_payment`, `installment_status`,
  `installment_performance`, `complaint_tag`, `installment_performance_tag`, `litigation_tag`,
  `voice_tag`, `special_tag`, `is_litigation_commission`, `institution`
) VALUES 
('张三', 'ID1001', 'CM0001', 25000.00, 22000.00, 'B202401001', 1, 5, '未锁定', 23500.00, 21000.00,
 '信用卡逾期', '常规', '成功', '2024-01-01', '2024-12-31', 0, '2020-05-15', 50000.00, '2023-12-20',
 '3期', '1期', '北京', '2024-01-15', '新案', '4期', '北京', 35, '工程师', '1234567',
 '1-12月', '2万-5万', '3档', '4星', '否', 1000.00, 500.00, '正常', '正常',
 '否', '否', '无诉讼', '否', '否', '否', '某银行'),
('李四', 'ID1002', 'CM0002', 18000.00, 16000.00, 'B202401002', 2, 10, '已锁定', 17200.00, 15500.00,
 '消费贷款', '专项', '失败', '2024-01-02', '2024-12-31', 1, '2019-08-20', 30000.00, '2023-11-15',
 '2期', '2期', '上海', '2024-01-10', '旧案', '3期', '上海', 28, '教师', '2345678',
 '6-18月', '1万-3万', '2档', '3星', '是', 800.00, 300.00, '逾期', '逾期',
 '是', '是', '诉讼中', '是', '是', '是', '某金融公司'),
('王五', 'ID1003', 'CM0003', 35000.00, 32000.00, 'B202401003', 1, 3, '未锁定', 33800.00, 31000.00,
 '房贷逾期', '常规', '未申请', '2024-01-03', '2024-12-31', 0, '2018-12-10', 80000.00, '2023-12-25',
 '1期', '1期', '广州', '2024-01-18', '新案', '2期', '广州', 42, '医生', '3456789',
 '1-6月', '3万-8万', '4档', '5星', '否', 1500.00, 800.00, '正常', '正常',
 '否', '否', '无诉讼', '否', '否', '否', '某银行'),
('赵六', 'ID1004', 'CM0004', 12000.00, 10000.00, 'B202401004', 3, 15, '已锁定', 11500.00, 9800.00,
 '其他类型', '专项', '成功', '2024-01-04', '2024-12-31', 2, '2021-03-05', 20000.00, '2023-10-30',
 '4期', '3期', '深圳', '2024-01-05', '旧案', '5期', '深圳', 31, '销售', '4567890',
 '12-24月', '1万以下', '1档', '2星', '是', 600.00, 200.00, '未申请', '未申请',
 '否', '否', '诉讼中', '否', '是', '是', '某小贷公司'),
('钱七', 'ID1005', 'CM0005', 28000.00, 25000.00, 'B202401005', 1, 7, '未锁定', 26800.00, 24000.00,
 '信用卡逾期', '常规', '失败', '2024-01-05', '2024-12-31', 0, '2020-11-18', 60000.00, '2023-12-18',
 '2期', '1期', '杭州', '2024-01-12', '新案', '3期', '杭州', 38, '经理', '5678901',
 '3-9月', '2万-5万', '3档', '4星', '否', 1200.00, 600.00, '正常', '正常',
 '否', '否', '无诉讼', '否', '否', '否', '某银行');
