package com.cf.financing.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cf.financing.entity.SysRole;
import com.cf.financing.entity.SysUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 系统用户Mapper接口
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Mapper
public interface SysUserMapper extends BaseMapper<SysUser> {

    /**
 * 分页查询用户信息
 *
 * @param page 分页参数
 * @param username 用户名
 * @param realName 真实姓名
 * @param phone 手机号
 * @param email 邮箱
 * @param status 状态
 * @param deptId 部门ID
 * @param roleId 角色ID
 * @return 用户分页数据
 */
    IPage<SysUser> selectUserPage(
            Page<SysUser> page,
            @Param("username") String username,
            @Param("realName") String realName,
            @Param("phone") String phone,
            @Param("email") String email,
            @Param("status") String status,
            @Param("deptId") Long deptId,
            @Param("roleId") Long roleId
    );

    /**
 * 根据用户名查询用户详细信息
 *
 * @param username 用户名
 * @return 用户信息
 */
    SysUser selectUserByUsername(@Param("username") String username);

    /**
 * 根据用户ID查询用户详细信息
 *
 * @param userId 用户ID
 * @return 用户信息
 */
    SysUser selectUserById(@Param("userId") Long userId);

    /**
 * 查询部门下的所有用户
 *
 * @param deptId 部门ID
 * @return 用户列表
 */
    List<SysUser> selectUsersByDeptId(@Param("deptId") Long deptId);

    /**
 * 查询角色下的所有用户
 *
 * @param roleId 角色ID
 * @return 用户列表
 */
    List<SysUser> selectUsersByRoleId(@Param("roleId") Long roleId);

    /**
 * 更新用户登录信息
 *
 * @param userId 用户ID
 * @param loginIp 登录IP
 * @return 更新结果
 */
    int updateLoginInfo(@Param("userId") Long userId, @Param("loginIp") String loginIp);

    /**
 * 重置用户密码
 *
 * @param userId 用户ID
 * @param newPassword 新密码
 * @return 更新结果
 */
    int resetPassword(@Param("userId") Long userId, @Param("newPassword") String newPassword);

    /**
 * 批量更新用户状态
 *
 * @param userIds 用户ID列表
 * @param status 状态
 * @return 更新结果
 */
    int batchUpdateStatus(@Param("userIds") List<Long> userIds, @Param("status") String status);

    /**
 * 获取用户统计信息
 *
 * @param deptId 部门ID（可选）
 * @return 统计信息
 */
    Map<String, Object> selectUserStatistics(@Param("deptId") Long deptId);

    /**
 * 检查用户名是否存在
 *
 * @param username 用户名
 * @param excludeUserId 排除的用户ID
 * @return 存在数量
 */
    int checkUsernameExists(@Param("username") String username, @Param("excludeUserId") Long excludeUserId);

    /**
 * 检查手机号是否存在
 *
 * @param phone 手机号
 * @param excludeUserId 排除的用户ID
 * @return 存在数量
 */
    int checkPhoneExists(@Param("phone") String phone, @Param("excludeUserId") Long excludeUserId);

    /**
 * 检查邮箱是否存在
 *
 * @param email 邮箱
 * @param excludeUserId 排除的用户ID
 * @return 存在数量
 */
    int checkEmailExists(@Param("email") String email, @Param("excludeUserId") Long excludeUserId);

    /**
 * 获取在线用户列表
 *
 * @return 在线用户列表
 */
    List<SysUser> selectOnlineUsers();

    /**
     * 根据用户ID查询角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<SysRole> selectRolesByUserId(@Param("userId") Long userId);
}