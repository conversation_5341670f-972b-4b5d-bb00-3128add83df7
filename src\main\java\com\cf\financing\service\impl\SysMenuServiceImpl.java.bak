package com.cf.financing.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cf.financing.entity.SysMenu;
import com.cf.financing.mapper.SysMenuMapper;
import com.cf.financing.mapper.SysRoleMenuMapper;
import com.cf.financing.service.ISysMenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 系统菜单服务实现类
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Service
public class SysMenuServiceImpl extends ServiceImpl<SysMenuMapper, SysMenu> implements ISysMenuService {

    @Autowired
    private SysMenuMapper menuMapper;

    @Autowired
    private SysRoleMenuMapper roleMenuMapper;

    @Override
    public IPage<SysMenu> selectMenuPage(Page<SysMenu> page, String menuName, String menuCode, Integer status, Integer menuType) {
        return menuMapper.selectMenuPage(page, menuName, menuCode, status, menuType);
    }

    @Override
    public List<SysMenu> selectMenuTree(Integer status) {
        List<SysMenu> menus = menuMapper.selectMenuTree(status);
        return buildMenuTree(menus);
    }

    @Override
    public List<SysMenu> selectMenusByUserId(Long userId) {
        return menuMapper.selectMenusByUserId(userId);
    }

    @Override
    public List<SysMenu> selectMenusByRoleId(Long roleId) {
        return menuMapper.selectMenusByRoleId(roleId);
    }

    @Override
    public List<String> selectPermissionsByUserId(Long userId) {
        return menuMapper.selectPermissionsByUserId(userId);
    }

    @Override
    public List<SysMenu> selectUserMenuTree(Long userId) {
        List<SysMenu> menus = menuMapper.selectUserMenuTree(userId);
        return buildMenuTree(menus);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertMenu(SysMenu menu) {
        // 检查菜单编码是否存在
        if (checkMenuCodeExists(menu.getMenuCode(), null)) {
            throw new RuntimeException("菜单编码已存在");
        }
        
        // 检查菜单名称是否存在（同级别下）
        if (checkMenuNameExists(menu.getMenuName(), menu.getParentId(), null)) {
            throw new RuntimeException("同级别下菜单名称已存在");
        }
        
        menu.setCreateTime(LocalDateTime.now());
        menu.setDeleted(0);
        
        return save(menu);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMenu(SysMenu menu) {
        // 检查菜单编码是否存在
        if (checkMenuCodeExists(menu.getMenuCode(), menu.getId())) {
            throw new RuntimeException("菜单编码已存在");
        }
        
        // 检查菜单名称是否存在（同级别下）
        if (checkMenuNameExists(menu.getMenuName(), menu.getParentId(), menu.getId())) {
            throw new RuntimeException("同级别下菜单名称已存在");
        }
        
        // 不能将父菜单设置为自己或自己的子菜单
        if (menu.getParentId() != null && menu.getParentId().equals(menu.getId())) {
            throw new RuntimeException("不能将父菜单设置为自己");
        }
        
        menu.setUpdateTime(LocalDateTime.now());
        
        return updateById(menu);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMenu(Long menuId) {
        // 检查是否存在子菜单
        if (hasChildMenus(menuId)) {
            throw new RuntimeException("存在子菜单，无法删除");
        }
        
        // 检查菜单是否被角色使用
        if (isMenuInUse(menuId)) {
            throw new RuntimeException("菜单正在使用中，无法删除");
        }
        
        // 删除角色菜单关联
        roleMenuMapper.deleteByMenuId(menuId);
        
        // 删除菜单
        return removeById(menuId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMenus(List<Long> menuIds) {
        if (CollectionUtils.isEmpty(menuIds)) {
            return false;
        }
        
        // 检查是否存在子菜单
        for (Long menuId : menuIds) {
            if (hasChildMenus(menuId)) {
                throw new RuntimeException("存在子菜单，无法删除");
            }
            if (isMenuInUse(menuId)) {
                throw new RuntimeException("存在正在使用的菜单，无法删除");
            }
        }
        
        // 删除角色菜单关联
        roleMenuMapper.deleteByMenuIds(menuIds);
        
        // 删除菜单
        return removeByIds(menuIds);
    }

    @Override
    public boolean checkMenuCodeExists(String menuCode, Long excludeId) {
        return menuMapper.checkMenuCodeExists(menuCode, excludeId) > 0;
    }

    @Override
    public boolean checkMenuNameExists(String menuName, Long parentId, Long excludeId) {
        return menuMapper.checkMenuNameExists(menuName, parentId, excludeId) > 0;
    }

    @Override
    public List<SysMenu> selectMenusByParentId(Long parentId) {
        return menuMapper.selectMenusByParentId(parentId);
    }

    @Override
    public boolean hasChildMenus(Long menuId) {
        return menuMapper.hasChildMenus(menuId) > 0;
    }

    @Override
    public boolean isMenuInUse(Long menuId) {
        return menuMapper.countRolesByMenuId(menuId) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMenuStatus(Long menuId, Integer status, Long operatorId) {
        SysMenu menu = new SysMenu();
        menu.setId(menuId);
        menu.setStatus(status);
        menu.setUpdateBy(operatorId);
        menu.setUpdateTime(LocalDateTime.now());
        
        return updateById(menu);
    }

    @Override
    public List<SysMenu> selectParentMenus() {
        return menuMapper.selectParentMenus();
    }

    @Override
    public SysMenu selectByPath(String path) {
        return menuMapper.selectByPath(path);
    }

    @Override
    public List<SysMenu> buildMenuTree(List<SysMenu> menus) {
        if (CollectionUtils.isEmpty(menus)) {
            return new ArrayList<>();
        }
        
        // 构建菜单映射
        Map<Long, SysMenu> menuMap = menus.stream()
                .collect(Collectors.toMap(SysMenu::getId, menu -> menu));
        
        // 构建树形结构
        List<SysMenu> rootMenus = new ArrayList<>();
        
        for (SysMenu menu : menus) {
            if (menu.getParentId() == null || menu.getParentId() == 0) {
                // 根菜单
                rootMenus.add(menu);
            } else {
                // 子菜单
                SysMenu parentMenu = menuMap.get(menu.getParentId());
                if (parentMenu != null) {
                    if (parentMenu.getChildren() == null) {
                        parentMenu.setChildren(new ArrayList<>());
                    }
                    parentMenu.getChildren().add(menu);
                }
            }
        }
        
        // 排序
        sortMenuTree(rootMenus);
        
        return rootMenus;
    }

    /**
     * 递归排序菜单树
     */
    private void sortMenuTree(List<SysMenu> menus) {
        if (CollectionUtils.isEmpty(menus)) {
            return;
        }
        
        // 按排序字段排序
        menus.sort(Comparator.comparing(SysMenu::getSortOrder, Comparator.nullsLast(Integer::compareTo)));
        
        // 递归排序子菜单
        for (SysMenu menu : menus) {
            if (!CollectionUtils.isEmpty(menu.getChildren())) {
                sortMenuTree(menu.getChildren());
            }
        }
    }

    @Override
    public Object getMenuStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        // 总菜单数
        long totalCount = count();
        statistics.put("totalCount", totalCount);
        
        // 目录数量
        long directoryCount = count(new LambdaQueryWrapper<SysMenu>()
                .eq(SysMenu::getMenuType, 0)
                .eq(SysMenu::getDeleted, 0));
        statistics.put("directoryCount", directoryCount);
        
        // 菜单数量
        long menuCount = count(new LambdaQueryWrapper<SysMenu>()
                .eq(SysMenu::getMenuType, 1)
                .eq(SysMenu::getDeleted, 0));
        statistics.put("menuCount", menuCount);
        
        // 按钮数量
        long buttonCount = count(new LambdaQueryWrapper<SysMenu>()
                .eq(SysMenu::getMenuType, 2)
                .eq(SysMenu::getDeleted, 0));
        statistics.put("buttonCount", buttonCount);
        
        // 启用菜单数
        long enabledCount = count(new LambdaQueryWrapper<SysMenu>()
                .eq(SysMenu::getStatus, 1)
                .eq(SysMenu::getDeleted, 0));
        statistics.put("enabledCount", enabledCount);
        
        // 禁用菜单数
        long disabledCount = count(new LambdaQueryWrapper<SysMenu>()
                .eq(SysMenu::getStatus, 0)
                .eq(SysMenu::getDeleted, 0));
        statistics.put("disabledCount", disabledCount);
        
        return statistics;
    }

    @Override
    public List<SysMenu> exportMenus(String menuName, String menuCode, Integer status, Integer menuType) {
        LambdaQueryWrapper<SysMenu> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysMenu::getDeleted, 0);
        
        if (StringUtils.hasText(menuName)) {
            wrapper.like(SysMenu::getMenuName, menuName);
        }
        if (StringUtils.hasText(menuCode)) {
            wrapper.like(SysMenu::getMenuCode, menuCode);
        }
        if (status != null) {
            wrapper.eq(SysMenu::getStatus, status);
        }
        if (menuType != null) {
            wrapper.eq(SysMenu::getMenuType, menuType);
        }
        
        wrapper.orderByAsc(SysMenu::getSortOrder);
        
        return list(wrapper);
    }

    @Override
    public void refreshMenuCache() {
        // 这里可以实现菜单缓存刷新逻辑
        // 例如清除Redis缓存等
        log.info("菜单缓存已刷新");
    }
}