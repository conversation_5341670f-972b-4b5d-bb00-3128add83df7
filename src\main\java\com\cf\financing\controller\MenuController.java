package com.cf.financing.controller;

import com.cf.financing.common.Result;
import com.cf.financing.entity.SysMenu;
import com.cf.financing.service.ISysMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpSession;
import java.util.List;

/**
 * 菜单控制器 - 用于前端动态菜单加载
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Api(tags = "菜单管理")
@RestController
@RequestMapping("/api/menu")
public class MenuController {

    private static final Logger log = LoggerFactory.getLogger(MenuController.class);

    @Autowired
    private ISysMenuService menuService;

    /**
     * 获取当前用户的菜单树
     */
    @ApiOperation("获取当前用户的菜单树")
    @GetMapping("/user-menus")
    public Result<List<SysMenu>> getUserMenus(HttpSession session) {
        try {
            // 从session中获取用户ID，如果没有则使用默认值1L（超级管理员）
            Long userId = (Long) session.getAttribute("userId");
            if (userId == null) {
                userId = 1L; // 默认超级管理员
            }
            
            List<SysMenu> menuTree = menuService.selectUserMenuTree(userId);
            return Result.success(menuTree);
        } catch (Exception e) {
            log.error("获取用户菜单失败", e);
            return Result.error("获取菜单失败");
        }
    }

    /**
     * 获取菜单树（用于角色授权）
     */
    @ApiOperation("获取菜单树")
    @GetMapping("/tree")
    public Result<List<SysMenu>> getMenuTree() {
        try {
            List<SysMenu> menuTree = menuService.selectMenuTree(1); // 只获取启用的菜单
            return Result.success(menuTree);
        } catch (Exception e) {
            log.error("获取菜单树失败", e);
            return Result.error("获取菜单树失败");
        }
    }

    /**
     * 根据角色ID获取菜单权限
     */
    @ApiOperation("根据角色ID获取菜单权限")
    @GetMapping("/role/{roleId}")
    public Result<List<Long>> getRoleMenus(@PathVariable Long roleId) {
        try {
            List<Long> menuIds = menuService.selectMenuIdsByRoleId(roleId);
            return Result.success(menuIds);
        } catch (Exception e) {
            log.error("获取角色菜单权限失败", e);
            return Result.error("获取角色菜单权限失败");
        }
    }
}
