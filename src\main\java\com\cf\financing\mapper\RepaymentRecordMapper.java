package com.cf.financing.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cf.financing.entity.RepaymentRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 还款记录Mapper接口
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Mapper
public interface RepaymentRecordMapper extends BaseMapper<RepaymentRecord> {

    /**
     * 分页查询还款记录
     *
     * @param page 分页参数
     * @param params 查询参数
     * @return 分页结果
     */
    IPage<RepaymentRecord> selectRepaymentPage(Page<RepaymentRecord> page, @Param("params") Map<String, Object> params);

    /**
     * 统计还款总数
     *
     * @param params 查询参数
     * @return 总数
     */
    Long countRepayment(@Param("params") Map<String, Object> params);

    /**
     * 统计还款总金额
     *
     * @param params 查询参数
     * @return 总金额
     */
    BigDecimal sumRepaymentAmount(@Param("params") Map<String, Object> params);

    /**
     * 根据客户索引号查询还款记录
     *
     * @param clientId 客户索引号
     * @return 还款记录列表
     */
    List<RepaymentRecord> selectByClientId(@Param("clientId") String clientId);

    /**
     * 批量审核还款记录
     *
     * @param ids 还款记录ID列表
     * @param auditStatus 审核状态
     * @param auditorId 审核人ID
     * @param auditRemark 审核意见
     * @return 影响行数
     */
    int batchAuditRepayment(@Param("ids") List<Long> ids, @Param("auditStatus") String auditStatus,
                           @Param("auditorId") Long auditorId, @Param("auditRemark") String auditRemark);

    /**
 * 根据案件ID查询还款记录
 *
 * @param caseId 案件ID
 * @return 还款记录列表
 */
    List<RepaymentRecord> selectRepaymentsByCaseId(@Param("caseId") Long caseId);

    /**
 * 根据客户ID查询还款记录
 *
 * @param customerId 客户ID
 * @return 还款记录列表
 */
    List<RepaymentRecord> selectRepaymentsByCustomerId(@Param("customerId") Long customerId);

    /**
 * 根据还款编号查询还款记录
 *
 * @param repaymentNo 还款编号
 * @return 还款记录
 */
    RepaymentRecord selectRepaymentByNo(@Param("repaymentNo") String repaymentNo);

    /**
 * 根据交易流水号查询还款记录
 *
 * @param transactionNo 交易流水号
 * @return 还款记录
 */
    RepaymentRecord selectRepaymentByTransactionNo(@Param("transactionNo") String transactionNo);

    /**
 * 获取案件的总还款金额
 *
 * @param caseId 案件ID
 * @return 总还款金额
 */
    BigDecimal selectTotalRepaymentByCaseId(@Param("caseId") Long caseId);

    /**
 * 获取客户的总还款金额
 *
 * @param customerId 客户ID
 * @return 总还款金额
 */
    BigDecimal selectTotalRepaymentByCustomerId(@Param("customerId") Long customerId);

    /**
 * 获取还款统计信息
 *
 * @param operatorId 操作员ID（可选）
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @return 统计信息
 */
    Map<String, Object> selectRepaymentStatistics(
            @Param("operatorId") Long operatorId,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate
    );

    /**
 * 获取还款趋势数据
 *
 * @param operatorId 操作员ID（可选）
 * @param days 天数
 * @return 趋势数据
 */
    List<Map<String, Object>> selectRepaymentTrendData(
            @Param("operatorId") Long operatorId,
            @Param("days") Integer days
    );

    /**
 * 获取还款方式统计
 *
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @return 还款方式统计
 */
    List<Map<String, Object>> selectRepaymentTypeStats(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate
    );

    /**
 * 获取今日还款记录
 *
 * @param operatorId 操作员ID（可选）
 * @param repaymentDate 还款日期
 * @return 今日还款记录
 */
    List<RepaymentRecord> selectTodayRepayments(
            @Param("operatorId") Long operatorId,
            @Param("repaymentDate") LocalDate repaymentDate
    );

    /**
 * 获取待确认的还款记录
 *
 * @param operatorId 操作员ID（可选）
 * @return 待确认还款记录
 */
    List<RepaymentRecord> selectPendingRepayments(@Param("operatorId") Long operatorId);

    /**
 * 批量更新还款状态
 *
 * @param repaymentIds 还款记录ID列表
 * @param repaymentStatus 还款状态
 * @param operatorId 操作员ID
 * @return 更新结果
 */
    int batchUpdateRepaymentStatus(
            @Param("repaymentIds") List<Long> repaymentIds,
            @Param("repaymentStatus") String repaymentStatus,
            @Param("operatorId") Long operatorId
    );

    /**
 * 检查还款编号是否存在
 *
 * @param repaymentNo 还款编号
 * @param excludeRepaymentId 排除的还款记录ID
 * @return 存在数量
 */
    int checkRepaymentNoExists(
            @Param("repaymentNo") String repaymentNo,
            @Param("excludeRepaymentId") Long excludeRepaymentId
    );

    /**
 * 检查交易流水号是否存在
 *
 * @param transactionNo 交易流水号
 * @param excludeRepaymentId 排除的还款记录ID
 * @return 存在数量
 */
    int checkTransactionNoExists(
            @Param("transactionNo") String transactionNo,
            @Param("excludeRepaymentId") Long excludeRepaymentId
    );

    /**
 * 获取月度还款统计
 *
 * @param months 月份数
 * @return 月度统计数据
 */
    List<Map<String, Object>> selectMonthlyRepaymentStats(@Param("months") Integer months);

    /**
 * 获取操作员还款业绩排行
 *
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @param limit 限制数量
 * @return 业绩排行
 */
    List<Map<String, Object>> selectOperatorRepaymentRanking(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Param("limit") Integer limit
    );

    /**
     * 获取今日还款统计
     *
     * @return 今日统计数据
     */
    Map<String, Object> selectTodayRepaymentStatistics();


}