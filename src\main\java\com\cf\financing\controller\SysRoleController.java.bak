package com.cf.financing.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cf.financing.common.Result;
import com.cf.financing.entity.SysRole;
import com.cf.financing.service.ISysRoleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 系统角色控制器
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Slf4j
@Api(tags = "系统角色管理")
@RestController
@RequestMapping("/api/system/role")
public class SysRoleController {

    @Autowired
    private ISysRoleService roleService;

    /**
     * 分页查询角色列表
     */
    @ApiOperation("分页查询角色列表")
    @GetMapping("/page")
    @PreAuthorize("hasAuthority('system:role:list')")
    public Result<IPage<SysRole>> getRolePage(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("角色名称") @RequestParam(required = false) String roleName,
            @ApiParam("角色编码") @RequestParam(required = false) String roleCode,
            @ApiParam("状态") @RequestParam(required = false) Integer status) {
        
        Page<SysRole> page = new Page<>(current, size);
        IPage<SysRole> result = roleService.selectRolePage(page, roleName, roleCode, status);
        
        return Result.success(result);
    }

    /**
     * 查询所有启用的角色
     */
    @ApiOperation("查询所有启用的角色")
    @GetMapping("/enabled")
    @PreAuthorize("hasAuthority('system:role:list')")
    public Result<List<SysRole>> getEnabledRoles() {
        List<SysRole> roles = roleService.selectEnabledRoles();
        return Result.success(roles);
    }

    /**
     * 根据ID查询角色详情
     */
    @ApiOperation("根据ID查询角色详情")
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('system:role:query')")
    public Result<SysRole> getRoleById(@ApiParam("角色ID") @PathVariable Long id) {
        SysRole role = roleService.getById(id);
        if (role == null) {
            return Result.error("角色不存在");
        }
        return Result.success(role);
    }

    /**
     * 新增角色
     */
    @ApiOperation("新增角色")
    @PostMapping
    @PreAuthorize("hasAuthority('system:role:add')")
    public Result<String> addRole(@Valid @RequestBody SysRole role) {
        try {
            boolean success = roleService.insertRole(role);
            if (success) {
                return Result.success("角色创建成功");
            } else {
                return Result.error("角色创建失败");
            }
        } catch (Exception e) {
            log.error("创建角色失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 修改角色
     */
    @ApiOperation("修改角色")
    @PutMapping
    @PreAuthorize("hasAuthority('system:role:edit')")
    public Result<String> updateRole(@Valid @RequestBody SysRole role) {
        try {
            boolean success = roleService.updateRole(role);
            if (success) {
                return Result.success("角色更新成功");
            } else {
                return Result.error("角色更新失败");
            }
        } catch (Exception e) {
            log.error("更新角色失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 删除角色
     */
    @ApiOperation("删除角色")
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('system:role:remove')")
    public Result<String> deleteRole(@ApiParam("角色ID") @PathVariable Long id) {
        try {
            boolean success = roleService.deleteRole(id);
            if (success) {
                return Result.success("角色删除成功");
            } else {
                return Result.error("角色删除失败");
            }
        } catch (Exception e) {
            log.error("删除角色失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 批量删除角色
     */
    @ApiOperation("批量删除角色")
    @DeleteMapping("/batch")
    @PreAuthorize("hasAuthority('system:role:remove')")
    public Result<String> deleteRoles(@RequestBody List<Long> ids) {
        try {
            boolean success = roleService.deleteRoles(ids);
            if (success) {
                return Result.success("角色批量删除成功");
            } else {
                return Result.error("角色批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除角色失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 修改角色状态
     */
    @ApiOperation("修改角色状态")
    @PutMapping("/{id}/status")
    @PreAuthorize("hasAuthority('system:role:edit')")
    public Result<String> updateRoleStatus(
            @ApiParam("角色ID") @PathVariable Long id,
            @ApiParam("状态") @RequestParam Integer status) {
        try {
            // 这里应该从当前登录用户获取操作人ID，暂时使用1L
            boolean success = roleService.updateRoleStatus(id, status, 1L);
            if (success) {
                return Result.success("角色状态更新成功");
            } else {
                return Result.error("角色状态更新失败");
            }
        } catch (Exception e) {
            log.error("更新角色状态失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 分配角色菜单权限
     */
    @ApiOperation("分配角色菜单权限")
    @PostMapping("/{id}/menus")
    @PreAuthorize("hasAuthority('system:role:auth')")
    public Result<String> assignRoleMenus(
            @ApiParam("角色ID") @PathVariable Long id,
            @RequestBody List<Long> menuIds) {
        try {
            // 这里应该从当前登录用户获取操作人ID，暂时使用1L
            boolean success = roleService.assignRoleMenus(id, menuIds, 1L);
            if (success) {
                return Result.success("角色权限分配成功");
            } else {
                return Result.error("角色权限分配失败");
            }
        } catch (Exception e) {
            log.error("分配角色权限失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 查询角色的菜单权限ID列表
     */
    @ApiOperation("查询角色的菜单权限ID列表")
    @GetMapping("/{id}/menus")
    @PreAuthorize("hasAuthority('system:role:query')")
    public Result<List<Long>> getRoleMenuIds(@ApiParam("角色ID") @PathVariable Long id) {
        List<Long> menuIds = roleService.selectMenuIdsByRoleId(id);
        return Result.success(menuIds);
    }

    /**
     * 检查角色编码是否存在
     */
    @ApiOperation("检查角色编码是否存在")
    @GetMapping("/check-code")
    @PreAuthorize("hasAuthority('system:role:query')")
    public Result<Boolean> checkRoleCodeExists(
            @ApiParam("角色编码") @RequestParam String roleCode,
            @ApiParam("排除的角色ID") @RequestParam(required = false) Long excludeId) {
        boolean exists = roleService.checkRoleCodeExists(roleCode, excludeId);
        return Result.success(exists);
    }

    /**
     * 检查角色名称是否存在
     */
    @ApiOperation("检查角色名称是否存在")
    @GetMapping("/check-name")
    @PreAuthorize("hasAuthority('system:role:query')")
    public Result<Boolean> checkRoleNameExists(
            @ApiParam("角色名称") @RequestParam String roleName,
            @ApiParam("排除的角色ID") @RequestParam(required = false) Long excludeId) {
        boolean exists = roleService.checkRoleNameExists(roleName, excludeId);
        return Result.success(exists);
    }

    /**
     * 获取角色统计信息
     */
    @ApiOperation("获取角色统计信息")
    @GetMapping("/statistics")
    @PreAuthorize("hasAuthority('system:role:query')")
    public Result<Object> getRoleStatistics() {
        Object statistics = roleService.getRoleStatistics();
        return Result.success(statistics);
    }

    /**
     * 导出角色数据
     */
    @ApiOperation("导出角色数据")
    @GetMapping("/export")
    @PreAuthorize("hasAuthority('system:role:export')")
    public Result<List<SysRole>> exportRoles(
            @ApiParam("角色名称") @RequestParam(required = false) String roleName,
            @ApiParam("角色编码") @RequestParam(required = false) String roleCode,
            @ApiParam("状态") @RequestParam(required = false) Integer status) {
        List<SysRole> roles = roleService.exportRoles(roleName, roleCode, status);
        return Result.success(roles);
    }

    /**
     * 复制角色
     */
    @ApiOperation("复制角色")
    @PostMapping("/{id}/copy")
    @PreAuthorize("hasAuthority('system:role:add')")
    public Result<String> copyRole(
            @ApiParam("源角色ID") @PathVariable Long id,
            @ApiParam("新角色名称") @RequestParam String newRoleName,
            @ApiParam("新角色编码") @RequestParam String newRoleCode) {
        try {
            // 这里应该从当前登录用户获取操作人ID，暂时使用1L
            boolean success = roleService.copyRole(id, newRoleName, newRoleCode, 1L);
            if (success) {
                return Result.success("角色复制成功");
            } else {
                return Result.error("角色复制失败");
            }
        } catch (Exception e) {
            log.error("复制角色失败", e);
            return Result.error(e.getMessage());
        }
    }
}