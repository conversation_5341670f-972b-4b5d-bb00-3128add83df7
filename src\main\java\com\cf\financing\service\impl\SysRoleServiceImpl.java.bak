package com.cf.financing.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cf.financing.entity.SysRole;
import com.cf.financing.mapper.SysRoleMapper;
import com.cf.financing.mapper.SysRoleMenuMapper;
import com.cf.financing.mapper.SysUserRoleMapper;
import com.cf.financing.service.ISysRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统角色服务实现类
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Service
public class SysRoleServiceImpl extends ServiceImpl<SysRoleMapper, SysRole> implements ISysRoleService {

    @Autowired
    private SysRoleMapper roleMapper;

    @Autowired
    private SysRoleMenuMapper roleMenuMapper;

    @Autowired
    private SysUserRoleMapper userRoleMapper;

    @Override
    public IPage<SysRole> selectRolePage(Page<SysRole> page, String roleName, String roleCode, Integer status) {
        return roleMapper.selectRolePage(page, roleName, roleCode, status);
    }

    @Override
    public List<SysRole> selectRolesByUserId(Long userId) {
        return roleMapper.selectRolesByUserId(userId);
    }

    @Override
    public List<SysRole> selectEnabledRoles() {
        return roleMapper.selectEnabledRoles();
    }

    @Override
    public SysRole selectByRoleCode(String roleCode) {
        return roleMapper.selectByRoleCode(roleCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertRole(SysRole role) {
        // 检查角色编码是否存在
        if (checkRoleCodeExists(role.getRoleCode(), null)) {
            throw new RuntimeException("角色编码已存在");
        }
        
        // 检查角色名称是否存在
        if (checkRoleNameExists(role.getRoleName(), null)) {
            throw new RuntimeException("角色名称已存在");
        }
        
        role.setCreateTime(LocalDateTime.now());
        role.setDeleted(0);
        
        return save(role);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRole(SysRole role) {
        // 检查角色编码是否存在
        if (checkRoleCodeExists(role.getRoleCode(), role.getId())) {
            throw new RuntimeException("角色编码已存在");
        }
        
        // 检查角色名称是否存在
        if (checkRoleNameExists(role.getRoleName(), role.getId())) {
            throw new RuntimeException("角色名称已存在");
        }
        
        role.setUpdateTime(LocalDateTime.now());
        
        return updateById(role);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRole(Long roleId) {
        // 检查角色是否被使用
        if (isRoleInUse(roleId)) {
            throw new RuntimeException("角色正在使用中，无法删除");
        }
        
        // 删除角色菜单关联
        roleMenuMapper.deleteByRoleId(roleId);
        
        // 删除角色
        return removeById(roleId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRoles(List<Long> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return false;
        }
        
        // 检查角色是否被使用
        for (Long roleId : roleIds) {
            if (isRoleInUse(roleId)) {
                throw new RuntimeException("存在正在使用的角色，无法删除");
            }
        }
        
        // 删除角色菜单关联
        roleMenuMapper.deleteByRoleIds(roleIds);
        
        // 删除角色
        return removeByIds(roleIds);
    }

    @Override
    public boolean checkRoleCodeExists(String roleCode, Long excludeId) {
        return roleMapper.checkRoleCodeExists(roleCode, excludeId) > 0;
    }

    @Override
    public boolean checkRoleNameExists(String roleName, Long excludeId) {
        return roleMapper.checkRoleNameExists(roleName, excludeId) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignRoleMenus(Long roleId, List<Long> menuIds, Long operatorId) {
        // 删除原有的角色菜单关联
        roleMenuMapper.deleteByRoleId(roleId);
        
        // 插入新的角色菜单关联
        if (!CollectionUtils.isEmpty(menuIds)) {
            return roleMenuMapper.insertRoleMenuBatch(roleId, menuIds, operatorId) > 0;
        }
        
        return true;
    }

    @Override
    public List<Long> selectMenuIdsByRoleId(Long roleId) {
        return roleMapper.selectMenuIdsByRoleId(roleId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRoleStatus(Long roleId, Integer status, Long operatorId) {
        SysRole role = new SysRole();
        role.setId(roleId);
        role.setStatus(status);
        role.setUpdateBy(operatorId);
        role.setUpdateTime(LocalDateTime.now());
        
        return updateById(role);
    }

    @Override
    public boolean isRoleInUse(Long roleId) {
        return roleMapper.countUsersByRoleId(roleId) > 0;
    }

    @Override
    public List<SysRole> selectRolesByIds(List<Long> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return null;
        }
        return roleMapper.selectRolesByIds(roleIds);
    }

    @Override
    public Object getRoleStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        // 总角色数
        long totalCount = count();
        statistics.put("totalCount", totalCount);
        
        // 启用角色数
        long enabledCount = count(new LambdaQueryWrapper<SysRole>()
                .eq(SysRole::getStatus, 1)
                .eq(SysRole::getDeleted, 0));
        statistics.put("enabledCount", enabledCount);
        
        // 禁用角色数
        long disabledCount = count(new LambdaQueryWrapper<SysRole>()
                .eq(SysRole::getStatus, 0)
                .eq(SysRole::getDeleted, 0));
        statistics.put("disabledCount", disabledCount);
        
        return statistics;
    }

    @Override
    public List<SysRole> exportRoles(String roleName, String roleCode, Integer status) {
        LambdaQueryWrapper<SysRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRole::getDeleted, 0);
        
        if (StringUtils.hasText(roleName)) {
            wrapper.like(SysRole::getRoleName, roleName);
        }
        if (StringUtils.hasText(roleCode)) {
            wrapper.like(SysRole::getRoleCode, roleCode);
        }
        if (status != null) {
            wrapper.eq(SysRole::getStatus, status);
        }
        
        wrapper.orderByAsc(SysRole::getSortOrder);
        
        return list(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean copyRole(Long sourceRoleId, String newRoleName, String newRoleCode, Long operatorId) {
        // 获取源角色信息
        SysRole sourceRole = getById(sourceRoleId);
        if (sourceRole == null) {
            throw new RuntimeException("源角色不存在");
        }
        
        // 检查新角色编码是否存在
        if (checkRoleCodeExists(newRoleCode, null)) {
            throw new RuntimeException("角色编码已存在");
        }
        
        // 检查新角色名称是否存在
        if (checkRoleNameExists(newRoleName, null)) {
            throw new RuntimeException("角色名称已存在");
        }
        
        // 创建新角色
        SysRole newRole = new SysRole();
        newRole.setRoleCode(newRoleCode);
        newRole.setRoleName(newRoleName);
        newRole.setRoleDesc(sourceRole.getRoleDesc());
        newRole.setStatus(sourceRole.getStatus());
        newRole.setSortOrder(sourceRole.getSortOrder());
        newRole.setDataScope(sourceRole.getDataScope());
        newRole.setCreateBy(operatorId);
        newRole.setCreateTime(LocalDateTime.now());
        newRole.setDeleted(0);
        newRole.setRemark("复制自角色：" + sourceRole.getRoleName());
        
        if (!save(newRole)) {
            return false;
        }
        
        // 复制角色菜单权限
        List<Long> menuIds = selectMenuIdsByRoleId(sourceRoleId);
        if (!CollectionUtils.isEmpty(menuIds)) {
            assignRoleMenus(newRole.getId(), menuIds, operatorId);
        }
        
        return true;
    }
}