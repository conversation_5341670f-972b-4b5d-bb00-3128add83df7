package com.cf.financing.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cf.financing.entity.SysUserRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户角色关联Mapper接口
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Mapper
public interface SysUserRoleMapper extends BaseMapper<SysUserRole> {

    /**
     * 根据用户ID删除用户角色关联
     *
     * @param userId 用户ID
     * @return 影响行数
     */
    int deleteByUserId(@Param("userId") Long userId);

    /**
     * 根据角色ID删除用户角色关联
     *
     * @param roleId 角色ID
     * @return 影响行数
     */
    int deleteByRoleId(@Param("roleId") Long roleId);

    /**
     * 批量插入用户角色关联
     *
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @param createBy 创建人
     * @return 影响行数
     */
    int insertUserRoleBatch(@Param("userId") Long userId,
                           @Param("roleIds") List<Long> roleIds,
                           @Param("createBy") Long createBy);

    /**
     * 根据用户ID查询角色ID列表
     *
     * @param userId 用户ID
     * @return 角色ID列表
     */
    List<Long> selectRoleIdsByUserId(@Param("userId") Long userId);

    /**
     * 根据角色ID查询用户ID列表
     *
     * @param roleId 角色ID
     * @return 用户ID列表
     */
    List<Long> selectUserIdsByRoleId(@Param("roleId") Long roleId);

    /**
     * 检查用户是否拥有指定角色
     *
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 数量
     */
    int checkUserHasRole(@Param("userId") Long userId, @Param("roleId") Long roleId);

    /**
     * 根据用户ID列表删除用户角色关联
     *
     * @param userIds 用户ID列表
     * @return 影响行数
     */
    int deleteByUserIds(@Param("userIds") List<Long> userIds);

    /**
     * 根据角色ID列表删除用户角色关联
     *
     * @param roleIds 角色ID列表
     * @return 影响行数
     */
    int deleteByRoleIds(@Param("roleIds") List<Long> roleIds);

    /**
     * 查询用户角色关联详情
     *
     * @param userId 用户ID
     * @return 用户角色关联列表（包含角色信息）
     */
    List<SysUserRole> selectUserRoleDetails(@Param("userId") Long userId);

    /**
     * 查询角色用户关联详情
     *
     * @param roleId 角色ID
     * @return 用户角色关联列表（包含用户信息）
     */
    List<SysUserRole> selectRoleUserDetails(@Param("roleId") Long roleId);

    /**
     * 批量更新用户角色关联（先删除后插入）
     *
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @param createBy 创建人
     * @return 影响行数
     */
    int updateUserRoles(@Param("userId") Long userId,
                       @Param("roleIds") List<Long> roleIds,
                       @Param("createBy") Long createBy);
}