package com.cf.financing.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cf.financing.entity.CustomerInfo;
import com.cf.financing.mapper.CustomerInfoMapper;
import com.cf.financing.service.ICustomerInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 客户信息服务实现类
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Slf4j
@Service
public class CustomerInfoServiceImpl extends ServiceImpl<CustomerInfoMapper, CustomerInfo> implements ICustomerInfoService {

    @Override
    public IPage<CustomerInfo> getCustomerPage(
            Page<CustomerInfo> page,
            String customerNo,
            String customerName,
            String phone,
            String idCard,
            String riskLevel,
            String creditLevel) {
        
        return baseMapper.selectCustomerPage(
                page, customerNo, customerName, phone, idCard, riskLevel, creditLevel, null
        );
    }

    @Override
    public CustomerInfo getCustomerById(Long customerId) {
        return baseMapper.selectCustomerById(customerId);
    }

    @Override
    public CustomerInfo getCustomerByIdCard(String idCard) {
        return baseMapper.selectCustomerByIdCard(idCard);
    }

    @Override
    public CustomerInfo getCustomerByPhone(String phone) {
        return baseMapper.selectCustomerByPhone(phone);
    }

    @Override
    public CustomerInfo getCustomerByNo(String customerNo) {
        return baseMapper.selectCustomerByNo(customerNo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createCustomer(CustomerInfo customer) {
        try {
            // 检查身份证号是否存在
            if (StringUtils.hasText(customer.getIdCard()) && 
                checkIdCardExists(customer.getIdCard(), null)) {
                log.warn("身份证号已存在: {}", customer.getIdCard());
                return false;
            }
            
            // 检查客户编号是否存在
            if (StringUtils.hasText(customer.getCustomerNo()) && 
                checkCustomerNoExists(customer.getCustomerNo(), null)) {
                log.warn("客户编号已存在: {}", customer.getCustomerNo());
                return false;
            }
            
            // 如果没有客户编号，自动生成
            if (!StringUtils.hasText(customer.getCustomerNo())) {
                customer.setCustomerNo(generateCustomerNo());
            }
            
            // 设置默认风险等级
            if (!StringUtils.hasText(customer.getRiskLevel())) {
                customer.setRiskLevel("LOW");
            }
            
            // 设置默认信用等级
            if (!StringUtils.hasText(customer.getCreditLevel())) {
                customer.setCreditLevel("C");
            }
            
            return save(customer);
        } catch (Exception e) {
            log.error("创建客户失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCustomer(CustomerInfo customer) {
        try {
            // 检查身份证号是否存在
            if (StringUtils.hasText(customer.getIdCard()) && 
                checkIdCardExists(customer.getIdCard(), customer.getId())) {
                log.warn("身份证号已存在: {}", customer.getIdCard());
                return false;
            }
            
            // 检查客户编号是否存在
            if (StringUtils.hasText(customer.getCustomerNo()) && 
                checkCustomerNoExists(customer.getCustomerNo(), customer.getId())) {
                log.warn("客户编号已存在: {}", customer.getCustomerNo());
                return false;
            }
            
            return updateById(customer);
        } catch (Exception e) {
            log.error("更新客户失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteCustomer(Long customerId) {
        try {
            return removeById(customerId);
        } catch (Exception e) {
            log.error("删除客户失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteCustomers(List<Long> customerIds) {
        try {
            return removeByIds(customerIds);
        } catch (Exception e) {
            log.error("批量删除客户失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getCustomerStatistics() {
        return baseMapper.selectCustomerStatistics();
    }

    @Override
    public List<Map<String, Object>> getRiskLevelDistribution() {
        return baseMapper.selectRiskLevelDistribution();
    }

    @Override
    public List<Map<String, Object>> getCreditLevelDistribution() {
        return baseMapper.selectCreditLevelDistribution();
    }

    @Override
    public List<CustomerInfo> getHighRiskCustomers(Integer limit) {
        return baseMapper.selectHighRiskCustomers(limit);
    }

    @Override
    public List<CustomerInfo> getTopOverdueCustomers(Integer limit) {
        return baseMapper.selectTopOverdueCustomers(limit);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateRiskLevel(List<Long> customerIds, String riskLevel) {
        try {
            int result = baseMapper.batchUpdateRiskLevel(customerIds, riskLevel);
            return result > 0;
        } catch (Exception e) {
            log.error("批量更新客户风险等级失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateCreditLevel(List<Long> customerIds, String creditLevel) {
        try {
            int result = baseMapper.batchUpdateCreditLevel(customerIds, creditLevel);
            return result > 0;
        } catch (Exception e) {
            log.error("批量更新客户信用等级失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean checkIdCardExists(String idCard, Long excludeCustomerId) {
        int count = baseMapper.checkIdCardExists(idCard, excludeCustomerId);
        return count > 0;
    }

    @Override
    public boolean checkCustomerNoExists(String customerNo, Long excludeCustomerId) {
        int count = baseMapper.checkCustomerNoExists(customerNo, excludeCustomerId);
        return count > 0;
    }

    @Override
    public List<Map<String, Object>> getMonthlyNewCustomerTrend(Integer months) {
        return baseMapper.selectMonthlyNewCustomerTrend(months);
    }

    @Override
    public List<CustomerInfo> searchCustomers(String keyword, Integer limit) {
        return baseMapper.searchCustomers(keyword, limit);
    }

    @Override
    public String generateCustomerNo() {
        // 生成格式: CUS + 年月日 + 4位序号
        String dateStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        
        // 查询当日最大序号
        LambdaQueryWrapper<CustomerInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.likeRight(CustomerInfo::getCustomerNo, "CUS" + dateStr)
               .orderByDesc(CustomerInfo::getCustomerNo)
               .last("LIMIT 1");
        
        CustomerInfo lastCustomer = getOne(wrapper);
        int sequence = 1;
        
        if (lastCustomer != null && lastCustomer.getCustomerNo() != null) {
            String lastCustomerNo = lastCustomer.getCustomerNo();
            if (lastCustomerNo.length() >= 15) {
                try {
                    sequence = Integer.parseInt(lastCustomerNo.substring(11)) + 1;
                } catch (NumberFormatException e) {
                    log.warn("解析客户编号序号失败: {}", lastCustomerNo);
                }
            }
        }
        
        return String.format("CUS%s%04d", dateStr, sequence);
    }
}