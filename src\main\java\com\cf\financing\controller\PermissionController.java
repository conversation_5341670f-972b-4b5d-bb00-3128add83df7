package com.cf.financing.controller;

import com.cf.financing.entity.SysUser;
import com.cf.financing.entity.SysRole;
import com.cf.financing.entity.SysMenu;
import com.cf.financing.service.ISysUserService;
import com.cf.financing.service.ISysRoleService;
import com.cf.financing.service.ISysMenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 权限查询控制器
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/api/permission")
public class PermissionController {

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private ISysMenuService menuService;

    /**
     * 获取当前用户的所有权限
     */
    @GetMapping("/current")
    public Map<String, Object> getCurrentUserPermissions() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !authentication.isAuthenticated()) {
                result.put("success", false);
                result.put("message", "用户未登录");
                return result;
            }

            String username = authentication.getName();
            if ("anonymousUser".equals(username)) {
                result.put("success", false);
                result.put("message", "匿名用户无权限");
                return result;
            }

            SysUser user = userService.getUserByUsername(username);
            if (user == null) {
                result.put("success", false);
                result.put("message", "用户不存在");
                return result;
            }

            // 检查是否为超级管理员
            boolean isAdmin = "admin".equals(username) || 
                             authentication.getAuthorities().stream()
                                 .anyMatch(auth -> "ROLE_ADMIN".equals(auth.getAuthority()));

            if (!isAdmin) {
                result.put("success", false);
                result.put("message", "只有超级管理员才能查看所有权限");
                return result;
            }

            // 获取所有角色信息
            List<SysRole> allRoles = roleService.list();
            
            // 获取所有菜单权限
            List<SysMenu> allMenus = menuService.selectMenuTree(1); // 1表示启用状态
            
            // 获取用户菜单权限
            List<SysMenu> userMenus = menuService.selectUserMenuTree(user.getId());
            
            // 获取用户权限标识
            List<String> userPermissions = menuService.selectPermissionsByUserId(user.getId());

            result.put("success", true);
            result.put("user", user);
            result.put("allRoles", allRoles);
            result.put("allMenus", allMenus);
            result.put("userMenus", userMenus);
            result.put("userPermissions", userPermissions);
            result.put("isAdmin", isAdmin);

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取权限信息失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 获取超级管理员角色的所有权限
     */
    @GetMapping("/admin")
    public Map<String, Object> getAdminPermissions() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !authentication.isAuthenticated()) {
                result.put("success", false);
                result.put("message", "用户未登录");
                return result;
            }

            String username = authentication.getName();
            SysUser user = userService.getUserByUsername(username);
            
            // 检查是否为超级管理员
            boolean isAdmin = "admin".equals(username) || 
                             authentication.getAuthorities().stream()
                                 .anyMatch(auth -> "ROLE_ADMIN".equals(auth.getAuthority()));

            if (!isAdmin) {
                result.put("success", false);
                result.put("message", "只有超级管理员才能查看管理员权限");
                return result;
            }

            // 获取超级管理员角色
            SysRole adminRole = roleService.selectByRoleCode("ADMIN");
            if (adminRole == null) {
                result.put("success", false);
                result.put("message", "超级管理员角色不存在");
                return result;
            }

            // 获取所有菜单权限（超级管理员拥有所有权限）
            List<SysMenu> allMenus = menuService.selectMenuTree(1);
            
            // 构建权限树结构
            List<Map<String, Object>> permissionTree = buildPermissionTree(allMenus);

            result.put("success", true);
            result.put("adminRole", adminRole);
            result.put("allPermissions", permissionTree);
            result.put("totalPermissionCount", countAllPermissions(allMenus));

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取管理员权限失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 构建权限树结构
     */
    private List<Map<String, Object>> buildPermissionTree(List<SysMenu> menus) {
        List<Map<String, Object>> tree = new ArrayList<>();
        
        for (SysMenu menu : menus) {
            Map<String, Object> node = new HashMap<>();
            node.put("id", menu.getId());
            node.put("name", menu.getMenuName());
            node.put("code", menu.getMenuCode());
            node.put("type", menu.getMenuType());
            node.put("permission", menu.getPermission());
            node.put("path", menu.getPath());
            node.put("icon", menu.getIcon());
            
            if (menu.getChildren() != null && !menu.getChildren().isEmpty()) {
                node.put("children", buildPermissionTree(menu.getChildren()));
            }
            
            tree.add(node);
        }
        
        return tree;
    }

    /**
     * 统计所有权限数量
     */
    private int countAllPermissions(List<SysMenu> menus) {
        int count = 0;
        for (SysMenu menu : menus) {
            if (menu.getPermission() != null && !menu.getPermission().trim().isEmpty()) {
                count++;
            }
            if (menu.getChildren() != null) {
                count += countAllPermissions(menu.getChildren());
            }
        }
        return count;
    }

    /**
     * 检查用户是否有指定权限
     */
    @GetMapping("/check")
    public Map<String, Object> checkPermission(@RequestParam String permission) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !authentication.isAuthenticated()) {
                result.put("success", false);
                result.put("hasPermission", false);
                result.put("message", "用户未登录");
                return result;
            }

            boolean hasPermission = authentication.getAuthorities().stream()
                    .anyMatch(auth -> permission.equals(auth.getAuthority()));

            result.put("success", true);
            result.put("hasPermission", hasPermission);
            result.put("permission", permission);

        } catch (Exception e) {
            result.put("success", false);
            result.put("hasPermission", false);
            result.put("message", "权限检查失败: " + e.getMessage());
        }

        return result;
    }
}
