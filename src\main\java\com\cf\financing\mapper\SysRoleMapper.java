package com.cf.financing.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cf.financing.entity.SysRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统角色Mapper接口
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Mapper
public interface SysRoleMapper extends BaseMapper<SysRole> {

    /**
     * 分页查询角色列表
     *
     * @param page 分页参数
     * @param roleName 角色名称
     * @param roleCode 角色编码
     * @param status 状态
     * @return 角色分页列表
     */
    IPage<SysRole> selectRolePage(Page<SysRole> page,
                                  @Param("roleName") String roleName,
                                  @Param("roleCode") String roleCode,
                                  @Param("status") Integer status);

    /**
     * 根据用户ID查询角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<SysRole> selectRolesByUserId(@Param("userId") Long userId);

    /**
     * 查询所有启用的角色
     *
     * @return 角色列表
     */
    List<SysRole> selectEnabledRoles();

    /**
     * 根据角色编码查询角色
     *
     * @param roleCode 角色编码
     * @return 角色信息
     */
    SysRole selectByRoleCode(@Param("roleCode") String roleCode);

    /**
     * 检查角色编码是否存在
     *
     * @param roleCode 角色编码
     * @param excludeId 排除的角色ID
     * @return 数量
     */
    int checkRoleCodeExists(@Param("roleCode") String roleCode, @Param("excludeId") Long excludeId);

    /**
     * 检查角色名称是否存在
     *
     * @param roleName 角色名称
     * @param excludeId 排除的角色ID
     * @return 数量
     */
    int checkRoleNameExists(@Param("roleName") String roleName, @Param("excludeId") Long excludeId);

    /**
     * 根据角色ID列表查询角色
     *
     * @param roleIds 角色ID列表
     * @return 角色列表
     */
    List<SysRole> selectRolesByIds(@Param("roleIds") List<Long> roleIds);

    /**
     * 查询角色的菜单权限ID列表
     *
     * @param roleId 角色ID
     * @return 菜单ID列表
     */
    List<Long> selectMenuIdsByRoleId(@Param("roleId") Long roleId);

    /**
     * 删除角色的菜单权限
     *
     * @param roleId 角色ID
     * @return 影响行数
     */
    int deleteRoleMenuByRoleId(@Param("roleId") Long roleId);

    /**
     * 批量插入角色菜单关联
     *
     * @param roleId 角色ID
     * @param menuIds 菜单ID列表
     * @param createBy 创建人
     * @return 影响行数
     */
    int insertRoleMenuBatch(@Param("roleId") Long roleId,
                           @Param("menuIds") List<Long> menuIds,
                           @Param("createBy") Long createBy);

    /**
     * 统计角色使用情况
     *
     * @param roleId 角色ID
     * @return 使用该角色的用户数量
     */
    int countUsersByRoleId(@Param("roleId") Long roleId);
}