package com.cf.financing.service;

import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;

/**
 * 自定义用户详情服务
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Service
public class CustomUserDetailsService implements UserDetailsService {

    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        // 这里暂时使用硬编码的用户信息，实际项目中应该从数据库查询
        if ("admin".equals(username)) {
            // 密码是 "123456" 的BCrypt加密结果
            String encodedPassword = passwordEncoder.encode("123456");
            return User.builder()
                    .username("admin")
                    .password(encodedPassword)
                    .authorities("ROLE_ADMIN", "ROLE_USER",
                            // 用户管理权限
                            "system:user:list", "system:user:query", "system:user:add", 
                            "system:user:edit", "system:user:remove", "system:user:resetPwd", 
                            "system:user:auth",
                            // 角色管理权限
                            "system:role:list", "system:role:query", "system:role:add", 
                            "system:role:edit", "system:role:remove", "system:role:auth", 
                            "system:role:export",
                            // 菜单管理权限
                            "system:menu:list", "system:menu:query", "system:menu:add", 
                            "system:menu:edit", "system:menu:remove", "system:menu:export")
                    .build();
        } else if ("user".equals(username)) {
            // 使用Spring Security默认生成的密码
            String encodedPassword = passwordEncoder.encode("cffef99f-5889-42be-b990-87efa8be055f");
            return User.builder()
                    .username("user")
                    .password(encodedPassword)
                    .authorities("ROLE_USER")
                    .build();
        }
        
        throw new UsernameNotFoundException("用户不存在: " + username);
    }
}
