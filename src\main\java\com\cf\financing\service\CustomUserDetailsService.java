package com.cf.financing.service;

import com.cf.financing.entity.SysUser;
import com.cf.financing.entity.SysRole;
import com.cf.financing.mapper.SysUserMapper;
import com.cf.financing.mapper.SysRoleMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 自定义用户详情服务
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Service
public class CustomUserDetailsService implements UserDetailsService {

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private SysRoleMapper roleMapper;

    @Autowired
    private ISysMenuService menuService;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        // 从数据库查询用户信息
        SysUser user = userMapper.selectUserByUsername(username);
        if (user == null) {
            throw new UsernameNotFoundException("用户不存在: " + username);
        }

        // 检查用户状态
        if (user.getStatus() == null || user.getStatus() != 1) {
            throw new UsernameNotFoundException("用户已被禁用: " + username);
        }

        // 获取用户权限
        List<GrantedAuthority> authorities = getUserAuthorities(user);

        return User.builder()
                .username(user.getUsername())
                .password(user.getPassword())
                .authorities(authorities)
                .build();
    }

    /**
     * 获取用户权限列表
     */
    private List<GrantedAuthority> getUserAuthorities(SysUser user) {
        List<GrantedAuthority> authorities = new ArrayList<>();

        try {
            // 获取用户角色信息
            List<SysRole> userRoles = userMapper.selectRolesByUserId(user.getId());

            // 添加角色权限
            if (userRoles != null && !userRoles.isEmpty()) {
                for (SysRole role : userRoles) {
                    authorities.add(new SimpleGrantedAuthority("ROLE_" + role.getRoleCode()));
                }
            } else {
                // 如果用户没有关联角色，根据用户名判断默认角色
                if ("admin".equals(user.getUsername())) {
                    authorities.add(new SimpleGrantedAuthority("ROLE_ADMIN"));
                    authorities.add(new SimpleGrantedAuthority("ROLE_USER"));
                } else {
                    authorities.add(new SimpleGrantedAuthority("ROLE_USER"));
                }
            }

            // 获取用户菜单权限
            List<String> permissions = menuService.selectPermissionsByUserId(user.getId());
            for (String permission : permissions) {
                if (permission != null && !permission.trim().isEmpty()) {
                    authorities.add(new SimpleGrantedAuthority(permission));
                }
            }

            // 为超级管理员添加所有系统管理权限
            if ("admin".equals(user.getUsername()) ||
                authorities.stream().anyMatch(auth -> "ROLE_ADMIN".equals(auth.getAuthority()))) {
                addAdminPermissions(authorities);
            }

        } catch (Exception e) {
            // 如果获取权限失败，至少给用户基本权限
            authorities.add(new SimpleGrantedAuthority("ROLE_USER"));
        }

        return authorities;
    }

    /**
     * 添加管理员权限
     */
    private void addAdminPermissions(List<GrantedAuthority> authorities) {
        String[] adminPermissions = {
            // 用户管理权限
            "system:user:list", "system:user:query", "system:user:add",
            "system:user:edit", "system:user:remove", "system:user:resetPwd",
            "system:user:auth",
            // 角色管理权限
            "system:role:list", "system:role:query", "system:role:add",
            "system:role:edit", "system:role:remove", "system:role:auth",
            "system:role:export",
            // 菜单管理权限
            "system:menu:list", "system:menu:query", "system:menu:add",
            "system:menu:edit", "system:menu:remove", "system:menu:export"
        };

        for (String permission : adminPermissions) {
            authorities.add(new SimpleGrantedAuthority(permission));
        }
    }
}
