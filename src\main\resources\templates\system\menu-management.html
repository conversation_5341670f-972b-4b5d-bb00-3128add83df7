<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>菜单管理</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap"
        rel="stylesheet">
    <style>
        :root {
            --primary-color: #165DFF;
            --primary-light: #E8F3FF;
            --success-color: #00B42A;
            --success-light: #E8FFEF;
            --danger-color: #F53F3F;
            --danger-light: #FFF2F2;
            --warning-color: #FF7D00;
            --warning-light: #FFF7E8;
            --text-color: #1D2129;
            --text-secondary: #4E5969;
            --bg-color: #F7F8FA;
            --card-bg: #FFFFFF;
            --border-color: #E5E6EB;
            --hover-bg: #F2F3F5;
            --shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            --shadow-lg: 0 4px 16px rgba(0, 0, 0, 0.12);
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Noto Sans SC', 'Microsoft YaHei', sans-serif;
        }

        body {
            background: var(--bg-color);
            color: var(--text-color);
            min-height: 100vh;
            padding: 10px;
            display: flex;
            justify-content: center;
            font-size: 14px;
            overflow: hidden;
        }

        .app-container {
            width: 100%;
            max-width: 1400px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: var(--shadow);
            transition: var(--transition);
            background: var(--card-bg);
            display: flex;
            flex-direction: column;
            height: 100vh;
            max-height: 900px;
        }

        .app-container:hover {
            box-shadow: var(--shadow-lg);
        }

        /* 导航栏 */
        .app-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 14px 20px;
            border-bottom: 1px solid var(--border-color);
            background: var(--card-bg);
            position: relative;
        }

        .app-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--primary-color);
        }

        .nav-actions {
            display: flex;
            gap: 10px;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* 筛选框区域 */
        .query-form {
            padding: 18px 20px;
            background: var(--card-bg);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            position: relative;
            z-index: 10;
        }

        .form-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .form-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-color);
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
        }

        @media (max-width: 1200px) {
            .form-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 480px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .form-group label {
            font-size: 12px;
            color: var(--text-secondary);
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 5px;
            white-space: normal;
        }

        .form-control {
            padding: 8px 12px;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-color);
            font-size: 13px;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(22, 93, 255, 0.2);
        }

        .form-control::placeholder {
            color: #C9CDD4;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            border: none;
            font-weight: 500;
            cursor: pointer;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 5px;
            transition: var(--transition);
            justify-content: center;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #0D47A1;
            box-shadow: 0 2px 8px rgba(22, 93, 255, 0.3);
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: var(--hover-bg);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: #E5E6EB;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background: #009A29;
            box-shadow: 0 2px 8px rgba(0, 180, 42, 0.3);
            transform: translateY(-1px);
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .btn-warning:hover {
            background: #E86F00;
            box-shadow: 0 2px 8px rgba(255, 125, 0, 0.3);
            transform: translateY(-1px);
        }

        .btn-danger {
            background: var(--danger-color);
            color: white;
        }

        .btn-danger:hover {
            background: #E63946;
            box-shadow: 0 2px 8px rgba(245, 63, 63, 0.3);
            transform: translateY(-1px);
        }

        .form-actions {
            display: flex;
            gap: 10px;
            margin-top: 12px;
            justify-content: flex-end;
        }

        /* 操作按钮区域样式 */
        .action-buttons {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 20px 0;
            margin-bottom: 20px;
            border-bottom: 1px solid #e9ecef;
            background: #f8f9fa;
            border-radius: 6px;
            padding-left: 16px;
            padding-right: 16px;
        }

        .action-buttons .btn {
            height: 36px;
            padding: 0 16px;
            font-size: 14px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: all 0.2s ease;
        }

        .action-buttons .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }



        /* 表格容器 */
        .table-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            background: var(--card-bg);
        }

        .table-content {
            flex: 1;
            overflow: auto;
            position: relative;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
            background: var(--card-bg);
        }

        .data-table th,
        .data-table td {
            padding: 10px 8px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
            white-space: nowrap;
            vertical-align: middle;
        }

        .data-table th {
            background: var(--hover-bg);
            font-weight: 600;
            color: var(--text-color);
            position: sticky;
            top: 0;
            z-index: 10;
            font-size: 12px;
        }

        .data-table tbody tr {
            transition: var(--transition);
        }

        .data-table tbody tr:hover {
            background: var(--hover-bg);
        }

        .checkbox-cell {
            width: 40px;
            text-align: center;
        }

        .checkbox-cell input[type="checkbox"] {
            cursor: pointer;
        }

        .highlight {
            color: var(--primary-color);
            font-weight: 600;
        }

        .status-tag {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            display: inline-block;
        }

        .status-tag.success {
            background: var(--success-light);
            color: var(--success-color);
        }

        .status-tag.danger {
            background: var(--danger-light);
            color: var(--danger-color);
        }

        .type-tag {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            display: inline-block;
        }

        .type-tag.directory {
            background: var(--primary-light);
            color: var(--primary-color);
        }

        .type-tag.menu {
            background: var(--success-light);
            color: var(--success-color);
        }

        .type-tag.button {
            background: var(--warning-light);
            color: var(--warning-color);
        }

        .action-btn {
            padding: 4px 8px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: var(--card-bg);
            cursor: pointer;
            font-size: 11px;
            margin-right: 4px;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            gap: 3px;
        }

        .action-btn:hover {
            background: var(--hover-bg);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .action-btn.edit {
            color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .action-btn.delete {
            color: var(--danger-color);
            border-color: var(--danger-color);
        }

        .action-btn.add {
            color: var(--success-color);
            border-color: var(--success-color);
        }

        /* 树形结构样式 */
        .tree-indent {
            display: inline-block;
            width: 20px;
        }

        .tree-icon {
            color: var(--text-secondary);
            margin-right: 5px;
        }

        /* 分页样式 */
        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 20px;
            border-top: 1px solid var(--border-color);
            background: var(--card-bg);
        }

        .pagination-info {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .pagination-controls {
            display: flex;
            gap: 4px;
        }

        .pagination-btn {
            padding: 6px 10px;
            border: 1px solid var(--border-color);
            background: var(--card-bg);
            cursor: pointer;
            font-size: 12px;
            border-radius: 4px;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 32px;
        }

        .pagination-btn:hover:not(:disabled) {
            background: var(--hover-bg);
            border-color: var(--primary-color);
        }

        .pagination-btn.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background: var(--card-bg);
            border-radius: 8px;
            padding: 0;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px 20px;
            border-bottom: 1px solid var(--border-color);
            background: var(--hover-bg);
        }

        .modal-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-color);
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: var(--text-secondary);
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-close:hover {
            color: var(--text-color);
        }

        .modal-body {
            padding: 20px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .modal-footer {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            padding: 16px 20px;
            border-top: 1px solid var(--border-color);
            background: var(--hover-bg);
        }
    </style>
</head>

<body>
    <div class="app-container">
        <!-- 导航栏 -->
        <div class="app-nav">
            <div class="app-title">
                <i class="fas fa-sitemap"></i>
                菜单管理系统
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 筛选框区域 -->
            <div class="query-form">
                <div class="form-header">
                    <div class="form-title">
                        <i class="fas fa-filter"></i>
                        查询条件
                    </div>
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label>菜单名称</label>
                        <input type="text" class="form-control" placeholder="请输入菜单名称" id="menuName">
                    </div>

                    <div class="form-group">
                        <label>菜单类型</label>
                        <select class="form-control" id="menuType">
                            <option value="">全部类型</option>
                            <option value="1">目录</option>
                            <option value="2">菜单</option>
                            <option value="3">按钮</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>菜单状态</label>
                        <select class="form-control" id="status">
                            <option value="">全部状态</option>
                            <option value="1">启用</option>
                            <option value="0">禁用</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>父级菜单</label>
                        <select class="form-control" id="parentId">
                            <option value="">全部菜单</option>
                            <option value="0">顶级菜单</option>
                        </select>
                    </div>
                </div>

                <div class="form-actions">
                    <button class="btn btn-secondary" id="resetBtn">
                        <i class="fas fa-undo"></i>
                        重置
                    </button>
                    <button class="btn btn-primary" id="searchBtn">
                        <i class="fas fa-search"></i>
                        查询
                    </button>
                </div>
            </div>

            <!-- 操作按钮区域 -->
            <div class="action-buttons">
                <button class="btn btn-success" id="addMenuBtn">
                    <i class="fas fa-plus"></i>
                    新增菜单
                </button>
                <button class="btn btn-danger" id="batchDeleteBtn">
                    <i class="fas fa-trash"></i>
                    批量删除
                </button>
            </div>

            <!-- 记录展示区域 -->
            <div class="table-container">
                <div class="table-content">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th class="checkbox-cell">
                                    <input type="checkbox" id="selectAll">
                                </th>
                                <th>菜单名称</th>
                                <th>菜单类型</th>
                                <th>图标</th>
                                <th>排序</th>
                                <th>权限标识</th>
                                <th>路由地址</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="tableBody">
                            <!-- 数据将通过JavaScript动态生成 -->
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="pagination">
                    <div class="pagination-info">
                        共 <span id="totalRecords">0</span> 条记录，第 <span id="currentPage">1</span> / <span
                            id="totalPages">1</span> 页
                    </div>
                    <div class="pagination-controls">
                        <button class="pagination-btn" id="prevBtn" disabled>
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button class="pagination-btn active">1</button>
                        <button class="pagination-btn">2</button>
                        <button class="pagination-btn">3</button>
                        <button class="pagination-btn" id="nextBtn">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 菜单编辑模态框 -->
    <div class="modal" id="menuModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">新增菜单</h3>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="menuForm">
                    <div class="form-group">
                        <label>菜单名称 *</label>
                        <input type="text" class="form-control" id="formMenuName" required>
                    </div>
                    <div class="form-group">
                        <label>父级菜单</label>
                        <select class="form-control" id="formParentId">
                            <option value="0">顶级菜单</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>菜单类型 *</label>
                        <select class="form-control" id="formMenuType" required>
                            <option value="">请选择类型</option>
                            <option value="1">目录</option>
                            <option value="2">菜单</option>
                            <option value="3">按钮</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>菜单图标</label>
                        <input type="text" class="form-control" id="formIcon" placeholder="如：fas fa-home">
                    </div>
                    <div class="form-group">
                        <label>排序号</label>
                        <input type="number" class="form-control" id="formSort" value="0">
                    </div>
                    <div class="form-group">
                        <label>权限标识</label>
                        <input type="text" class="form-control" id="formPermission" placeholder="如：system:user:list">
                    </div>
                    <div class="form-group">
                        <label>路由地址</label>
                        <input type="text" class="form-control" id="formPath" placeholder="如：/system/user">
                    </div>
                    <div class="form-group">
                        <label>状态</label>
                        <select class="form-control" id="formStatus">
                            <option value="1">启用</option>
                            <option value="0">禁用</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal()">取消</button>
                <button class="btn btn-primary" onclick="saveMenu()">保存</button>
            </div>
        </div>
    </div>

    <script>
        // 模拟数据
        const mockData = [
            {
                id: 1,
                menuName: '系统管理',
                parentId: 0,
                menuType: 1,
                icon: 'fas fa-cogs',
                sort: 1,
                permission: 'system',
                path: '/system',
                status: 1,
                createTime: '2023-01-01 10:00',
                level: 0
            },
            {
                id: 2,
                menuName: '用户管理',
                parentId: 1,
                menuType: 2,
                icon: 'fas fa-users',
                sort: 1,
                permission: 'system:user',
                path: '/system/user',
                status: 1,
                createTime: '2023-01-01 10:00',
                level: 1
            },
            {
                id: 3,
                menuName: '角色管理',
                parentId: 1,
                menuType: 2,
                icon: 'fas fa-user-shield',
                sort: 2,
                permission: 'system:role',
                path: '/system/role',
                status: 1,
                createTime: '2023-01-01 10:00',
                level: 1
            },
            {
                id: 4,
                menuName: '菜单管理',
                parentId: 1,
                menuType: 2,
                icon: 'fas fa-sitemap',
                sort: 3,
                permission: 'system:menu',
                path: '/system/menu',
                status: 1,
                createTime: '2023-01-01 10:00',
                level: 1
            },
            {
                id: 5,
                menuName: '新增用户',
                parentId: 2,
                menuType: 3,
                icon: '',
                sort: 1,
                permission: 'system:user:add',
                path: '',
                status: 1,
                createTime: '2023-01-01 10:00',
                level: 2
            },
            {
                id: 6,
                menuName: '编辑用户',
                parentId: 2,
                menuType: 3,
                icon: '',
                sort: 2,
                permission: 'system:user:edit',
                path: '',
                status: 1,
                createTime: '2023-01-01 10:00',
                level: 2
            },
            {
                id: 7,
                menuName: '删除用户',
                parentId: 2,
                menuType: 3,
                icon: '',
                sort: 3,
                permission: 'system:user:delete',
                path: '',
                status: 0,
                createTime: '2023-01-01 10:00',
                level: 2
            }
        ];

        let currentEditId = null;

        // 状态映射
        const statusMap = {
            1: { text: '启用', class: 'success' },
            0: { text: '禁用', class: 'danger' }
        };

        // 类型映射
        const typeMap = {
            1: { text: '目录', class: 'directory' },
            2: { text: '菜单', class: 'menu' },
            3: { text: '按钮', class: 'button' }
        };

        // 渲染表格
        function renderTable() {
            const tbody = document.getElementById('tableBody');
            tbody.innerHTML = '';

            // 构建树形结构
            const treeData = buildTree(mockData, 0);
            renderTreeNodes(tbody, treeData, 0);

        }

        // 构建树形结构
        function buildTree(data, parentId) {
            return data.filter(item => item.parentId === parentId)
                .sort((a, b) => a.sort - b.sort)
                .map(item => ({
                    ...item,
                    children: buildTree(data, item.id)
                }));
        }

        // 渲染树形节点
        function renderTreeNodes(tbody, nodes, level) {
            nodes.forEach(item => {
                const status = statusMap[item.status];
                const type = typeMap[item.menuType];
                const row = document.createElement('tr');

                const indent = '&nbsp;'.repeat(level * 4);
                const treeIcon = item.children && item.children.length > 0 ?
                    '<i class="fas fa-folder tree-icon"></i>' :
                    '<i class="fas fa-file tree-icon"></i>';

                row.innerHTML = `
                    <td class="checkbox-cell">
                        <input type="checkbox" data-id="${item.id}">
                    </td>
                    <td>
                        ${indent}${treeIcon}
                        <span class="highlight">${item.menuName}</span>
                    </td>
                    <td>
                        <span class="type-tag ${type.class}">${type.text}</span>
                    </td>
                    <td>
                        ${item.icon ? `<i class="${item.icon}"></i>` : '-'}
                    </td>
                    <td>${item.sort}</td>
                    <td>${item.permission || '-'}</td>
                    <td>${item.path || '-'}</td>
                    <td>
                        <span class="status-tag ${status.class}">${status.text}</span>
                    </td>
                    <td>${item.createTime}</td>
                    <td>
                        <button class="action-btn add" onclick="addSubMenu(${item.id})">
                            <i class="fas fa-plus"></i>
                            新增
                        </button>
                        <button class="action-btn edit" onclick="editMenu(${item.id})">
                            <i class="fas fa-edit"></i>
                            编辑
                        </button>
                        <button class="action-btn delete" onclick="deleteMenu(${item.id})">
                            <i class="fas fa-trash"></i>
                            删除
                        </button>
                    </td>
                `;
                tbody.appendChild(row);

                // 递归渲染子节点
                if (item.children && item.children.length > 0) {
                    renderTreeNodes(tbody, item.children, level + 1);
                }
            });
        }



        // 新增菜单
        function addMenu() {
            currentEditId = null;
            document.getElementById('modalTitle').textContent = '新增菜单';
            document.getElementById('menuForm').reset();
            loadParentOptions();
            document.getElementById('menuModal').classList.add('show');
        }

        // 新增子菜单
        function addSubMenu(parentId) {
            currentEditId = null;
            document.getElementById('modalTitle').textContent = '新增子菜单';
            document.getElementById('menuForm').reset();
            loadParentOptions();
            document.getElementById('formParentId').value = parentId;
            document.getElementById('menuModal').classList.add('show');
        }

        // 编辑菜单
        function editMenu(id) {
            const menu = mockData.find(item => item.id === id);
            if (menu) {
                currentEditId = id;
                document.getElementById('modalTitle').textContent = '编辑菜单';
                loadParentOptions();

                document.getElementById('formMenuName').value = menu.menuName;
                document.getElementById('formParentId').value = menu.parentId;
                document.getElementById('formMenuType').value = menu.menuType;
                document.getElementById('formIcon').value = menu.icon || '';
                document.getElementById('formSort').value = menu.sort;
                document.getElementById('formPermission').value = menu.permission || '';
                document.getElementById('formPath').value = menu.path || '';
                document.getElementById('formStatus').value = menu.status;

                document.getElementById('menuModal').classList.add('show');
            }
        }

        // 加载父级菜单选项
        function loadParentOptions() {
            const select = document.getElementById('formParentId');
            select.innerHTML = '<option value="0">顶级菜单</option>';

            const directories = mockData.filter(item => item.menuType === 1 || item.menuType === 2);
            directories.forEach(item => {
                const indent = '　'.repeat(item.level);
                select.innerHTML += `<option value="${item.id}">${indent}${item.menuName}</option>`;
            });
        }

        // 保存菜单
        function saveMenu() {
            const form = document.getElementById('menuForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const menuData = {
                menuName: document.getElementById('formMenuName').value,
                parentId: parseInt(document.getElementById('formParentId').value),
                menuType: parseInt(document.getElementById('formMenuType').value),
                icon: document.getElementById('formIcon').value,
                sort: parseInt(document.getElementById('formSort').value),
                permission: document.getElementById('formPermission').value,
                path: document.getElementById('formPath').value,
                status: parseInt(document.getElementById('formStatus').value)
            };

            // 计算层级
            if (menuData.parentId === 0) {
                menuData.level = 0;
            } else {
                const parent = mockData.find(item => item.id === menuData.parentId);
                menuData.level = parent ? parent.level + 1 : 0;
            }

            if (currentEditId) {
                // 编辑模式
                const index = mockData.findIndex(item => item.id === currentEditId);
                if (index > -1) {
                    mockData[index] = { ...mockData[index], ...menuData };
                }
                alert('菜单信息更新成功！');
            } else {
                // 新增模式
                menuData.id = Math.max(...mockData.map(item => item.id)) + 1;
                menuData.createTime = new Date().toLocaleString('zh-CN');
                mockData.push(menuData);
                alert('菜单创建成功！');
            }

            closeModal();
            renderTable();
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('menuModal').classList.remove('show');
            currentEditId = null;
        }

        // 删除菜单
        function deleteMenu(id) {
            const menu = mockData.find(item => item.id === id);
            if (menu) {
                // 检查是否有子菜单
                const hasChildren = mockData.some(item => item.parentId === id);
                if (hasChildren) {
                    alert('该菜单下还有子菜单，请先删除子菜单！');
                    return;
                }

                if (confirm(`确定要删除菜单 "${menu.menuName}" 吗？此操作无法撤销。`)) {
                    const index = mockData.findIndex(item => item.id === id);
                    if (index > -1) {
                        mockData.splice(index, 1);
                        renderTable();
                        alert('菜单删除成功！');
                    }
                }
            }
        }

        // 批量删除菜单
        function batchDeleteMenus() {
            const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]:checked');
            if (checkboxes.length === 0) {
                alert('请选择要删除的菜单');
                return;
            }

            const idsToDelete = Array.from(checkboxes).map(cb => parseInt(cb.getAttribute('data-id')));

            // 检查是否有父菜单被选中但子菜单未被选中
            for (const id of idsToDelete) {
                const hasChildren = mockData.some(item => item.parentId === id && !idsToDelete.includes(item.id));
                if (hasChildren) {
                    const menu = mockData.find(item => item.id === id);
                    alert(`菜单 "${menu.menuName}" 下还有子菜单未被选中，请先选中所有子菜单或单独删除！`);
                    return;
                }
            }

            if (confirm(`确定要删除选中的 ${checkboxes.length} 个菜单吗？此操作无法撤销。`)) {
                // 从数据中删除选中的菜单
                for (let i = mockData.length - 1; i >= 0; i--) {
                    if (idsToDelete.includes(mockData[i].id)) {
                        mockData.splice(i, 1);
                    }
                }

                renderTable();
                alert(`成功删除 ${idsToDelete.length} 个菜单`);
            }
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function () {
            // 渲染表格
            renderTable();

            // 绑定事件
            document.getElementById('addMenuBtn').addEventListener('click', addMenu);
            document.getElementById('batchDeleteBtn').addEventListener('click', batchDeleteMenus);

            // 搜索功能
            document.getElementById('searchBtn').addEventListener('click', function () {
                console.log('执行搜索');
                // 这里可以添加实际的搜索逻辑
            });

            // 重置功能
            document.getElementById('resetBtn').addEventListener('click', function () {
                document.getElementById('menuName').value = '';
                document.getElementById('menuType').value = '';
                document.getElementById('status').value = '';
                document.getElementById('parentId').value = '';
            });

            // 全选功能
            document.getElementById('selectAll').addEventListener('change', function () {
                const checkboxes = document.querySelectorAll('#tableBody input[type="checkbox"]');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
            });

            // 点击模态框外部关闭
            document.getElementById('menuModal').addEventListener('click', function (e) {
                if (e.target === this) {
                    closeModal();
                }
            });
        });
    </script>
</body>

</html>