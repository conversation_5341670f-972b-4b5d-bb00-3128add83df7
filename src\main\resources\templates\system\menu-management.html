<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>菜单管理 - CF系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        /* 筛选框样式 */
        .filter-box {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .filter-title {
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            color: white;
            padding: 15px 20px;
            font-weight: 600;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .filter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            padding: 20px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-label {
            font-weight: 500;
            color: #374151;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .filter-control {
            padding: 10px 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: white;
        }

        .filter-control:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .filter-btn-group {
            padding: 0 20px 20px;
            display: flex;
            gap: 10px;
        }

        .filter-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-btn.search {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
        }

        .filter-btn.search:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
        }

        .filter-btn.reset {
            background: linear-gradient(135deg, #6b7280, #4b5563);
            color: white;
        }

        .filter-btn.reset:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(107, 114, 128, 0.3);
        }

        /* 功能框样式 */
        .function-box {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }

        .stats-info {
            display: flex;
            gap: 30px;
            flex-wrap: wrap;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #374151;
            font-size: 14px;
        }

        .stat-item i {
            color: #4f46e5;
            font-size: 16px;
        }

        .function-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .function-btn {
            padding: 10px 16px;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .function-btn.primary {
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            color: white;
        }

        .function-btn.primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
        }

        .function-btn.success {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
        }

        .function-btn.success:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
        }

        .function-btn.warning {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
        }

        .function-btn.warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
        }

        .function-btn.danger {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
        }

        .function-btn.danger:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
        }

        .function-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
        }

        .function-btn {
            background: linear-gradient(135deg, #6b7280, #4b5563);
            color: white;
        }

        .function-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(107, 114, 128, 0.3);
        }

        /* 数据表格样式 */
        .data-table {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .data-header {
            background: linear-gradient(135deg, #1f2937, #111827);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .data-title {
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .data-actions {
            display: flex;
            gap: 10px;
        }

        .action-btn {
            padding: 8px 16px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
        }

        .action-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }

        .table-container {
            max-height: 600px;
            overflow-y: auto;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table thead {
            background: #f8fafc;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table th {
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
            color: #374151;
            border-bottom: 2px solid #e5e7eb;
            font-size: 14px;
        }

        .table td {
            padding: 12px;
            border-bottom: 1px solid #f3f4f6;
            color: #374151;
            font-size: 14px;
        }

        .table tbody tr {
            transition: all 0.2s ease;
        }

        .table tbody tr:hover {
            background: #f8fafc;
        }

        .checkbox {
            width: 16px;
            height: 16px;
            accent-color: #4f46e5;
        }

        /* 树形结构样式 */
        .tree-row {
            border-left: 3px solid transparent;
        }

        .tree-row.level-1 {
            border-left-color: #4f46e5;
        }

        .tree-row.level-2 {
            border-left-color: #10b981;
        }

        .tree-row.level-3 {
            border-left-color: #f59e0b;
        }

        .tree-indent {
            display: inline-block;
        }

        .tree-toggle {
            cursor: pointer;
            width: 16px;
            height: 16px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            color: #6b7280;
            transition: color 0.2s ease;
        }

        .tree-toggle:hover {
            color: #4f46e5;
        }

        .menu-icon {
            width: 20px;
            height: 20px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            color: #6b7280;
        }

        .menu-type-tag {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            display: inline-block;
        }

        .type-directory {
            background: #dbeafe;
            color: #1d4ed8;
        }

        .type-menu {
            background: #dcfce7;
            color: #166534;
        }

        .type-button {
            background: #fef3c7;
            color: #d97706;
        }

        .status-tag {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
        }

        .status-show {
            background: #dcfce7;
            color: #166534;
        }

        .status-hide {
            background: #fee2e2;
            color: #991b1b;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .action-buttons button {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .btn-add {
            background: #dcfce7;
            color: #166534;
        }

        .btn-add:hover {
            background: #bbf7d0;
            transform: translateY(-1px);
        }

        .btn-edit {
            background: #dbeafe;
            color: #1d4ed8;
        }

        .btn-edit:hover {
            background: #bfdbfe;
            transform: translateY(-1px);
        }

        .btn-delete {
            background: #fee2e2;
            color: #dc2626;
        }

        .btn-delete:hover {
            background: #fecaca;
            transform: translateY(-1px);
        }

        /* 分页样式 */
        .pagination {
            padding: 20px;
            background: #f8fafc;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: 1px solid #e5e7eb;
        }

        .pagination-info {
            color: #6b7280;
            font-size: 14px;
        }

        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .pagination-btn {
            padding: 8px 16px;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
            color: #374151;
        }

        .pagination-btn:hover:not(:disabled) {
            background: #f3f4f6;
            border-color: #9ca3af;
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .page-numbers {
            display: flex;
            gap: 4px;
        }

        .page-number {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
            color: #374151;
        }

        .page-number:hover {
            background: #f3f4f6;
        }

        .page-number.active {
            background: #4f46e5;
            color: white;
            border-color: #4f46e5;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            max-width: 800px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            animation: modalSlideIn 0.3s ease;
        }

        .modal-content.modal-sm {
            max-width: 500px;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal-header {
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 5px;
            border-radius: 4px;
            transition: background 0.2s ease;
        }

        .modal-close:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .modal-body {
            padding: 30px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-label {
            font-weight: 500;
            color: #374151;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .form-label.required::after {
            content: ' *';
            color: #ef4444;
        }

        .form-control {
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: white;
        }

        .form-control:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .modal-footer {
            padding: 20px 30px;
            background: #f8fafc;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            border-top: 1px solid #e5e7eb;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
        }

        @media (max-width: 768px) {
            .filter-grid {
                grid-template-columns: 1fr;
            }
            
            .function-box {
                flex-direction: column;
                align-items: stretch;
            }
            
            .stats-info {
                justify-content: center;
            }
            
            .function-buttons {
                justify-content: center;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .pagination {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- 主容器 -->
    <div class="container">
        <!-- 筛选框 -->
        <div class="filter-box">
            <div class="filter-title">
                <i class="fas fa-filter"></i>
                菜单管理 - 筛选条件
            </div>
            <div class="filter-grid">
                <div class="filter-group">
                    <label class="filter-label">菜单名称:</label>
                    <input type="text" id="searchMenuName" placeholder="请输入菜单名称" class="filter-control">
                </div>
                <div class="filter-group">
                    <label class="filter-label">菜单类型:</label>
                    <select id="searchMenuType" class="filter-control">
                        <option value="">全部类型</option>
                        <option value="M">目录</option>
                        <option value="C">菜单</option>
                        <option value="F">按钮</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">状态:</label>
                    <select id="searchStatus" class="filter-control">
                        <option value="">全部状态</option>
                        <option value="1">显示</option>
                        <option value="0">隐藏</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">权限标识:</label>
                    <input type="text" id="searchPermission" placeholder="请输入权限标识" class="filter-control">
                </div>
            </div>
            <div class="filter-btn-group">
                <button onclick="searchMenus()" class="filter-btn search">
                    <i class="fas fa-search"></i>搜索
                </button>
                <button onclick="resetSearch()" class="filter-btn reset">
                    <i class="fas fa-refresh"></i>重置
                </button>
            </div>
        </div>

        <!-- 功能框 -->
        <div class="function-box">
            <div class="stats-info">
                <div class="stat-item">
                    <i class="fas fa-sitemap"></i>
                    <span>总菜单数: <strong id="totalMenus">0</strong></span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-folder"></i>
                    <span>目录: <strong id="directoryCount">0</strong></span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-list"></i>
                    <span>菜单: <strong id="menuCount">0</strong></span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-hand-pointer"></i>
                    <span>按钮: <strong id="buttonCount">0</strong></span>
                </div>
            </div>
            <div class="function-buttons">
                <button onclick="openAddMenuModal()" class="function-btn primary">
                    <i class="fas fa-plus"></i>新增菜单
                </button>
                <button onclick="expandAll()" class="function-btn success">
                    <i class="fas fa-expand-arrows-alt"></i>展开全部
                </button>
                <button onclick="collapseAll()" class="function-btn warning">
                    <i class="fas fa-compress-arrows-alt"></i>收起全部
                </button>
                <button onclick="exportMenus()" class="function-btn">
                    <i class="fas fa-download"></i>导出
                </button>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="data-table">
            <div class="data-header">
                <div class="data-title">
                    <i class="fas fa-sitemap"></i>
                    菜单列表
                </div>
                <div class="data-actions">
                    <button onclick="refreshData()" class="action-btn">
                        <i class="fas fa-sync-alt"></i>刷新
                    </button>
                </div>
            </div>
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th width="300">菜单名称</th>
                            <th width="80">图标</th>
                            <th width="100">排序</th>
                            <th width="120">权限标识</th>
                            <th width="100">组件路径</th>
                            <th width="80">类型</th>
                            <th width="80">状态</th>
                            <th width="150">创建时间</th>
                            <th width="200">操作</th>
                        </tr>
                    </thead>
                    <tbody id="menuTableBody">
                        <!-- 菜单数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 新增菜单模态框 -->
    <div id="addMenuModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-plus"></i>
                    新增菜单
                </h3>
                <button onclick="closeAddMenuModal()" class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="addMenuForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">上级菜单:</label>
                            <select id="addParentId" name="parentId" class="form-control">
                                <option value="0">主类目</option>
                                <!-- 动态加载父级菜单选项 -->
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label required">菜单类型:</label>
                            <select id="addMenuType" name="menuType" required class="form-control" onchange="handleMenuTypeChange('add')">
                                <option value="M">目录</option>
                                <option value="C">菜单</option>
                                <option value="F">按钮</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label required">菜单名称:</label>
                            <input type="text" id="addMenuName" name="menuName" required class="form-control" placeholder="请输入菜单名称">
                        </div>
                        <div class="form-group">
                            <label class="form-label">显示排序:</label>
                            <input type="number" id="addOrderNum" name="orderNum" class="form-control" placeholder="请输入排序号" value="0">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">菜单图标:</label>
                            <input type="text" id="addIcon" name="icon" class="form-control" placeholder="请输入图标类名">
                        </div>
                        <div class="form-group">
                            <label class="form-label">路由地址:</label>
                            <input type="text" id="addPath" name="path" class="form-control" placeholder="请输入路由地址">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">组件路径:</label>
                            <input type="text" id="addComponent" name="component" class="form-control" placeholder="请输入组件路径">
                        </div>
                        <div class="form-group">
                            <label class="form-label">权限标识:</label>
                            <input type="text" id="addPerms" name="perms" class="form-control" placeholder="请输入权限标识">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">菜单状态:</label>
                            <select id="addVisible" name="visible" class="form-control">
                                <option value="1">显示</option>
                                <option value="0">隐藏</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">菜单状态:</label>
                            <select id="addStatus" name="status" class="form-control">
                                <option value="1">正常</option>
                                <option value="0">停用</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group full-width">
                            <label class="form-label">备注:</label>
                            <textarea id="addRemark" name="remark" rows="3" class="form-control" placeholder="请输入备注"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" onclick="closeAddMenuModal()" class="btn btn-secondary">
                    <i class="fas fa-times"></i>取消
                </button>
                <button type="submit" form="addMenuForm" class="btn btn-primary">
                    <i class="fas fa-save"></i>保存
                </button>
            </div>
        </div>
    </div>

    <!-- 编辑菜单模态框 -->
    <div id="menuModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle" class="modal-title">
                    <i class="fas fa-edit"></i>
                    编辑菜单
                </h3>
                <button onclick="closeMenuModal()" class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="menuForm">
                    <input type="hidden" id="menuId">
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">上级菜单:</label>
                            <select id="parentId" name="parentId" class="form-control">
                                <option value="0">主类目</option>
                                <!-- 动态加载父级菜单选项 -->
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label required">菜单类型:</label>
                            <select id="menuType" name="menuType" required class="form-control" onchange="handleMenuTypeChange('edit')">
                                <option value="M">目录</option>
                                <option value="C">菜单</option>
                                <option value="F">按钮</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label required">菜单名称:</label>
                            <input type="text" id="menuName" name="menuName" required class="form-control" placeholder="请输入菜单名称">
                        </div>
                        <div class="form-group">
                            <label class="form-label">显示排序:</label>
                            <input type="number" id="orderNum" name="orderNum" class="form-control" placeholder="请输入排序号">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">菜单图标:</label>
                            <input type="text" id="icon" name="icon" class="form-control" placeholder="请输入图标类名">
                        </div>
                        <div class="form-group">
                            <label class="form-label">路由地址:</label>
                            <input type="text" id="path" name="path" class="form-control" placeholder="请输入路由地址">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">组件路径:</label>
                            <input type="text" id="component" name="component" class="form-control" placeholder="请输入组件路径">
                        </div>
                        <div class="form-group">
                            <label class="form-label">权限标识:</label>
                            <input type="text" id="perms" name="perms" class="form-control" placeholder="请输入权限标识">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">菜单状态:</label>
                            <select id="visible" name="visible" class="form-control">
                                <option value="1">显示</option>
                                <option value="0">隐藏</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">菜单状态:</label>
                            <select id="status" name="status" class="form-control">
                                <option value="1">正常</option>
                                <option value="0">停用</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group full-width">
                            <label class="form-label">备注:</label>
                            <textarea id="remark" name="remark" rows="3" class="form-control" placeholder="请输入备注"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" onclick="closeMenuModal()" class="btn btn-secondary">
                    <i class="fas fa-times"></i>取消
                </button>
                <button type="button" onclick="saveMenu()" class="btn btn-primary">
                    <i class="fas fa-save"></i>保存
                </button>
            </div>
        </div>
    </div>

    <script th:src="@{/js/menu-management.js}"></script>
</body>
</html>