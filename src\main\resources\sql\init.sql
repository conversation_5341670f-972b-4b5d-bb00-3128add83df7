-- CF金融催收管理系统数据库初始化脚本
-- 数据库名: financing
-- 字符集: utf8mb4

CREATE DATABASE IF NOT EXISTS `financing` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `financing`;

-- 1. 用户表
CREATE TABLE `sys_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(100) NOT NULL COMMENT '密码',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态(0:禁用,1:启用)',
  `dept_id` bigint(20) DEFAULT NULL COMMENT '部门ID',
  `role_id` bigint(20) DEFAULT NULL COMMENT '角色ID',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '删除标记(0:正常,1:删除)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_dept_id` (`dept_id`),
  KEY `idx_role_id` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统用户表';

-- 2. 角色表
CREATE TABLE `sys_role` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(50) NOT NULL COMMENT '角色名称',
  `role_code` varchar(50) NOT NULL COMMENT '角色编码',
  `description` varchar(255) DEFAULT NULL COMMENT '角色描述',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态(0:禁用,1:启用)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '删除标记(0:正常,1:删除)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_code` (`role_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统角色表';


-- ----------------------------
-- Table structure for `sys_menu`
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu` (
    `menu_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
    `menu_name` varchar(50) NOT NULL COMMENT '菜单名称',
    `parent_id` bigint(20) DEFAULT 0 COMMENT '父菜单ID',
    `order_num` int(4) DEFAULT 0 COMMENT '显示顺序',
    `url` varchar(200) DEFAULT '#' COMMENT '请求地址',
    `target` varchar(20) DEFAULT '' COMMENT '打开方式（menuItem页签 menuBlank新窗口）',
    `menu_type` char(1) DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
    `visible` char(1) DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
    `is_refresh` char(1) DEFAULT '1' COMMENT '是否刷新（0刷新 1不刷新）',
    `perms` varchar(100) DEFAULT NULL COMMENT '权限标识',
    `icon` varchar(100) DEFAULT '#' COMMENT '菜单图标',
    `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `remark` varchar(500) DEFAULT '' COMMENT '备注',
    PRIMARY KEY (`menu_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2009 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='菜单权限表';

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO `sys_menu` VALUES ('1', '系统管理', '0', '1', '#', '', 'M', '0', '1', '', 'fa fa-gear', 'admin', '2018-03-16 11:33:00', 'ry', '2018-03-16 11:33:00', '系统管理目录');
INSERT INTO `sys_menu` VALUES ('100', '用户管理', '1', '1', '/system/user', '', 'C', '0', '1', 'system:user:view', '#', 'admin', '2018-03-16 11:33:00', 'ry', '2018-03-16 11:33:00', '用户管理菜单');
INSERT INTO `sys_menu` VALUES ('101', '角色管理', '1', '2', '/system/role', '', 'C', '0', '1', 'system:role:view', '#', 'admin', '2018-03-16 11:33:00', 'ry', '2018-03-16 11:33:00', '角色管理菜单');
INSERT INTO `sys_menu` VALUES ('102', '菜单管理', '1', '3', '/system/menu', '', 'C', '0', '1', 'system:menu:view', '#', 'admin', '2018-03-16 11:33:00', 'ry', '2018-03-16 11:33:00', '菜单管理菜单');

-- 3. 部门表
CREATE TABLE `sys_dept` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '部门ID',
  `parent_id` bigint(20) DEFAULT 0 COMMENT '父部门ID',
  `dept_name` varchar(50) NOT NULL COMMENT '部门名称',
  `dept_code` varchar(50) DEFAULT NULL COMMENT '部门编码',
  `leader` varchar(50) DEFAULT NULL COMMENT '负责人',
  `phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态(0:禁用,1:启用)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '删除标记(0:正常,1:删除)',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统部门表';

-- 4. 客户信息表
CREATE TABLE `customer_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '客户ID',
  `customer_no` varchar(50) NOT NULL COMMENT '客户编号',
  `name` varchar(100) NOT NULL COMMENT '客户姓名',
  `id_card` varchar(18) DEFAULT NULL COMMENT '身份证号',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `phone2` varchar(20) DEFAULT NULL COMMENT '备用手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `address` varchar(255) DEFAULT NULL COMMENT '地址',
  `company` varchar(100) DEFAULT NULL COMMENT '工作单位',
  `income` decimal(12,2) DEFAULT NULL COMMENT '月收入',
  `credit_level` varchar(10) DEFAULT NULL COMMENT '信用等级',
  `risk_level` varchar(10) DEFAULT NULL COMMENT '风险等级',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '删除标记(0:正常,1:删除)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_customer_no` (`customer_no`),
  KEY `idx_id_card` (`id_card`),
  KEY `idx_phone` (`phone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户信息表';

-- 5. 案件信息表
CREATE TABLE `case_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '案件ID',
  `case_no` varchar(50) NOT NULL COMMENT '案件编号',
  `customer_id` bigint(20) NOT NULL COMMENT '客户ID',
  `product_name` varchar(100) DEFAULT NULL COMMENT '产品名称',
  `loan_amount` decimal(12,2) NOT NULL COMMENT '借款金额',
  `overdue_amount` decimal(12,2) NOT NULL COMMENT '逾期金额',
  `overdue_days` int(11) DEFAULT 0 COMMENT '逾期天数',
  `overdue_date` date DEFAULT NULL COMMENT '逾期日期',
  `case_status` varchar(20) DEFAULT 'PENDING' COMMENT '案件状态(PENDING:待处理,PROCESSING:处理中,COMPLETED:已完成,CLOSED:已关闭)',
  `case_level` varchar(10) DEFAULT NULL COMMENT '案件等级(S1,S2,S3,M1,M2,M3)',
  `assign_user_id` bigint(20) DEFAULT NULL COMMENT '分配催收员ID',
  `assign_time` datetime DEFAULT NULL COMMENT '分配时间',
  `last_contact_time` datetime DEFAULT NULL COMMENT '最后联系时间',
  `next_contact_time` datetime DEFAULT NULL COMMENT '下次联系时间',
  `contact_result` varchar(50) DEFAULT NULL COMMENT '联系结果',
  `remark` text COMMENT '备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '删除标记(0:正常,1:删除)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_case_no` (`case_no`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_assign_user_id` (`assign_user_id`),
  KEY `idx_case_status` (`case_status`),
  KEY `idx_overdue_date` (`overdue_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='案件信息表';

-- 6. 联系记录表
CREATE TABLE `contact_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `case_id` bigint(20) NOT NULL COMMENT '案件ID',
  `contact_type` varchar(20) NOT NULL COMMENT '联系方式(PHONE:电话,SMS:短信,EMAIL:邮件,VISIT:上门)',
  `contact_time` datetime NOT NULL COMMENT '联系时间',
  `contact_person` varchar(100) DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `contact_result` varchar(50) DEFAULT NULL COMMENT '联系结果',
  `contact_content` text COMMENT '联系内容',
  `promise_amount` decimal(12,2) DEFAULT NULL COMMENT '承诺还款金额',
  `promise_date` date DEFAULT NULL COMMENT '承诺还款日期',
  `operator_id` bigint(20) NOT NULL COMMENT '操作员ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_case_id` (`case_id`),
  KEY `idx_contact_time` (`contact_time`),
  KEY `idx_operator_id` (`operator_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='联系记录表';

-- 7. 还款记录表
CREATE TABLE `repayment_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '还款ID',
  `case_id` bigint(20) NOT NULL COMMENT '案件ID',
  `repayment_no` varchar(50) NOT NULL COMMENT '还款编号',
  `repayment_amount` decimal(12,2) NOT NULL COMMENT '还款金额',
  `repayment_date` date NOT NULL COMMENT '还款日期',
  `repayment_type` varchar(20) DEFAULT NULL COMMENT '还款方式(BANK:银行转账,ALIPAY:支付宝,WECHAT:微信,CASH:现金)',
  `repayment_status` varchar(20) DEFAULT 'SUCCESS' COMMENT '还款状态(SUCCESS:成功,FAILED:失败,PENDING:处理中)',
  `transaction_no` varchar(100) DEFAULT NULL COMMENT '交易流水号',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `operator_id` bigint(20) DEFAULT NULL COMMENT '操作员ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_repayment_no` (`repayment_no`),
  KEY `idx_case_id` (`case_id`),
  KEY `idx_repayment_date` (`repayment_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='还款记录表';

-- 8. 任务表
CREATE TABLE `task_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `task_no` varchar(50) NOT NULL COMMENT '任务编号',
  `task_title` varchar(200) NOT NULL COMMENT '任务标题',
  `task_type` varchar(20) DEFAULT NULL COMMENT '任务类型(CALL:电话催收,VISIT:上门催收,LEGAL:法务处理)',
  `case_id` bigint(20) DEFAULT NULL COMMENT '关联案件ID',
  `assign_user_id` bigint(20) NOT NULL COMMENT '分配用户ID',
  `task_status` varchar(20) DEFAULT 'PENDING' COMMENT '任务状态(PENDING:待处理,PROCESSING:处理中,COMPLETED:已完成,CANCELLED:已取消)',
  `priority` varchar(10) DEFAULT 'NORMAL' COMMENT '优先级(HIGH:高,NORMAL:普通,LOW:低)',
  `plan_start_time` datetime DEFAULT NULL COMMENT '计划开始时间',
  `plan_end_time` datetime DEFAULT NULL COMMENT '计划结束时间',
  `actual_start_time` datetime DEFAULT NULL COMMENT '实际开始时间',
  `actual_end_time` datetime DEFAULT NULL COMMENT '实际结束时间',
  `task_content` text COMMENT '任务内容',
  `task_result` text COMMENT '任务结果',
  `create_user_id` bigint(20) NOT NULL COMMENT '创建人ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '删除标记(0:正常,1:删除)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_no` (`task_no`),
  KEY `idx_case_id` (`case_id`),
  KEY `idx_assign_user_id` (`assign_user_id`),
  KEY `idx_task_status` (`task_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务信息表';

-- 9. 统计数据表
CREATE TABLE `statistics_data` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `stat_type` varchar(50) NOT NULL COMMENT '统计类型',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID(为空表示全局统计)',
  `dept_id` bigint(20) DEFAULT NULL COMMENT '部门ID',
  `total_cases` int(11) DEFAULT 0 COMMENT '总案件数',
  `new_cases` int(11) DEFAULT 0 COMMENT '新增案件数',
  `completed_cases` int(11) DEFAULT 0 COMMENT '完成案件数',
  `contact_count` int(11) DEFAULT 0 COMMENT '联系次数',
  `repayment_amount` decimal(15,2) DEFAULT 0.00 COMMENT '回款金额',
  `repayment_count` int(11) DEFAULT 0 COMMENT '回款笔数',
  `overdue_amount` decimal(15,2) DEFAULT 0.00 COMMENT '逾期金额',
  `recovery_rate` decimal(5,2) DEFAULT 0.00 COMMENT '回收率(%)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_stat_date_type_user` (`stat_date`,`stat_type`,`user_id`),
  KEY `idx_stat_date` (`stat_date`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_dept_id` (`dept_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='统计数据表';

-- 10. 系统日志表
CREATE TABLE `sys_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `username` varchar(50) DEFAULT NULL COMMENT '用户名',
  `operation` varchar(100) DEFAULT NULL COMMENT '操作内容',
  `method` varchar(200) DEFAULT NULL COMMENT '请求方法',
  `params` text COMMENT '请求参数',
  `ip` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `location` varchar(100) DEFAULT NULL COMMENT '操作地点',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `execute_time` bigint(20) DEFAULT NULL COMMENT '执行时长(毫秒)',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统日志表';

-- 插入初始数据

-- 插入默认部门
INSERT INTO `sys_dept` (`id`, `parent_id`, `dept_name`, `dept_code`, `leader`, `sort_order`) VALUES
(1, 0, '总公司', 'ROOT', '系统管理员', 1),
(2, 1, '催收部', 'COLLECTION', '催收主管', 2),
(3, 1, '风控部', 'RISK', '风控主管', 3),
(4, 1, '客服部', 'SERVICE', '客服主管', 4);

-- 插入默认角色
INSERT INTO `sys_role` (`id`, `role_name`, `role_code`, `description`) VALUES
(1, '超级管理员', 'ADMIN', '系统超级管理员，拥有所有权限'),
(2, '催收主管', 'COLLECTION_MANAGER', '催收部门主管，管理催收业务'),
(3, '催收员', 'COLLECTOR', '催收员，执行催收任务'),
(4, '客服', 'SERVICE', '客服人员，处理客户咨询');

-- 插入默认用户 (密码都是123456)
INSERT INTO `sys_user` (`id`, `username`, `password`, `real_name`, `phone`, `email`, `dept_id`, `role_id`) VALUES
(1, 'admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXISwrCZJ/6HpVfS2tVZHKHHbdC', '系统管理员', '13800138000', '<EMAIL>', 1, 1),
(2, 'manager', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXISwrCZJ/6HpVfS2tVZHKHHbdC', '催收主管', '13800138001', '<EMAIL>', 2, 2),
(3, 'collector1', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXISwrCZJ/6HpVfS2tVZHKHHbdC', '催收员1', '13800138002', '<EMAIL>', 2, 3);

-- 用户角色关联表
CREATE TABLE IF NOT EXISTS `sys_user_role` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_role` (`user_id`, `role_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_role_id` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';

-- 插入用户角色关联数据
INSERT INTO `sys_user_role` (`user_id`, `role_id`, `create_by`) VALUES
(1, 1, 1), -- admin用户关联超级管理员角色
(2, 2, 1), -- manager用户关联催收主管角色
(3, 3, 1); -- collector1用户关联催收员角色

-- 角色菜单关联表
CREATE TABLE IF NOT EXISTS `sys_role_menu` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `menu_id` bigint(20) NOT NULL COMMENT '菜单ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_menu` (`role_id`, `menu_id`),
  KEY `idx_role_id` (`role_id`),
  KEY `idx_menu_id` (`menu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色菜单关联表';

-- 插入超级管理员角色的菜单权限（拥有所有菜单权限）
INSERT INTO `sys_role_menu` (`role_id`, `menu_id`, `create_by`) VALUES
(1, 1, 1),   -- 系统管理目录
(1, 100, 1), -- 用户管理
(1, 101, 1), -- 角色管理
(1, 102, 1); -- 菜单管理

-- 插入测试客户数据
INSERT INTO `customer_info` (`customer_no`, `name`, `id_card`, `phone`, `email`, `address`, `company`, `income`, `credit_level`, `risk_level`) VALUES
('C001', '张三', '110101199001011234', '13900139001', '<EMAIL>', '北京市朝阳区xxx街道', 'ABC公司', 8000.00, 'B', 'MEDIUM'),
('C002', '李四', '110101199002021234', '13900139002', '<EMAIL>', '北京市海淀区xxx街道', 'DEF公司', 12000.00, 'A', 'LOW'),
('C003', '王五', '110101199003031234', '13900139003', '<EMAIL>', '北京市西城区xxx街道', '个体经营', 5000.00, 'C', 'HIGH');

-- 插入测试案件数据
INSERT INTO `case_info` (`case_no`, `customer_id`, `product_name`, `loan_amount`, `overdue_amount`, `overdue_days`, `overdue_date`, `case_level`, `assign_user_id`) VALUES
('CASE001', 1, '个人信用贷', 50000.00, 5200.00, 15, '2024-01-01', 'S1', 3),
('CASE002', 2, '房屋抵押贷', 200000.00, 8500.00, 30, '2024-01-15', 'M1', 3),
('CASE003', 3, '小额贷款', 20000.00, 2100.00, 45, '2024-02-01', 'M2', 3);

COMMIT;