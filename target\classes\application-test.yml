server:
  port: 8080

spring:
  application:
    name: cf-financing-system

  # 使用H2内存数据库进行测试
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=MySQL
    username: sa
    password:

  h2:
    console:
      enabled: true
      path: /h2-console

  # SQL初始化
  sql:
    init:
      mode: always
      schema-locations: classpath:schema.sql
      data-locations: classpath:data.sql

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: AUTO
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  type-aliases-package: com.cf.financing.entity

# 日志配置
logging:
  level:
    com.cf.financing: debug
    org.springframework.security: debug
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n'

# JWT配置
jwt:
  secret: cf-financing-system-secret-key-2024
  expiration: 86400000  # 24小时
  header: Authorization
  prefix: Bearer

# 系统配置
system:
  name: CF金融催收管理系统
  version: 1.0.0
  author: CF Team
  upload:
    path: /uploads/
    max-file-size: 10MB
    allowed-types: jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx
