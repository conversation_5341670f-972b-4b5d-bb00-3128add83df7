package com.cf.financing.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cf.financing.entity.SysMenu;

import java.util.List;

/**
 * 系统菜单服务接口
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
public interface ISysMenuService extends IService<SysMenu> {

    /**
     * 分页查询菜单列表
     *
     * @param page 分页参数
     * @param menuName 菜单名称
     * @param menuCode 菜单编码
     * @param status 状态
     * @param menuType 菜单类型
     * @return 菜单分页列表
     */
    IPage<SysMenu> selectMenuPage(Page<SysMenu> page, String menuName, String menuCode, Integer status, Integer menuType);

    /**
     * 查询菜单树形结构
     *
     * @param status 状态
     * @return 菜单树列表
     */
    List<SysMenu> selectMenuTree(Integer status);

    /**
     * 根据用户ID查询菜单权限
     *
     * @param userId 用户ID
     * @return 菜单列表
     */
    List<SysMenu> selectMenusByUserId(Long userId);

    /**
     * 根据角色ID查询菜单权限
     *
     * @param roleId 角色ID
     * @return 菜单列表
     */
    List<SysMenu> selectMenusByRoleId(Long roleId);

    /**
     * 查询用户菜单权限标识
     *
     * @param userId 用户ID
     * @return 权限标识列表
     */
    List<String> selectPermissionsByUserId(Long userId);

    /**
     * 查询用户可访问的菜单树（用于前端导航）
     *
     * @param userId 用户ID
     * @return 菜单树列表
     */
    List<SysMenu> selectUserMenuTree(Long userId);

    /**
     * 新增菜单
     *
     * @param menu 菜单信息
     * @return 是否成功
     */
    boolean insertMenu(SysMenu menu);

    /**
     * 修改菜单
     *
     * @param menu 菜单信息
     * @return 是否成功
     */
    boolean updateMenu(SysMenu menu);

    /**
     * 删除菜单
     *
     * @param menuId 菜单ID
     * @return 是否成功
     */
    boolean deleteMenu(Long menuId);

    /**
     * 批量删除菜单
     *
     * @param menuIds 菜单ID列表
     * @return 是否成功
     */
    boolean deleteMenus(List<Long> menuIds);

    /**
     * 检查菜单编码是否存在
     *
     * @param menuCode 菜单编码
     * @param excludeId 排除的菜单ID
     * @return 是否存在
     */
    boolean checkMenuCodeExists(String menuCode, Long excludeId);

    /**
     * 检查菜单名称是否存在（同级别下）
     *
     * @param menuName 菜单名称
     * @param parentId 父菜单ID
     * @param excludeId 排除的菜单ID
     * @return 是否存在
     */
    boolean checkMenuNameExists(String menuName, Long parentId, Long excludeId);

    /**
     * 根据父菜单ID查询子菜单
     *
     * @param parentId 父菜单ID
     * @return 子菜单列表
     */
    List<SysMenu> selectMenusByParentId(Long parentId);

    /**
     * 检查是否存在子菜单
     *
     * @param menuId 菜单ID
     * @return 是否存在子菜单
     */
    boolean hasChildMenus(Long menuId);

    /**
     * 检查菜单是否被角色使用
     *
     * @param menuId 菜单ID
     * @return 是否被使用
     */
    boolean isMenuInUse(Long menuId);

    /**
     * 修改菜单状态
     *
     * @param menuId 菜单ID
     * @param status 状态
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean updateMenuStatus(Long menuId, Integer status, Long operatorId);

    /**
     * 查询所有父菜单（目录和菜单类型）
     *
     * @return 父菜单列表
     */
    List<SysMenu> selectParentMenus();

    /**
     * 根据路径查询菜单
     *
     * @param path 路径
     * @return 菜单信息
     */
    SysMenu selectByPath(String path);

    /**
     * 构建菜单树形结构
     *
     * @param menus 菜单列表
     * @return 菜单树列表
     */
    List<SysMenu> buildMenuTree(List<SysMenu> menus);

    /**
     * 获取菜单统计信息
     *
     * @return 统计信息
     */
    Object getMenuStatistics();

    /**
     * 导出菜单数据
     *
     * @param menuName 菜单名称
     * @param menuCode 菜单编码
     * @param status 状态
     * @param menuType 菜单类型
     * @return 菜单列表
     */
    List<SysMenu> exportMenus(String menuName, String menuCode, Integer status, Integer menuType);

    /**
     * 刷新菜单缓存
     */
    void refreshMenuCache();

    /**
     * 根据角色ID查询菜单ID列表
     *
     * @param roleId 角色ID
     * @return 菜单ID列表
     */
    List<Long> selectMenuIdsByRoleId(Long roleId);
}