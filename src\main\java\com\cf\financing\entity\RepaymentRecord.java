package com.cf.financing.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 还款记录实体类
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("repayment_record")
public class RepaymentRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 还款ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 客户索引号
     */
    @TableField("client_id")
    private String clientId;

    /**
     * 客户姓名
     */
    @TableField("client_name")
    private String clientName;

    /**
     * 身份证号
     */
    @TableField("id_card")
    private String idCard;

    /**
     * 手机号
     */
    @TableField("phone")
    private String phone;

    /**
     * 还款编号
     */
    @TableField("repayment_no")
    private String repaymentNo;

    /**
     * 委托金额
     */
    @TableField("entrusted_amount")
    private BigDecimal entrustedAmount;

    /**
     * 逾期金额
     */
    @TableField("overdue_amount")
    private BigDecimal overdueAmount;

    /**
     * 还款金额
     */
    @TableField("repayment_amount")
    private BigDecimal repaymentAmount;

    /**
     * 本金还款
     */
    @TableField("principal_repayment")
    private BigDecimal principalRepayment;

    /**
     * 利息还款
     */
    @TableField("interest_repayment")
    private BigDecimal interestRepayment;

    /**
     * 费用还款
     */
    @TableField("fee_repayment")
    private BigDecimal feeRepayment;

    /**
     * 还款日期
     */
    @TableField("repayment_date")
    private LocalDate repaymentDate;

    /**
     * 还款时间
     */
    @TableField("repayment_time")
    private LocalDateTime repaymentTime;

    /**
     * 还款方式(BANK:银行转账,ALIPAY:支付宝,WECHAT:微信,CASH:现金,POS:POS机,OTHER:其他)
     */
    @TableField("repayment_type")
    private String repaymentType;

    /**
     * 还款渠道
     */
    @TableField("repayment_channel")
    private String repaymentChannel;

    /**
     * 还款状态(SUCCESS:成功,FAILED:失败,PENDING:处理中,CANCELLED:已取消)
     */
    @TableField("repayment_status")
    private String repaymentStatus;

    /**
     * 交易流水号
     */
    @TableField("transaction_no")
    private String transactionNo;

    /**
     * 银行流水号
     */
    @TableField("bank_serial_no")
    private String bankSerialNo;

    /**
     * 收款账户
     */
    @TableField("receive_account")
    private String receiveAccount;

    /**
     * 收款银行
     */
    @TableField("receive_bank")
    private String receiveBank;

    /**
     * 付款账户
     */
    @TableField("pay_account")
    private String payAccount;

    /**
     * 付款银行
     */
    @TableField("pay_bank")
    private String payBank;

    /**
     * 还款类型(ACTIVE:主动还款,PASSIVE:被动还款,PARTIAL:部分还款,FULL:全额还款)
     */
    @TableField("payment_type")
    private String paymentType;

    /**
     * 是否逾期还款
     */
    @TableField("is_overdue")
    private Boolean isOverdue;

    /**
     * 逾期天数
     */
    @TableField("overdue_days")
    private Integer overdueDays;

    /**
     * 减免金额
     */
    @TableField("reduction_amount")
    private BigDecimal reductionAmount;

    /**
     * 实收金额
     */
    @TableField("actual_amount")
    private BigDecimal actualAmount;

    /**
     * 手续费
     */
    @TableField("handling_fee")
    private BigDecimal handlingFee;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 操作员ID
     */
    @TableField("operator_id")
    private Long operatorId;

    /**
     * 审核状态(PENDING:待审核,APPROVED:已审核,REJECTED:已拒绝)
     */
    @TableField("audit_status")
    private String auditStatus;

    /**
     * 审核人ID
     */
    @TableField("auditor_id")
    private Long auditorId;

    /**
     * 审核时间
     */
    @TableField("audit_time")
    private LocalDateTime auditTime;

    /**
     * 审核意见
     */
    @TableField("audit_remark")
    private String auditRemark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标记(0:正常,1:删除)
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    // 非数据库字段
    /**
     * 操作员姓名
     */
    @TableField(exist = false)
    private String operatorName;

    /**
     * 审核人姓名
     */
    @TableField(exist = false)
    private String auditorName;

    /**
     * 剩余欠款
     */
    @TableField(exist = false)
    private BigDecimal remainingAmount;

    /**
     * 还款进度百分比
     */
    @TableField(exist = false)
    private BigDecimal repaymentProgress;
}