-- 菜单表
CREATE TABLE sys_menu (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '菜单ID',
    menu_name VARCHAR(50) NOT NULL COMMENT '菜单名称',
    parent_id BIGINT DEFAULT 0 COMMENT '父菜单ID',
    order_num INT DEFAULT 0 COMMENT '显示顺序',
    path VARCHAR(200) DEFAULT '' COMMENT '路由地址',
    component VARCHAR(255) DEFAULT NULL COMMENT '组件路径',
    query_param VARCHAR(255) DEFAULT NULL COMMENT '路由参数',
    is_frame TINYINT(1) DEFAULT 1 COMMENT '是否为外链（0是 1否）',
    is_cache TINYINT(1) DEFAULT 0 COMMENT '是否缓存（0缓存 1不缓存）',
    menu_type CHAR(1) DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
    visible CHAR(1) DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
    status CHAR(1) DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
    perms VARCHAR(100) DEFAULT NULL COMMENT '权限标识',
    icon VARCHAR(100) DEFAULT '#' COMMENT '菜单图标',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT '' COMMENT '备注'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='菜单权限表';

-- 角色菜单关联表
CREATE TABLE sys_role_menu (
    role_id BIGINT NOT NULL COMMENT '角色ID',
    menu_id BIGINT NOT NULL COMMENT '菜单ID',
    PRIMARY KEY (role_id, menu_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色和菜单关联表';

-- 用户角色关联表
CREATE TABLE sys_user_role (
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    PRIMARY KEY (user_id, role_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户和角色关联表';

-- 插入菜单数据（根据dashboard.html中的菜单结构）
INSERT INTO sys_menu VALUES
-- 一级菜单
(1, '首页', 0, 1, '/home', NULL, NULL, 1, 0, 'C', '0', '0', 'system:home:view', 'fa fa-home', 'admin', NOW(), '', NOW(), '首页'),
(2, '案池管理', 0, 2, '/case-pool', NULL, NULL, 1, 0, 'C', '0', '0', 'system:casepool:view', 'fa fa-folder-open', 'admin', NOW(), '', NOW(), '案池管理'),
(3, '还款管理', 0, 3, '/repayment', NULL, NULL, 1, 0, 'C', '0', '0', 'system:repayment:view', 'fa fa-credit-card', 'admin', NOW(), '', NOW(), '还款管理'),
(4, '作业清单', 0, 4, '/tasklist', NULL, NULL, 1, 0, 'C', '0', '0', 'system:tasklist:view', 'fa fa-tasks', 'admin', NOW(), '', NOW(), '作业清单'),
(5, '新增统计', 0, 5, '/statistics', NULL, NULL, 1, 0, 'C', '0', '0', 'system:statistics:view', 'fa fa-bar-chart', 'admin', NOW(), '', NOW(), '新增统计'),
(6, '换单查询', 0, 6, '/exchange', NULL, NULL, 1, 0, 'C', '0', '0', 'system:exchange:view', 'fa fa-exchange', 'admin', NOW(), '', NOW(), '换单查询'),
(7, '系统管理', 0, 7, '/system', NULL, NULL, 1, 0, 'M', '0', '0', 'system:manage', 'fa fa-cog', 'admin', NOW(), '', NOW(), '系统管理目录'),

-- 系统管理子菜单
(100, '用户管理', 7, 1, '/system/user', NULL, NULL, 1, 0, 'C', '0', '0', 'system:user:list', 'fa fa-users', 'admin', NOW(), '', NOW(), '用户管理菜单'),
(101, '角色管理', 7, 2, '/system/role', NULL, NULL, 1, 0, 'C', '0', '0', 'system:role:list', 'fa fa-user-circle', 'admin', NOW(), '', NOW(), '角色管理菜单'),
(102, '菜单管理', 7, 3, '/system/menu', NULL, NULL, 1, 0, 'C', '0', '0', 'system:menu:list', 'fa fa-list', 'admin', NOW(), '', NOW(), '菜单管理菜单'),

-- 用户管理按钮权限
(1000, '用户查询', 100, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:user:query', '#', 'admin', NOW(), '', NOW(), ''),
(1001, '用户新增', 100, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:user:add', '#', 'admin', NOW(), '', NOW(), ''),
(1002, '用户修改', 100, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:user:edit', '#', 'admin', NOW(), '', NOW(), ''),
(1003, '用户删除', 100, 4, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:user:remove', '#', 'admin', NOW(), '', NOW(), ''),
(1004, '用户导出', 100, 5, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:user:export', '#', 'admin', NOW(), '', NOW(), ''),
(1005, '用户导入', 100, 6, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:user:import', '#', 'admin', NOW(), '', NOW(), ''),
(1006, '重置密码', 100, 7, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:user:resetPwd', '#', 'admin', NOW(), '', NOW(), ''),
(1007, '分配角色', 100, 8, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:user:role', '#', 'admin', NOW(), '', NOW(), ''),

-- 角色管理按钮权限
(1100, '角色查询', 101, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:role:query', '#', 'admin', NOW(), '', NOW(), ''),
(1101, '角色新增', 101, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:role:add', '#', 'admin', NOW(), '', NOW(), ''),
(1102, '角色修改', 101, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:role:edit', '#', 'admin', NOW(), '', NOW(), ''),
(1103, '角色删除', 101, 4, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:role:remove', '#', 'admin', NOW(), '', NOW(), ''),
(1104, '角色导出', 101, 5, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:role:export', '#', 'admin', NOW(), '', NOW(), ''),
(1105, '分配权限', 101, 6, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:role:auth', '#', 'admin', NOW(), '', NOW(), ''),

-- 菜单管理按钮权限
(1200, '菜单查询', 102, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:menu:query', '#', 'admin', NOW(), '', NOW(), ''),
(1201, '菜单新增', 102, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:menu:add', '#', 'admin', NOW(), '', NOW(), ''),
(1202, '菜单修改', 102, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:menu:edit', '#', 'admin', NOW(), '', NOW(), ''),
(1203, '菜单删除', 102, 4, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:menu:remove', '#', 'admin', NOW(), '', NOW(), '');

-- 插入默认角色数据
INSERT INTO sys_role VALUES
(1, '超级管理员', 'admin', 0, '1', '0', '0', 'admin', NOW(), '', NOW(), '超级管理员'),
(2, '普通角色', 'common', 1, '1', '0', '0', 'admin', NOW(), '', NOW(), '普通角色');

-- 给超级管理员分配所有菜单权限
INSERT INTO sys_role_menu (role_id, menu_id)
SELECT 1, id FROM sys_menu;

-- 给普通角色分配基础菜单权限
INSERT INTO sys_role_menu VALUES
(2, 1), (2, 2), (2, 3), (2, 4), (2, 5), (2, 6),
(2, 1000), (2, 1100), (2, 1200);
