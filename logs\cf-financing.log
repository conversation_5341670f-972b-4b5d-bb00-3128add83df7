2025-06-15 00:00:06 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:00:06 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:00:06 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-1] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:00:06 [http-nio-8080-exec-10] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:00:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:00:06 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:00:06 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:00:06 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:00:06 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:00:06 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:00:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:00:06 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:00:06 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:00:06 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:00:06 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:00:06 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:00:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:00:06 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:01:06 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:01:06 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:01:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:01:06 [http-nio-8080-exec-7] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:01:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:01:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:01:06 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:01:06 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:01:06 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:01:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:01:06 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:01:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:01:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:01:06 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:01:06 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:01:06 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:01:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:01:06 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:01:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:01:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:01:06 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:01:23 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:01:23 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:01:23 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:01:23 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:01:23 [http-nio-8080-exec-10] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:01:23 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:02:06 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:02:06 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:02:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:02:06 [http-nio-8080-exec-4] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:02:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:02:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:02:06 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:02:06 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:02:06 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:02:06 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:02:06 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:02:06 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:02:06 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:02:06 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:02:06 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:02:06 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:02:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:02:06 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:02:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:02:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:02:06 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:03:06 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:03:06 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:03:06 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-3] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:03:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-7] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:03:06 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:03:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:03:06 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:03:06 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:03:06 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:03:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:03:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:03:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:03:06 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:03:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:03:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:03:06 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:03:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:03:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:04:06 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:04:06 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:04:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:04:06 [http-nio-8080-exec-4] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:04:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:04:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:04:06 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:04:06 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:04:06 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:04:06 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:04:06 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:04:06 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:04:06 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:04:06 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:04:06 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:04:06 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:04:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:04:06 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:04:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:04:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:04:06 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:04:10 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:04:10 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:10 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:10 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:04:10 [http-nio-8080-exec-3] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:04:10 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:04:12 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:04:12 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:12 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:12 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:04:12 [http-nio-8080-exec-7] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:04:12 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:04:12 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:04:12 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:12 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:12 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:04:12 [http-nio-8080-exec-8] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:04:12 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:04:12 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system/menu
2025-06-15 00:04:12 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:12 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:12 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system/menu
2025-06-15 00:04:12 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:04:18 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:04:18 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:18 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:18 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:04:18 [http-nio-8080-exec-1] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:04:18 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:04:18 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:04:18 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:18 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:18 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:04:18 [http-nio-8080-exec-10] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:04:18 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:04:18 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system/user
2025-06-15 00:04:18 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:18 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:18 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system/user
2025-06-15 00:04:18 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:04:23 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:04:23 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:23 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:23 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:04:23 [http-nio-8080-exec-5] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:04:23 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:04:50 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:04:50 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:50 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:50 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:04:50 [http-nio-8080-exec-2] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:04:50 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:04:50 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:04:50 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:50 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:50 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:04:50 [http-nio-8080-exec-6] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:04:50 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:04:50 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system/role
2025-06-15 00:04:50 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:50 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:50 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system/role
2025-06-15 00:04:50 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:04:52 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:04:52 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:52 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:52 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:04:52 [http-nio-8080-exec-7] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:04:52 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:04:52 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:04:52 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:52 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:52 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:04:52 [http-nio-8080-exec-8] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:04:52 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:04:52 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system/menu
2025-06-15 00:04:52 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:52 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:52 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system/menu
2025-06-15 00:04:52 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:05:06 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:05:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:05:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:05:06 [http-nio-8080-exec-1] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:05:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:05:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:05:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:05:06 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:05:06 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:05:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:05:06 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:05:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:05:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:05:06 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:05:06 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:05:06 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:05:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:05:06 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:05:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:05:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:05:06 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:06:06 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:06:06 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:06:06 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-6] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:06:06 [http-nio-8080-exec-2] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:06:06 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:06:06 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:06:06 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:06:06 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:06:06 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:06:06 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:06:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:06:06 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:06:06 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:06:06 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:06:06 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:06:06 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:06:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:06:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:07:06 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:07:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:07:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:07:06 [http-nio-8080-exec-1] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:07:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:07:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:07:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:07:06 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:07:06 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:07:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:07:06 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:07:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:07:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:07:06 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:07:06 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:07:06 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:07:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:07:06 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:07:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:07:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:07:06 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:07:23 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:07:23 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:07:23 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:07:23 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:07:23 [http-nio-8080-exec-6] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:07:23 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:08:16 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:08:16 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:08:16 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:08:16 [http-nio-8080-exec-3] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:08:16 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:08:16 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:08:16 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:08:45 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:08:45 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:08:45 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:08:45 [http-nio-8080-exec-8] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:08:45 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:08:45 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:08:45 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:08:50 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:08:50 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:08:50 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:08:50 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:08:50 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:08:50 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:08:50 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:08:50 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:08:50 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:08:50 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:08:50 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:08:50 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:08:50 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:08:50 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:08:52 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:08:52 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:08:52 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:08:52 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:08:52 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:08:52 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:08:52 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:08:52 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:08:52 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:08:52 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:08:52 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:08:52 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:08:52 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:08:52 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:08:54 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:08:54 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:08:54 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:08:54 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:08:54 [http-nio-8080-exec-5] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:08:54 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:09:06 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:09:06 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:09:06 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:09:06 [http-nio-8080-exec-6] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:09:06 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:09:06 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:09:06 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:09:45 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:09:45 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:09:45 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:09:45 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:09:45 [http-nio-8080-exec-2] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:09:45 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:10:06 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:10:06 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:10:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:10:06 [http-nio-8080-exec-7] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:10:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:10:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:10:06 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:10:19 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:10:19 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:10:19 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:10:19 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:10:19 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:10:19 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:10:19 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:10:19 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:10:19 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:10:19 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:10:19 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:10:19 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:10:19 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:10:19 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:10:30 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:10:30 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:10:30 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:10:30 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:10:30 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:10:30 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:10:30 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:10:30 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:10:30 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:10:30 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:10:30 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:10:30 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:10:30 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:10:30 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:11:23 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:11:23 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:11:23 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:11:23 [http-nio-8080-exec-2] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:11:23 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:11:23 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:11:23 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:11:23 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:11:23 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:11:23 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:11:23 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:11:23 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:11:23 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:11:23 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:11:23 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:11:23 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:11:23 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:11:23 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:11:23 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:11:23 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:11:23 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:11:48 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:11:48 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:11:48 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:11:48 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:11:48 [http-nio-8080-exec-8] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:11:48 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:12:06 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:12:06 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:12:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:12:06 [http-nio-8080-exec-9] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:12:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:12:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:12:06 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:12:06 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:12:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:12:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:12:06 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:12:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:12:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:12:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:12:06 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:12:06 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:12:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:12:06 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:12:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:12:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:12:06 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:13:06 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:13:06 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:13:06 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:13:06 [http-nio-8080-exec-5] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:13:06 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:13:06 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:13:06 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:13:06 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:13:06 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:13:06 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:13:06 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:13:06 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:13:06 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:13:06 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:13:06 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:13:06 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:13:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:13:06 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:13:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:13:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:13:06 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:14:06 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:14:06 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:14:06 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:14:06 [http-nio-8080-exec-3] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:14:06 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:14:06 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:14:06 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:14:06 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:14:06 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:14:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:14:06 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:14:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:14:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:14:06 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:14:06 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:14:06 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:14:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:14:06 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:14:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:14:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:14:06 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:14:07 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:14:07 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:14:07 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:14:07 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:14:07 [http-nio-8080-exec-9] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:14:07 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:15:06 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:15:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:15:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:15:06 [http-nio-8080-exec-1] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:15:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:15:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:15:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:15:06 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:15:06 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:15:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:15:06 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:15:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:15:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:15:06 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:15:06 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:15:06 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:15:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:15:06 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:15:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:15:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:15:07 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:16:06 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:16:06 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:16:07 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:16:07 [http-nio-8080-exec-6] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:16:07 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:16:07 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:16:07 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:16:07 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:16:07 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:16:07 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:16:07 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:16:07 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:16:08 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:16:08 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:16:08 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:16:08 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:16:08 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:16:08 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:16:08 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:16:08 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:16:08 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:17:06 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:17:06 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:17:06 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:17:07 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:17:07 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:17:07 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:17:07 [http-nio-8080-exec-7] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:17:07 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:17:07 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:17:07 [http-nio-8080-exec-8] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:17:07 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:17:07 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:17:08 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:17:08 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:17:08 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:17:08 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:17:08 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:17:08 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:17:08 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:17:08 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:17:09 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:17:09 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:17:09 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:17:09 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:17:09 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:17:09 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:17:09 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:18:06 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:18:06 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:18:07 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:18:07 [http-nio-8080-exec-4] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:18:07 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:18:07 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:18:07 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:18:07 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:18:07 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:18:07 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:18:07 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:18:07 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:18:07 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:18:08 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:18:08 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:18:08 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:18:08 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:18:08 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:18:08 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:18:08 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:18:08 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:18:31 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:18:31 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:18:31 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:18:31 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:18:31 [http-nio-8080-exec-2] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:18:31 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:18:33 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:18:34 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:18:34 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:18:34 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:18:34 [http-nio-8080-exec-3] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:18:34 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:18:34 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:18:34 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:18:34 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:18:34 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:18:34 [http-nio-8080-exec-8] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:18:35 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:18:35 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system/user
2025-06-15 00:18:35 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:18:35 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:18:35 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system/user
2025-06-15 00:18:35 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:18:35 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:18:35 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:18:36 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:18:36 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:18:36 [http-nio-8080-exec-9] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:18:36 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:18:36 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:18:36 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:18:36 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:18:37 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:18:37 [http-nio-8080-exec-9] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:18:37 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:18:37 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system/user
2025-06-15 00:18:37 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:18:37 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:18:37 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system/user
2025-06-15 00:18:37 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:19:06 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:19:06 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:19:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:19:07 [http-nio-8080-exec-10] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:19:07 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:19:07 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:19:07 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:19:07 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:19:07 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:19:07 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:19:07 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:19:07 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:19:07 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:19:07 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:19:07 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:19:07 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:19:07 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:19:07 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:19:08 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:19:08 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:19:08 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:19:08 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:19:08 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:19:08 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:19:08 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:19:08 [http-nio-8080-exec-6] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:19:08 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:19:23 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:19:23 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:19:23 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:19:23 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:19:23 [http-nio-8080-exec-2] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:19:23 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:19:49 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:19:49 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:19:49 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:19:49 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:19:49 [http-nio-8080-exec-8] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:19:49 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:20:06 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:20:07 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:20:07 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:20:07 [http-nio-8080-exec-7] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:20:07 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:20:07 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:20:07 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:20:07 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:20:07 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:20:07 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:20:07 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:20:08 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:20:08 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:20:08 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:20:08 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:20:08 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:20:08 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:20:08 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:20:08 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:20:08 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:20:08 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:20:59 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:20:59 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:20:59 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:20:59 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:21:00 [http-nio-8080-exec-4] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:21:00 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:21:03 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:21:03 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:03 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:03 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:21:03 [http-nio-8080-exec-5] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:21:03 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:21:03 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:21:04 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:04 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:04 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:21:04 [http-nio-8080-exec-6] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:21:04 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:21:04 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /tasklist
2025-06-15 00:21:04 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:04 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:04 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /tasklist
2025-06-15 00:21:04 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:21:04 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:21:05 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:05 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:05 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:21:05 [http-nio-8080-exec-3] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:21:06 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:21:06 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:21:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:06 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:06 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:21:06 [http-nio-8080-exec-8] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:21:06 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:21:06 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /tasklist
2025-06-15 00:21:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:06 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:06 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /tasklist
2025-06-15 00:21:06 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:21:06 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:21:06 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:21:07 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:21:07 [http-nio-8080-exec-9] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:21:07 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:21:07 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:21:07 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:21:07 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:21:07 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:21:07 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:21:07 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:21:07 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:21:08 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:21:08 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:21:08 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:21:08 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:21:08 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:21:08 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:21:08 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:21:08 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:21:08 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:21:11 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:21:11 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:11 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:11 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:21:11 [http-nio-8080-exec-10] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:21:11 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:21:11 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:21:11 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:11 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:11 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:21:11 [http-nio-8080-exec-4] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:21:12 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:21:12 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /statistics
2025-06-15 00:21:12 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:12 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:12 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /statistics
2025-06-15 00:21:12 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:21:23 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:21:23 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:23 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:23 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:21:23 [http-nio-8080-exec-5] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:21:23 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:21:23 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:21:23 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:23 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:23 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:21:23 [http-nio-8080-exec-6] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:21:23 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:21:23 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /exchange
2025-06-15 00:21:23 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:23 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:23 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /exchange
2025-06-15 00:21:23 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:21:41 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:21:41 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:41 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:41 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:21:41 [http-nio-8080-exec-8] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:21:41 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:21:41 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:21:41 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:42 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:42 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:21:42 [http-nio-8080-exec-3] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:21:42 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:21:42 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system/user
2025-06-15 00:21:42 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:42 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:42 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system/user
2025-06-15 00:21:42 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:22:06 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:22:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:22:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:22:06 [http-nio-8080-exec-1] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:22:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:22:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:22:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:22:06 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:22:06 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:22:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:22:06 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:22:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:22:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:22:06 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:22:06 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:22:07 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:22:07 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:22:07 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:22:07 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:22:07 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:22:07 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:22:23 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:22:23 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:22:23 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:22:23 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:22:24 [http-nio-8080-exec-5] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:22:24 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:22:41 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:22:41 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:22:41 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:22:41 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:22:41 [http-nio-8080-exec-6] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:22:42 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:23:06 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:23:06 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:23:07 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:23:07 [http-nio-8080-exec-2] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:23:07 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:23:07 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:23:07 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:23:07 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:23:08 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:23:08 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:23:08 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:23:08 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:23:08 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:23:08 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:23:08 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:23:08 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:23:08 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:23:08 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:23:08 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:23:08 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:23:08 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:24:06 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:24:06 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:24:07 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:24:07 [http-nio-8080-exec-9] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:24:07 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:24:07 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:24:07 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:24:07 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:24:07 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:24:07 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:24:07 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:24:07 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:24:07 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:24:08 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:24:13 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:24:13 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:24:13 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:24:13 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:24:13 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:24:13 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:24:13 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:24:48 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:24:49 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:24:49 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:24:49 [http-nio-8080-exec-4] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:24:49 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:24:49 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:24:49 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:24:49 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:24:49 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:24:49 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:24:49 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:24:49 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:24:49 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:24:50 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:24:50 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:24:50 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:24:50 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:24:50 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:24:50 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:24:50 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:24:50 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:24:50 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:24:51 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:24:51 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:24:51 [http-nio-8080-exec-6] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:24:51 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:24:51 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:24:51 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:24:51 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:24:51 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:24:51 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:24:52 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:24:52 [http-nio-8080-exec-2] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:24:52 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:24:52 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:24:53 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:24:53 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:24:53 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:24:53 [http-nio-8080-exec-8] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:24:53 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:24:53 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:24:53 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:24:53 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:24:53 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:24:54 [http-nio-8080-exec-3] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:24:54 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system/menu
2025-06-15 00:24:54 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:24:54 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:24:54 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system/role
2025-06-15 00:24:54 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:24:54 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:24:54 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system/menu
2025-06-15 00:24:54 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:24:54 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:24:54 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system/role
2025-06-15 00:24:55 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:24:57 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:24:57 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:24:57 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:24:57 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:24:57 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:24:58 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:24:58 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:24:58 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:24:58 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:24:58 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:24:58 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:24:58 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:24:58 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:24:58 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:25:23 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:25:23 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:25:24 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:25:24 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:25:24 [http-nio-8080-exec-4] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:25:24 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:26:06 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:26:06 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:26:07 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:26:07 [http-nio-8080-exec-6] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:26:07 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:26:07 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:26:07 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:26:07 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:26:07 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:26:07 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:26:07 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:26:08 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:26:08 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:26:08 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:26:08 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:26:08 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:26:08 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:26:08 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:26:08 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:26:08 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:26:08 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:27:06 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:27:06 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:27:07 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:27:07 [http-nio-8080-exec-7] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:27:07 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:27:07 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:27:07 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:27:07 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:27:07 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:27:07 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:27:07 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:27:07 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:27:08 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:27:08 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:27:08 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:27:08 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:27:08 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:27:08 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:27:08 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:27:08 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:27:08 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:27:44 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:27:44 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:27:44 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:27:44 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:27:44 [http-nio-8080-exec-10] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:27:44 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:27:45 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-06-15 00:27:45 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:27:45 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:27:45 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-06-15 00:27:45 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:27:45 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/session-manager.js
2025-06-15 00:27:45 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:27:45 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:27:45 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/session-manager.js
2025-06-15 00:27:45 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:27:45 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /home
2025-06-15 00:27:45 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:27:45 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:27:45 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /home
2025-06-15 00:27:45 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:27:47 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:27:47 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:27:47 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:27:47 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:27:47 [http-nio-8080-exec-2] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:27:47 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:27:47 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:27:47 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:27:47 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:27:47 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:27:47 [http-nio-8080-exec-8] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:27:47 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:27:47 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system/user
2025-06-15 00:27:47 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:27:47 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:27:47 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system/user
2025-06-15 00:27:47 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:28:11 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:28:11 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:28:11 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:28:11 [http-nio-8080-exec-9] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:28:11 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:28:11 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:28:11 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:28:13 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:28:13 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:28:13 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:28:13 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:28:13 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:28:13 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:28:13 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:28:29 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:28:29 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:28:29 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:28:29 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:28:29 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:28:29 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:28:29 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:28:34 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-15 00:28:34 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-15 01:16:59 [main] INFO  com.cf.financing.FinancingSystemApplication - Starting FinancingSystemApplication using Java 21.0.4 on JIANGKUNYIN with PID 40212 (D:\projects\test\financing-system\target\classes started by jky19 in D:\projects\test\financing-system)
2025-06-15 01:16:59 [main] DEBUG com.cf.financing.FinancingSystemApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-15 01:16:59 [main] INFO  com.cf.financing.FinancingSystemApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-15 01:17:00 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-15 01:17:01 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-15 01:17:01 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-15 01:17:01 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-15 01:17:01 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1260 ms
2025-06-15 01:17:01 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-15 01:17:02 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-15 01:17:02 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@4afd65fd, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@141aba65, org.springframework.security.web.context.SecurityContextPersistenceFilter@376b5cb2, org.springframework.security.web.header.HeaderWriterFilter@2ae3235e, org.springframework.security.web.authentication.logout.LogoutFilter@5f409872, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@5e0f2c82, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@106cd9c8, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@11abd6c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3356ff58, org.springframework.security.web.session.SessionManagementFilter@3686389, org.springframework.security.web.access.ExceptionTranslationFilter@9d99851, org.springframework.security.web.access.intercept.AuthorizationFilter@7b44bfb8]
2025-06-15 01:17:03 [main] INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-15 01:17:03 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-15 01:17:03 [main] INFO  com.cf.financing.FinancingSystemApplication - Started FinancingSystemApplication in 4.11 seconds (JVM running for 4.464)
2025-06-15 01:17:06 [http-nio-8080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-15 01:17:06 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-15 01:17:06 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-15 01:17:06 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 01:17:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:17:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:17:06 [http-nio-8080-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 5A078908EEC6B738FDFAB3291F10F2DF
2025-06-15 01:17:06 [http-nio-8080-exec-1] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 01:17:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 01:17:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 01:17:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:17:06 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 01:17:06 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:17:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:17:06 [http-nio-8080-exec-8] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 5A078908EEC6B738FDFAB3291F10F2DF
2025-06-15 01:17:06 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 01:17:07 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:17:07 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:17:07 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:17:07 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 01:17:07 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:17:07 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:17:07 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 01:17:07 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:17:07 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:17:07 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:17:44 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 01:17:44 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:17:44 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:17:44 [http-nio-8080-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 796CD487577BA0141CD1BCEDF790B04E
2025-06-15 01:17:44 [http-nio-8080-exec-6] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 01:17:44 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 01:17:44 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 01:17:44 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:17:44 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 01:17:44 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:17:44 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:17:44 [http-nio-8080-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id 796CD487577BA0141CD1BCEDF790B04E
2025-06-15 01:17:44 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 01:17:44 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:17:44 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:17:44 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:17:44 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 01:17:44 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:17:44 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:17:44 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 01:17:44 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:17:44 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:17:44 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:17:47 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /
2025-06-15 01:17:47 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:17:47 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:17:47 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /
2025-06-15 01:17:47 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:17:47 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:17:47 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:17:47 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 01:17:47 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:17:47 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:17:47 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 01:17:47 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:17:47 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:17:47 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:17:47 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-06-15 01:17:47 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:17:47 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:17:47 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-06-15 01:17:47 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/login.js
2025-06-15 01:17:47 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:17:47 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:17:47 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/login.js
2025-06-15 01:17:47 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:17:47 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:17:47 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:17:47 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:18:02 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /login
2025-06-15 01:18:02 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:18:02 [http-nio-8080-exec-2] DEBUG o.s.s.authentication.dao.DaoAuthenticationProvider - Authenticated user
2025-06-15 01:18:02 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.s.ChangeSessionIdAuthenticationStrategy - Changed session id from D6548ECFA2F115F0425738257851E674
2025-06-15 01:18:02 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.UsernamePasswordAuthenticationFilter - Set SecurityContextHolder to UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=D6548ECFA2F115F0425738257851E674], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]
2025-06-15 01:18:02 [http-nio-8080-exec-2] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to /dashboard
2025-06-15 01:18:02 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Stored SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=D6548ECFA2F115F0425738257851E674], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]] to HttpSession [org.apache.catalina.session.StandardSessionFacade@1a139f07]
2025-06-15 01:18:02 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Stored SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=D6548ECFA2F115F0425738257851E674], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]] to HttpSession [org.apache.catalina.session.StandardSessionFacade@1a139f07]
2025-06-15 01:18:02 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:18:02 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-06-15 01:18:02 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=D6548ECFA2F115F0425738257851E674], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 01:18:02 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=D6548ECFA2F115F0425738257851E674], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 01:18:02 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-06-15 01:18:02 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:18:02 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /home
2025-06-15 01:18:02 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=D6548ECFA2F115F0425738257851E674], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 01:18:02 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=D6548ECFA2F115F0425738257851E674], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 01:18:02 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /home
2025-06-15 01:18:02 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:18:02 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/menu/user-menus
2025-06-15 01:18:02 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=D6548ECFA2F115F0425738257851E674], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 01:18:02 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=D6548ECFA2F115F0425738257851E674], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 01:18:02 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/menu/user-menus
2025-06-15 01:18:02 [http-nio-8080-exec-4] ERROR com.cf.financing.controller.MenuController - 获取用户菜单失败
org.apache.ibatis.binding.BindingException: Invalid bound statement (not found): com.cf.financing.mapper.SysMenuMapper.selectUserMenuTree
	at org.apache.ibatis.binding.MapperMethod$SqlCommand.<init>(MapperMethod.java:235)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.<init>(MybatisMapperMethod.java:50)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.lambda$cachedInvoker$0(MybatisMapperProxy.java:111)
	at java.base/java.util.concurrent.ConcurrentHashMap.computeIfAbsent(ConcurrentHashMap.java:1708)
	at com.baomidou.mybatisplus.core.toolkit.CollectionUtils.computeIfAbsent(CollectionUtils.java:115)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.cachedInvoker(MybatisMapperProxy.java:98)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy80.selectUserMenuTree(Unknown Source)
	at com.cf.financing.service.impl.SysMenuServiceImpl.selectUserMenuTree(SysMenuServiceImpl.java:66)
	at com.cf.financing.service.impl.SysMenuServiceImpl$$FastClassBySpringCGLIB$$ee166329.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704)
	at com.cf.financing.service.impl.SysMenuServiceImpl$$EnhancerBySpringCGLIB$$107ebf4.selectUserMenuTree(<generated>)
	at com.cf.financing.controller.MenuController.getUserMenus(MenuController.java:45)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:337)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:96)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:223)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:926)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-15 01:18:02 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:18:04 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 01:18:04 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=D6548ECFA2F115F0425738257851E674], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 01:18:04 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=D6548ECFA2F115F0425738257851E674], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 01:18:04 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 01:18:04 [http-nio-8080-exec-7] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 01:18:04 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:18:04 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 01:18:04 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=D6548ECFA2F115F0425738257851E674], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 01:18:04 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=D6548ECFA2F115F0425738257851E674], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 01:18:04 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 01:18:04 [http-nio-8080-exec-10] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 01:18:04 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:18:04 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /home
2025-06-15 01:18:04 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=D6548ECFA2F115F0425738257851E674], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 01:18:04 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=D6548ECFA2F115F0425738257851E674], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 01:18:04 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /home
2025-06-15 01:18:04 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:18:06 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 01:18:06 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:18:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:18:06 [http-nio-8080-exec-9] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 01:18:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 01:18:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 01:18:06 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:18:06 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 01:18:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:18:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:18:06 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 01:18:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:18:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:18:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:18:06 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 01:18:06 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:18:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:18:06 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 01:18:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:18:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:18:06 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:18:16 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 01:18:16 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=D6548ECFA2F115F0425738257851E674], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 01:18:16 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=D6548ECFA2F115F0425738257851E674], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 01:18:16 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 01:18:16 [http-nio-8080-exec-2] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 01:18:16 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:18:16 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 01:18:16 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=D6548ECFA2F115F0425738257851E674], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 01:18:16 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=D6548ECFA2F115F0425738257851E674], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 01:18:16 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 01:18:16 [http-nio-8080-exec-6] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 01:18:16 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:18:16 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /home
2025-06-15 01:18:16 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=D6548ECFA2F115F0425738257851E674], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 01:18:16 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=D6548ECFA2F115F0425738257851E674], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 01:18:16 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /home
2025-06-15 01:18:16 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:20:06 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 01:20:06 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:20:06 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:20:06 [http-nio-8080-exec-5] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 01:20:06 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 01:20:06 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 01:20:06 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:20:06 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 01:20:06 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:20:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:20:06 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 01:20:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:20:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:20:06 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:20:06 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 01:20:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:20:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:20:06 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 01:20:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:20:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:20:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:21:00 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 01:21:00 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=D6548ECFA2F115F0425738257851E674], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 01:21:00 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=D6548ECFA2F115F0425738257851E674], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 01:21:00 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 01:21:00 [http-nio-8080-exec-8] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 01:21:00 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:21:02 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 01:21:02 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=D6548ECFA2F115F0425738257851E674], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 01:21:02 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=D6548ECFA2F115F0425738257851E674], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 01:21:02 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 01:21:02 [http-nio-8080-exec-2] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 01:21:02 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:21:06 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 01:21:06 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:21:06 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:21:06 [http-nio-8080-exec-6] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 01:21:06 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 01:21:06 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 01:21:06 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:21:06 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 01:21:06 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:21:06 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:21:06 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 01:21:06 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:21:06 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:21:06 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:21:06 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 01:21:06 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:21:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:21:06 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 01:21:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:21:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:21:06 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:21:22 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 01:21:22 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=D6548ECFA2F115F0425738257851E674], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 01:21:22 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=D6548ECFA2F115F0425738257851E674], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 01:21:22 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 01:21:22 [http-nio-8080-exec-7] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 01:21:22 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:23:06 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 01:23:06 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:23:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:23:06 [http-nio-8080-exec-9] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 01:23:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 01:23:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 01:23:06 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:23:06 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 01:23:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:23:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:23:06 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 01:23:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:23:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:23:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:23:06 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 01:23:06 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:23:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:23:06 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 01:23:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:23:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:23:06 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:23:25 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 01:23:25 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:23:25 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:23:25 [http-nio-8080-exec-2] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 01:23:25 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 01:23:25 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 01:23:25 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:23:25 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 01:23:25 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:23:25 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:23:25 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 01:23:25 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:23:25 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:23:25 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:23:25 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 01:23:25 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:23:25 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:23:25 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 01:23:25 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:23:25 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:23:25 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:23:36 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 01:23:36 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=D6548ECFA2F115F0425738257851E674], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 01:23:36 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=D6548ECFA2F115F0425738257851E674], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 01:23:36 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 01:23:36 [http-nio-8080-exec-4] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 01:23:36 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:23:37 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 01:23:37 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:23:37 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:23:37 [http-nio-8080-exec-7] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 01:23:37 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 01:23:37 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 01:23:37 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:23:37 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 01:23:37 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:23:37 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:23:37 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 01:23:37 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:23:37 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:23:37 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:23:37 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 01:23:37 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:23:37 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:23:37 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 01:23:37 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:23:37 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:23:37 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:24:02 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 01:24:02 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=D6548ECFA2F115F0425738257851E674], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 01:24:02 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=D6548ECFA2F115F0425738257851E674], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 01:24:02 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 01:24:02 [http-nio-8080-exec-9] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 01:24:02 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:24:06 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 01:24:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:24:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:24:06 [http-nio-8080-exec-1] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 01:24:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 01:24:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 01:24:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:24:06 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 01:24:06 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:24:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:24:06 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 01:24:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:24:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:24:06 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:24:06 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 01:24:06 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:24:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:24:06 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 01:24:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:24:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:24:06 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:24:48 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 01:24:48 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=D6548ECFA2F115F0425738257851E674], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 01:24:48 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=D6548ECFA2F115F0425738257851E674], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 01:24:48 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 01:24:48 [http-nio-8080-exec-6] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 01:24:48 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:25:06 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 01:25:06 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:25:06 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:25:06 [http-nio-8080-exec-3] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 01:25:06 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 01:25:06 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 01:25:06 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:25:06 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 01:25:06 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:25:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:25:06 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 01:25:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:25:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:25:06 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:25:06 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 01:25:06 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:25:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:25:06 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 01:25:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:25:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:25:06 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:33:23 [main] INFO  com.cf.financing.FinancingSystemApplication - Starting FinancingSystemApplication using Java 21.0.4 on JIANGKUNYIN with PID 34704 (D:\projects\test\financing-system\target\classes started by jky19 in D:\projects\test\financing-system)
2025-06-15 01:33:23 [main] DEBUG com.cf.financing.FinancingSystemApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-15 01:33:23 [main] INFO  com.cf.financing.FinancingSystemApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-15 01:33:25 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-15 01:33:26 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-15 01:33:26 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-15 01:33:27 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-15 01:33:27 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3826 ms
2025-06-15 01:33:27 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-15 01:33:29 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-15 01:33:30 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@4ab66127, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@236861da, org.springframework.security.web.context.SecurityContextPersistenceFilter@4522d793, org.springframework.security.web.header.HeaderWriterFilter@52ca0ad4, org.springframework.security.web.authentication.logout.LogoutFilter@376b5cb2, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@21a6a494, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4438b862, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5cdf221a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@37fef327, org.springframework.security.web.session.SessionManagementFilter@6658f08a, org.springframework.security.web.access.ExceptionTranslationFilter@6598caab, org.springframework.security.web.access.intercept.AuthorizationFilter@75839695]
2025-06-15 01:33:30 [main] INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-15 01:33:31 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-15 01:33:31 [main] INFO  com.cf.financing.FinancingSystemApplication - Started FinancingSystemApplication in 8.368 seconds (JVM running for 8.957)
2025-06-15 01:33:51 [http-nio-8080-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-15 01:33:51 [http-nio-8080-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-15 01:33:51 [http-nio-8080-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-15 01:33:51 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 01:33:51 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:33:51 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:33:51 [http-nio-8080-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id EB36622E9124D28C3AB12DBEE678AB2B
2025-06-15 01:33:51 [http-nio-8080-exec-2] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 01:33:51 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 01:33:51 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 01:33:51 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:33:51 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /
2025-06-15 01:33:51 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:33:51 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:33:51 [http-nio-8080-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id EB36622E9124D28C3AB12DBEE678AB2B
2025-06-15 01:33:51 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /
2025-06-15 01:33:51 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 01:33:51 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:33:51 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:33:51 [http-nio-8080-exec-3] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id EB36622E9124D28C3AB12DBEE678AB2B
2025-06-15 01:33:51 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 01:33:51 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:33:51 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:33:51 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:33:52 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:33:52 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:33:52 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:33:52 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 01:33:52 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:33:52 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:33:52 [http-nio-8080-exec-7] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id EB36622E9124D28C3AB12DBEE678AB2B
2025-06-15 01:33:52 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 01:33:52 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 01:33:52 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:33:52 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:33:52 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 01:33:52 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:33:52 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:33:52 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:33:52 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:33:52 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:33:52 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:34:06 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 01:34:06 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:34:06 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:34:06 [http-nio-8080-exec-6] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id BE8F5E8CA1549FB15FCD31B18C734A67
2025-06-15 01:34:06 [http-nio-8080-exec-6] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 01:34:06 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 01:34:06 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 01:34:06 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:34:06 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 01:34:06 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:34:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:34:06 [http-nio-8080-exec-4] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id BE8F5E8CA1549FB15FCD31B18C734A67
2025-06-15 01:34:06 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 01:34:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:34:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:34:06 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:34:06 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 01:34:06 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:34:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:34:06 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 01:34:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:34:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:34:06 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:34:08 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing POST /login
2025-06-15 01:34:08 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:34:09 [http-nio-8080-exec-9] ERROR druid.sql.Statement - {conn-10005, pstmt-20005} execute error. SELECT
            u.id, u.username, u.password, u.real_name, u.phone, u.email, u.status, u.dept_id, u.role_id,
            u.last_login_time, u.last_login_ip, u.login_count, u.create_time, u.update_time,
            d.dept_name,
            r.id as role_id, r.role_code, r.role_name, r.description as role_description, r.status as role_status
        FROM sys_user u
        LEFT JOIN sys_department d ON u.dept_id = d.id
        LEFT JOIN sys_user_role ur ON u.id = ur.user_id
        LEFT JOIN sys_role r ON ur.role_id = r.id
        WHERE u.username = ?
java.sql.SQLSyntaxErrorException: Table 'financing.sys_department' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3446)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.wall.WallFilter.preparedStatement_execute(WallFilter.java:639)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at jdk.proxy3/jdk.proxy3.$Proxy103.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:136)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	at jdk.proxy2/jdk.proxy2.$Proxy73.selectOne(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:160)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:89)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy74.selectUserByUsername(Unknown Source)
	at com.cf.financing.service.CustomUserDetailsService.loadUserByUsername(CustomUserDetailsService.java:40)
	at org.springframework.security.authentication.dao.DaoAuthenticationProvider.retrieveUser(DaoAuthenticationProvider.java:94)
	at org.springframework.security.authentication.dao.AbstractUserDetailsAuthenticationProvider.authenticate(AbstractUserDetailsAuthenticationProvider.java:133)
	at org.springframework.security.authentication.ProviderManager.authenticate(ProviderManager.java:182)
	at org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter.attemptAuthentication(UsernamePasswordAuthenticationFilter.java:85)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:926)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-06-15 01:34:09 [http-nio-8080-exec-9] DEBUG o.s.s.a.DefaultAuthenticationEventPublisher - No event was found for the exception org.springframework.security.authentication.InternalAuthenticationServiceException
2025-06-15 01:34:09 [http-nio-8080-exec-9] ERROR o.s.s.w.a.UsernamePasswordAuthenticationFilter - An internal error occurred while trying to authenticate the user.
org.springframework.security.authentication.InternalAuthenticationServiceException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'financing.sys_department' doesn't exist
### The error may exist in file [D:\projects\test\financing-system\target\classes\mapper\SysUserMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT             u.id, u.username, u.password, u.real_name, u.phone, u.email, u.status, u.dept_id, u.role_id,             u.last_login_time, u.last_login_ip, u.login_count, u.create_time, u.update_time,             d.dept_name,             r.id as role_id, r.role_code, r.role_name, r.description as role_description, r.status as role_status         FROM sys_user u         LEFT JOIN sys_department d ON u.dept_id = d.id         LEFT JOIN sys_user_role ur ON u.id = ur.user_id         LEFT JOIN sys_role r ON ur.role_id = r.id         WHERE u.username = ?
### Cause: java.sql.SQLSyntaxErrorException: Table 'financing.sys_department' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'financing.sys_department' doesn't exist
	at org.springframework.security.authentication.dao.DaoAuthenticationProvider.retrieveUser(DaoAuthenticationProvider.java:109)
	at org.springframework.security.authentication.dao.AbstractUserDetailsAuthenticationProvider.authenticate(AbstractUserDetailsAuthenticationProvider.java:133)
	at org.springframework.security.authentication.ProviderManager.authenticate(ProviderManager.java:182)
	at org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter.attemptAuthentication(UsernamePasswordAuthenticationFilter.java:85)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:227)
	at org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter.doFilter(AbstractAuthenticationProcessingFilter.java:217)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:346)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:221)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:186)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:926)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1791)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Table 'financing.sys_department' doesn't exist
### The error may exist in file [D:\projects\test\financing-system\target\classes\mapper\SysUserMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT             u.id, u.username, u.password, u.real_name, u.phone, u.email, u.status, u.dept_id, u.role_id,             u.last_login_time, u.last_login_ip, u.login_count, u.create_time, u.update_time,             d.dept_name,             r.id as role_id, r.role_code, r.role_name, r.description as role_description, r.status as role_status         FROM sys_user u         LEFT JOIN sys_department d ON u.dept_id = d.id         LEFT JOIN sys_user_role ur ON u.id = ur.user_id         LEFT JOIN sys_role r ON ur.role_id = r.id         WHERE u.username = ?
### Cause: java.sql.SQLSyntaxErrorException: Table 'financing.sys_department' doesn't exist
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: Table 'financing.sys_department' doesn't exist
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
	at jdk.proxy2/jdk.proxy2.$Proxy73.selectOne(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:160)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:89)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
	at jdk.proxy2/jdk.proxy2.$Proxy74.selectUserByUsername(Unknown Source)
	at com.cf.financing.service.CustomUserDetailsService.loadUserByUsername(CustomUserDetailsService.java:40)
	at org.springframework.security.authentication.dao.DaoAuthenticationProvider.retrieveUser(DaoAuthenticationProvider.java:94)
	... 56 common frames omitted
Caused by: java.sql.SQLSyntaxErrorException: Table 'financing.sys_department' doesn't exist
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:121)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3446)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.wall.WallFilter.preparedStatement_execute(WallFilter.java:639)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:158)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at jdk.proxy3/jdk.proxy3.$Proxy103.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:136)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	... 64 common frames omitted
2025-06-15 01:34:09 [http-nio-8080-exec-9] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to /login?error
2025-06-15 01:34:09 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 01:34:09 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 01:34:09 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:34:09 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login?error
2025-06-15 01:34:09 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:34:09 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:34:09 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login?error
2025-06-15 01:34:09 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:34:09 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:34:09 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:35:07 [main] INFO  com.cf.financing.FinancingSystemApplication - Starting FinancingSystemApplication using Java 21.0.4 on JIANGKUNYIN with PID 38376 (D:\projects\test\financing-system\target\classes started by jky19 in D:\projects\test\financing-system)
2025-06-15 01:35:07 [main] DEBUG com.cf.financing.FinancingSystemApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-15 01:35:07 [main] INFO  com.cf.financing.FinancingSystemApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-15 01:35:09 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-15 01:35:09 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-15 01:35:09 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-15 01:35:09 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-15 01:35:09 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2129 ms
2025-06-15 01:35:09 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-15 01:35:11 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-15 01:35:12 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@45e7bb79, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@605c7a9e, org.springframework.security.web.context.SecurityContextPersistenceFilter@236861da, org.springframework.security.web.header.HeaderWriterFilter@7608a838, org.springframework.security.web.authentication.logout.LogoutFilter@141aba65, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@9f9146d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2cd3fc29, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@46b5f061, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@21c75084, org.springframework.security.web.session.SessionManagementFilter@21a6a494, org.springframework.security.web.access.ExceptionTranslationFilter@11abd6c, org.springframework.security.web.access.intercept.AuthorizationFilter@1426370c]
2025-06-15 01:35:12 [main] INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-15 01:35:13 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-15 01:35:13 [main] INFO  com.cf.financing.FinancingSystemApplication - Started FinancingSystemApplication in 7.134 seconds (JVM running for 7.72)
2025-06-15 01:35:13 [http-nio-8080-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-15 01:35:13 [http-nio-8080-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-15 01:35:13 [http-nio-8080-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-15 01:35:13 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 01:35:13 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:35:13 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:35:13 [http-nio-8080-exec-2] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id A5A486C04223DC08204C467FBB273764
2025-06-15 01:35:13 [http-nio-8080-exec-2] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 01:35:13 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 01:35:13 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 01:35:13 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:35:13 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 01:35:13 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:35:13 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:35:13 [http-nio-8080-exec-1] DEBUG o.s.security.web.session.SessionManagementFilter - Request requested invalid session id A5A486C04223DC08204C467FBB273764
2025-06-15 01:35:13 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 01:35:13 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:35:14 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:35:14 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:35:14 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 01:35:14 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:35:14 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:35:14 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 01:35:14 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:35:14 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:35:14 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:35:19 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /
2025-06-15 01:35:19 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:35:19 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:35:19 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /
2025-06-15 01:35:19 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:35:19 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:35:19 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:35:19 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 01:35:19 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:35:19 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:35:19 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 01:35:19 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:35:19 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:35:19 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:35:19 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /images/logo.png
2025-06-15 01:35:19 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/login.js
2025-06-15 01:35:19 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:35:19 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:35:19 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:35:19 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:35:19 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /images/logo.png
2025-06-15 01:35:19 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/login.js
2025-06-15 01:35:19 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:35:19 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:35:19 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:35:19 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:36:06 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 01:36:06 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:36:06 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:36:06 [http-nio-8080-exec-5] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 01:36:06 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 01:36:06 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 01:36:06 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:36:06 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 01:36:06 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:36:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:36:06 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 01:36:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:36:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:36:06 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:36:06 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 01:36:06 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 01:36:06 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 01:36:06 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 01:36:06 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:36:06 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 01:36:06 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 01:36:35 [main] INFO  com.cf.financing.FinancingSystemApplication - Starting FinancingSystemApplication using Java 21.0.4 on JIANGKUNYIN with PID 4496 (D:\projects\test\financing-system\target\classes started by jky19 in D:\projects\test\financing-system)
2025-06-15 01:36:35 [main] DEBUG com.cf.financing.FinancingSystemApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-06-15 01:36:35 [main] INFO  com.cf.financing.FinancingSystemApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-15 01:36:37 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-15 01:36:37 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-15 01:36:37 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-06-15 01:36:37 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-15 01:36:37 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1756 ms
2025-06-15 01:36:37 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-15 01:36:38 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-15 01:36:39 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@d16209e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@75527e36, org.springframework.security.web.context.SecurityContextPersistenceFilter@55951fcd, org.springframework.security.web.header.HeaderWriterFilter@794c5f5e, org.springframework.security.web.authentication.logout.LogoutFilter@2aa6bbad, org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter@605c7a9e, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7534785a, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@46ab4efc, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@76596288, org.springframework.security.web.session.SessionManagementFilter@236861da, org.springframework.security.web.access.ExceptionTranslationFilter@75839695, org.springframework.security.web.access.intercept.AuthorizationFilter@45e7bb79]
2025-06-15 01:36:40 [main] INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page template: index
2025-06-15 01:36:40 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8080 is already in use
2025-06-15 01:36:40 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-15 01:36:40 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-15 01:36:40 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-06-15 01:36:40 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-15 01:36:40 [main] ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

