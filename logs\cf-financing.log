2025-06-15 00:00:06 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:00:06 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:00:06 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-1] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:00:06 [http-nio-8080-exec-10] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:00:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:00:06 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:00:06 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:00:06 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:00:06 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:00:06 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:00:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:00:06 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:00:06 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:00:06 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:00:06 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:00:06 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:00:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:00:06 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:00:06 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:01:06 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:01:06 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:01:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:01:06 [http-nio-8080-exec-7] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:01:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:01:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:01:06 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:01:06 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:01:06 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:01:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:01:06 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:01:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:01:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:01:06 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:01:06 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:01:06 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:01:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:01:06 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:01:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:01:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:01:06 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:01:23 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:01:23 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:01:23 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:01:23 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:01:23 [http-nio-8080-exec-10] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:01:23 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:02:06 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:02:06 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:02:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:02:06 [http-nio-8080-exec-4] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:02:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:02:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:02:06 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:02:06 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:02:06 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:02:06 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:02:06 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:02:06 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:02:06 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:02:06 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:02:06 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:02:06 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:02:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:02:06 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:02:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:02:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:02:06 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:03:06 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:03:06 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:03:06 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-3] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:03:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-7] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:03:06 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:03:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:03:06 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:03:06 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:03:06 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:03:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:03:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:03:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:03:06 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:03:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:03:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:03:06 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:03:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:03:06 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:03:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:04:06 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:04:06 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:04:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:04:06 [http-nio-8080-exec-4] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:04:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:04:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:04:06 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:04:06 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:04:06 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:04:06 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:04:06 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:04:06 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:04:06 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:04:06 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:04:06 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:04:06 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:04:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:04:06 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:04:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:04:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:04:06 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:04:10 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:04:10 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:10 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:10 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:04:10 [http-nio-8080-exec-3] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:04:10 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:04:12 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:04:12 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:12 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:12 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:04:12 [http-nio-8080-exec-7] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:04:12 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:04:12 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:04:12 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:12 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:12 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:04:12 [http-nio-8080-exec-8] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:04:12 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:04:12 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system/menu
2025-06-15 00:04:12 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:12 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:12 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system/menu
2025-06-15 00:04:12 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:04:18 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:04:18 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:18 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:18 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:04:18 [http-nio-8080-exec-1] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:04:18 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:04:18 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:04:18 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:18 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:18 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:04:18 [http-nio-8080-exec-10] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:04:18 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:04:18 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system/user
2025-06-15 00:04:18 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:18 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:18 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system/user
2025-06-15 00:04:18 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:04:23 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:04:23 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:23 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:23 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:04:23 [http-nio-8080-exec-5] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:04:23 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:04:50 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:04:50 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:50 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:50 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:04:50 [http-nio-8080-exec-2] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:04:50 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:04:50 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:04:50 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:50 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:50 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:04:50 [http-nio-8080-exec-6] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:04:50 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:04:50 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system/role
2025-06-15 00:04:50 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:50 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:50 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system/role
2025-06-15 00:04:50 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:04:52 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:04:52 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:52 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:52 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:04:52 [http-nio-8080-exec-7] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:04:52 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:04:52 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:04:52 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:52 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:52 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:04:52 [http-nio-8080-exec-8] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:04:52 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:04:52 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system/menu
2025-06-15 00:04:52 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:52 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:04:52 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system/menu
2025-06-15 00:04:52 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:05:06 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:05:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:05:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:05:06 [http-nio-8080-exec-1] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:05:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:05:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:05:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:05:06 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:05:06 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:05:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:05:06 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:05:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:05:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:05:06 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:05:06 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:05:06 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:05:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:05:06 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:05:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:05:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:05:06 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:06:06 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:06:06 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:06:06 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-6] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:06:06 [http-nio-8080-exec-2] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:06:06 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:06:06 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:06:06 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:06:06 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:06:06 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:06:06 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:06:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:06:06 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:06:06 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:06:06 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:06:06 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:06:06 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:06:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:06:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:06:06 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:07:06 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:07:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:07:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:07:06 [http-nio-8080-exec-1] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:07:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:07:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:07:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:07:06 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:07:06 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:07:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:07:06 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:07:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:07:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:07:06 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:07:06 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:07:06 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:07:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:07:06 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:07:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:07:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:07:06 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:07:23 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:07:23 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:07:23 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:07:23 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:07:23 [http-nio-8080-exec-6] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:07:23 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:08:16 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:08:16 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:08:16 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:08:16 [http-nio-8080-exec-3] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:08:16 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:08:16 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:08:16 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:08:45 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:08:45 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:08:45 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:08:45 [http-nio-8080-exec-8] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:08:45 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:08:45 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:08:45 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:08:50 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:08:50 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:08:50 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:08:50 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:08:50 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:08:50 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:08:50 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:08:50 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:08:50 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:08:50 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:08:50 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:08:50 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:08:50 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:08:50 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:08:52 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:08:52 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:08:52 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:08:52 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:08:52 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:08:52 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:08:52 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:08:52 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:08:52 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:08:52 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:08:52 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:08:52 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:08:52 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:08:52 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:08:54 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:08:54 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:08:54 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:08:54 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:08:54 [http-nio-8080-exec-5] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:08:54 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:09:06 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:09:06 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:09:06 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:09:06 [http-nio-8080-exec-6] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:09:06 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:09:06 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:09:06 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:09:45 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:09:45 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:09:45 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:09:45 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:09:45 [http-nio-8080-exec-2] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:09:45 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:10:06 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:10:06 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:10:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:10:06 [http-nio-8080-exec-7] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:10:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:10:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:10:06 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:10:19 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:10:19 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:10:19 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:10:19 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:10:19 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:10:19 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:10:19 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:10:19 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:10:19 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:10:19 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:10:19 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:10:19 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:10:19 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:10:19 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:10:30 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:10:30 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:10:30 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:10:30 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:10:30 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:10:30 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:10:30 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:10:30 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:10:30 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:10:30 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:10:30 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:10:30 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:10:30 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:10:30 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:11:23 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:11:23 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:11:23 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:11:23 [http-nio-8080-exec-2] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:11:23 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:11:23 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:11:23 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:11:23 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:11:23 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:11:23 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:11:23 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:11:23 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:11:23 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:11:23 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:11:23 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:11:23 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:11:23 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:11:23 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:11:23 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:11:23 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:11:23 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:11:48 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:11:48 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:11:48 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:11:48 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:11:48 [http-nio-8080-exec-8] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:11:48 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:12:06 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:12:06 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:12:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:12:06 [http-nio-8080-exec-9] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:12:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:12:06 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:12:06 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:12:06 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:12:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:12:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:12:06 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:12:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:12:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:12:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:12:06 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:12:06 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:12:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:12:06 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:12:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:12:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:12:06 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:13:06 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:13:06 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:13:06 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:13:06 [http-nio-8080-exec-5] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:13:06 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:13:06 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:13:06 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:13:06 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:13:06 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:13:06 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:13:06 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:13:06 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:13:06 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:13:06 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:13:06 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:13:06 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:13:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:13:06 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:13:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:13:06 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:13:06 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:14:06 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:14:06 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:14:06 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:14:06 [http-nio-8080-exec-3] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:14:06 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:14:06 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:14:06 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:14:06 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:14:06 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:14:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:14:06 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:14:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:14:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:14:06 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:14:06 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:14:06 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:14:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:14:06 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:14:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:14:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:14:06 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:14:07 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:14:07 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:14:07 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:14:07 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:14:07 [http-nio-8080-exec-9] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:14:07 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:15:06 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:15:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:15:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:15:06 [http-nio-8080-exec-1] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:15:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:15:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:15:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:15:06 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:15:06 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:15:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:15:06 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:15:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:15:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:15:06 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:15:06 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:15:06 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:15:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:15:06 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:15:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:15:06 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:15:07 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:16:06 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:16:06 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:16:07 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:16:07 [http-nio-8080-exec-6] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:16:07 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:16:07 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:16:07 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:16:07 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:16:07 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:16:07 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:16:07 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:16:07 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:16:08 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:16:08 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:16:08 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:16:08 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:16:08 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:16:08 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:16:08 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:16:08 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:16:08 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:17:06 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:17:06 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:17:06 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:17:07 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:17:07 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:17:07 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:17:07 [http-nio-8080-exec-7] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:17:07 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:17:07 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:17:07 [http-nio-8080-exec-8] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:17:07 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:17:07 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:17:08 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:17:08 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:17:08 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:17:08 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:17:08 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:17:08 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:17:08 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:17:08 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:17:09 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:17:09 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:17:09 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:17:09 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:17:09 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:17:09 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:17:09 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:18:06 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:18:06 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:18:07 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:18:07 [http-nio-8080-exec-4] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:18:07 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:18:07 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:18:07 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:18:07 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:18:07 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:18:07 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:18:07 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:18:07 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:18:07 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:18:08 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:18:08 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:18:08 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:18:08 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:18:08 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:18:08 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:18:08 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:18:08 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:18:31 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:18:31 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:18:31 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:18:31 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:18:31 [http-nio-8080-exec-2] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:18:31 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:18:33 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:18:34 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:18:34 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:18:34 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:18:34 [http-nio-8080-exec-3] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:18:34 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:18:34 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:18:34 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:18:34 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:18:34 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:18:34 [http-nio-8080-exec-8] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:18:35 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:18:35 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system/user
2025-06-15 00:18:35 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:18:35 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:18:35 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system/user
2025-06-15 00:18:35 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:18:35 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:18:35 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:18:36 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:18:36 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:18:36 [http-nio-8080-exec-9] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:18:36 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:18:36 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:18:36 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:18:36 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:18:37 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:18:37 [http-nio-8080-exec-9] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:18:37 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:18:37 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system/user
2025-06-15 00:18:37 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:18:37 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:18:37 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system/user
2025-06-15 00:18:37 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:19:06 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:19:06 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:19:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:19:07 [http-nio-8080-exec-10] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:19:07 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:19:07 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:19:07 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:19:07 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:19:07 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:19:07 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:19:07 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:19:07 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:19:07 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:19:07 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:19:07 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:19:07 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:19:07 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:19:07 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:19:08 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:19:08 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:19:08 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:19:08 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:19:08 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:19:08 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:19:08 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:19:08 [http-nio-8080-exec-6] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:19:08 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:19:23 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:19:23 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:19:23 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:19:23 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:19:23 [http-nio-8080-exec-2] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:19:23 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:19:49 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:19:49 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:19:49 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:19:49 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:19:49 [http-nio-8080-exec-8] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:19:49 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:20:06 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:20:07 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:20:07 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:20:07 [http-nio-8080-exec-7] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:20:07 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:20:07 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:20:07 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:20:07 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:20:07 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:20:07 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:20:07 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:20:08 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:20:08 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:20:08 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:20:08 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:20:08 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:20:08 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:20:08 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:20:08 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:20:08 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:20:08 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:20:59 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:20:59 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:20:59 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:20:59 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:21:00 [http-nio-8080-exec-4] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:21:00 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:21:03 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:21:03 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:03 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:03 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:21:03 [http-nio-8080-exec-5] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:21:03 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:21:03 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:21:04 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:04 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:04 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:21:04 [http-nio-8080-exec-6] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:21:04 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:21:04 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /tasklist
2025-06-15 00:21:04 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:04 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:04 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /tasklist
2025-06-15 00:21:04 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:21:04 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:21:05 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:05 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:05 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:21:05 [http-nio-8080-exec-3] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:21:06 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:21:06 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:21:06 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:06 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:06 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:21:06 [http-nio-8080-exec-8] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:21:06 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:21:06 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /tasklist
2025-06-15 00:21:06 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:06 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:06 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /tasklist
2025-06-15 00:21:06 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:21:06 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:21:06 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:21:07 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:21:07 [http-nio-8080-exec-9] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:21:07 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:21:07 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:21:07 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:21:07 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:21:07 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:21:07 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:21:07 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:21:07 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:21:08 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:21:08 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:21:08 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:21:08 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:21:08 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:21:08 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:21:08 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:21:08 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:21:08 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:21:11 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:21:11 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:11 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:11 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:21:11 [http-nio-8080-exec-10] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:21:11 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:21:11 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:21:11 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:11 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:11 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:21:11 [http-nio-8080-exec-4] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:21:12 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:21:12 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /statistics
2025-06-15 00:21:12 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:12 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:12 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /statistics
2025-06-15 00:21:12 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:21:23 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:21:23 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:23 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:23 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:21:23 [http-nio-8080-exec-5] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:21:23 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:21:23 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:21:23 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:23 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:23 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:21:23 [http-nio-8080-exec-6] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:21:23 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:21:23 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /exchange
2025-06-15 00:21:23 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:23 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:23 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /exchange
2025-06-15 00:21:23 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:21:41 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:21:41 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:41 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:41 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:21:41 [http-nio-8080-exec-8] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:21:41 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:21:41 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:21:41 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:42 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:42 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:21:42 [http-nio-8080-exec-3] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:21:42 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:21:42 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system/user
2025-06-15 00:21:42 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:42 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:21:42 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system/user
2025-06-15 00:21:42 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:22:06 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:22:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:22:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:22:06 [http-nio-8080-exec-1] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:22:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:22:06 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:22:06 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:22:06 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:22:06 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:22:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:22:06 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:22:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:22:06 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:22:06 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:22:06 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:22:07 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:22:07 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:22:07 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:22:07 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:22:07 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:22:07 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:22:23 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:22:23 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:22:23 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:22:23 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:22:24 [http-nio-8080-exec-5] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:22:24 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:22:41 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:22:41 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:22:41 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:22:41 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:22:41 [http-nio-8080-exec-6] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:22:42 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:23:06 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:23:06 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:23:07 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:23:07 [http-nio-8080-exec-2] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:23:07 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:23:07 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:23:07 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:23:07 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:23:08 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:23:08 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:23:08 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:23:08 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:23:08 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:23:08 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:23:08 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:23:08 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:23:08 [http-nio-8080-exec-3] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:23:08 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:23:08 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:23:08 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:23:08 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:24:06 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:24:06 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:24:07 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:24:07 [http-nio-8080-exec-9] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:24:07 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:24:07 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:24:07 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:24:07 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:24:07 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:24:07 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:24:07 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:24:07 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:24:07 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:24:08 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:24:13 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:24:13 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:24:13 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:24:13 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:24:13 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:24:13 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:24:13 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:24:48 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:24:49 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:24:49 [http-nio-8080-exec-4] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:24:49 [http-nio-8080-exec-4] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:24:49 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:24:49 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:24:49 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:24:49 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:24:49 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:24:49 [http-nio-8080-exec-5] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:24:49 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:24:49 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:24:49 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:24:50 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:24:50 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:24:50 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:24:50 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:24:50 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:24:50 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:24:50 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:24:50 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:24:50 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:24:51 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:24:51 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:24:51 [http-nio-8080-exec-6] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:24:51 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:24:51 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:24:51 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:24:51 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:24:51 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:24:51 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:24:52 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:24:52 [http-nio-8080-exec-2] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:24:52 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:24:52 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:24:53 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:24:53 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:24:53 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:24:53 [http-nio-8080-exec-8] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:24:53 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:24:53 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:24:53 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:24:53 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:24:53 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:24:54 [http-nio-8080-exec-3] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:24:54 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system/menu
2025-06-15 00:24:54 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:24:54 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:24:54 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system/role
2025-06-15 00:24:54 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:24:54 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:24:54 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system/menu
2025-06-15 00:24:54 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:24:54 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:24:54 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system/role
2025-06-15 00:24:55 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:24:57 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:24:57 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:24:57 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:24:57 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:24:57 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:24:58 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:24:58 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:24:58 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:24:58 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:24:58 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:24:58 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:24:58 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:24:58 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:24:58 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:25:23 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:25:23 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:25:24 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:25:24 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:25:24 [http-nio-8080-exec-4] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:25:24 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:26:06 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:26:06 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:26:07 [http-nio-8080-exec-6] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:26:07 [http-nio-8080-exec-6] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:26:07 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:26:07 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:26:07 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:26:07 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:26:07 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:26:07 [http-nio-8080-exec-2] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:26:07 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:26:08 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:26:08 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:26:08 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:26:08 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:26:08 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:26:08 [http-nio-8080-exec-8] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:26:08 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:26:08 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:26:08 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:26:08 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:27:06 [http-nio-8080-exec-7] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:27:06 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:27:07 [http-nio-8080-exec-7] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:27:07 [http-nio-8080-exec-7] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:27:07 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:27:07 [http-nio-8080-exec-7] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:27:07 [http-nio-8080-exec-7] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:27:07 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:27:07 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:27:07 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:27:07 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:27:07 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:27:08 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:27:08 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:27:08 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:27:08 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:27:08 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:27:08 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:27:08 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:27:08 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:27:08 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:27:44 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:27:44 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:27:44 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:27:44 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:27:44 [http-nio-8080-exec-10] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:27:44 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:27:45 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /dashboard
2025-06-15 00:27:45 [http-nio-8080-exec-4] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:27:45 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:27:45 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /dashboard
2025-06-15 00:27:45 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:27:45 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /js/session-manager.js
2025-06-15 00:27:45 [http-nio-8080-exec-5] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:27:45 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:27:45 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /js/session-manager.js
2025-06-15 00:27:45 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:27:45 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /home
2025-06-15 00:27:45 [http-nio-8080-exec-6] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:27:45 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:27:45 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /home
2025-06-15 00:27:45 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:27:47 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:27:47 [http-nio-8080-exec-2] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:27:47 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:27:47 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:27:47 [http-nio-8080-exec-2] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:27:47 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:27:47 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:27:47 [http-nio-8080-exec-8] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:27:47 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:27:47 [http-nio-8080-exec-8] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /api/session/check
2025-06-15 00:27:47 [http-nio-8080-exec-8] DEBUG com.cf.financing.controller.SessionController - Session检查通过，用户: admin
2025-06-15 00:27:47 [http-nio-8080-exec-8] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:27:47 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /system/user
2025-06-15 00:27:47 [http-nio-8080-exec-3] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Retrieved SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:27:47 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to SecurityContextImpl [Authentication=UsernamePasswordAuthenticationToken [Principal=org.springframework.security.core.userdetails.User [Username=admin, Password=[PROTECTED], Enabled=true, AccountNonExpired=true, credentialsNonExpired=true, AccountNonLocked=true, Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]], Credentials=[PROTECTED], Authenticated=true, Details=WebAuthenticationDetails [RemoteIpAddress=0:0:0:0:0:0:0:1, SessionId=null], Granted Authorities=[ROLE_ADMIN, ROLE_USER, system:menu:add, system:menu:edit, system:menu:export, system:menu:list, system:menu:query, system:menu:remove, system:role:add, system:role:auth, system:role:edit, system:role:export, system:role:list, system:role:query, system:role:remove, system:user:add, system:user:auth, system:user:edit, system:user:list, system:user:query, system:user:remove, system:user:resetPwd]]]
2025-06-15 00:27:47 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /system/user
2025-06-15 00:27:47 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:28:11 [http-nio-8080-exec-9] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /api/session/check
2025-06-15 00:28:11 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:28:11 [http-nio-8080-exec-9] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:28:11 [http-nio-8080-exec-9] DEBUG o.s.security.web.DefaultRedirectStrategy - Redirecting to http://localhost:8080/login
2025-06-15 00:28:11 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:28:11 [http-nio-8080-exec-9] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store empty SecurityContext
2025-06-15 00:28:11 [http-nio-8080-exec-9] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:28:13 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /login
2025-06-15 00:28:13 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:28:13 [http-nio-8080-exec-1] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:28:13 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /login
2025-06-15 00:28:13 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:28:13 [http-nio-8080-exec-1] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:28:13 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:28:29 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Securing HEAD /dashboard
2025-06-15 00:28:29 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-06-15 00:28:29 [http-nio-8080-exec-10] DEBUG o.s.s.w.a.AnonymousAuthenticationFilter - Set SecurityContextHolder to anonymous SecurityContext
2025-06-15 00:28:29 [http-nio-8080-exec-10] DEBUG org.springframework.security.web.FilterChainProxy - Secured HEAD /dashboard
2025-06-15 00:28:29 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:28:29 [http-nio-8080-exec-10] DEBUG o.s.s.w.c.HttpSessionSecurityContextRepository - Did not store anonymous SecurityContext
2025-06-15 00:28:29 [http-nio-8080-exec-10] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-06-15 00:28:34 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-15 00:28:34 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
