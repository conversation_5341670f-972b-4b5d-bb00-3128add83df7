package com.cf.financing.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cf.financing.entity.ExchangeOrder;
import com.cf.financing.mapper.ExchangeOrderMapper;
import com.cf.financing.service.IExchangeOrderService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 换单管理服务实现类
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Service
public class ExchangeOrderServiceImpl extends ServiceImpl<ExchangeOrderMapper, ExchangeOrder> implements IExchangeOrderService {

    @Override
    public IPage<ExchangeOrder> getExchangeOrderPage(Page<ExchangeOrder> page,
                                                   String exchangeNo,
                                                   Long customerId,
                                                   Long caseId,
                                                   Integer exchangeType,
                                                   Integer status,
                                                   LocalDateTime startTime,
                                                   LocalDateTime endTime) {
        return baseMapper.selectExchangeOrderPage(page, exchangeNo, customerId, caseId,
                exchangeType, status, startTime, endTime);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createExchangeOrder(ExchangeOrder exchangeOrder) {
        try {
            // 生成换单编号
            String exchangeNo = generateExchangeNo();
            exchangeOrder.setExchangeNo(exchangeNo);
            
            // 设置默认状态为待审核
            exchangeOrder.setStatus(0);
            
            // 设置创建时间
            exchangeOrder.setCreateTime(LocalDateTime.now());
            exchangeOrder.setUpdateTime(LocalDateTime.now());
            
            return save(exchangeOrder);
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateExchangeOrder(ExchangeOrder exchangeOrder) {
        try {
            exchangeOrder.setUpdateTime(LocalDateTime.now());
            return updateById(exchangeOrder);
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean reviewExchangeOrder(Long id, Integer status, String reviewRemark, Long reviewerId) {
        try {
            ExchangeOrder exchangeOrder = getById(id);
            if (exchangeOrder == null) {
                return false;
            }
            
            exchangeOrder.setStatus(status);
            exchangeOrder.setReviewRemark(reviewRemark);
            exchangeOrder.setReviewerId(reviewerId);
            exchangeOrder.setReviewTime(LocalDateTime.now());
            exchangeOrder.setUpdateTime(LocalDateTime.now());
            
            return updateById(exchangeOrder);
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean executeExchangeOrder(Long id, Long executorId) {
        try {
            ExchangeOrder exchangeOrder = getById(id);
            if (exchangeOrder == null || !exchangeOrder.getStatus().equals(1)) {
                return false;
            }
            
            exchangeOrder.setStatus(2); // 已执行
            exchangeOrder.setExecutorId(executorId);
            exchangeOrder.setExecuteTime(LocalDateTime.now());
            exchangeOrder.setUpdateTime(LocalDateTime.now());
            
            return updateById(exchangeOrder);
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelExchangeOrder(Long id, String cancelReason, Long operatorId) {
        try {
            ExchangeOrder exchangeOrder = getById(id);
            if (exchangeOrder == null) {
                return false;
            }
            
            exchangeOrder.setStatus(3); // 已取消
            exchangeOrder.setReviewRemark(cancelReason);
            exchangeOrder.setReviewerId(operatorId);
            exchangeOrder.setReviewTime(LocalDateTime.now());
            exchangeOrder.setUpdateTime(LocalDateTime.now());
            
            return updateById(exchangeOrder);
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public List<ExchangeOrder> getExchangeOrdersByCustomer(Long customerId, Integer status) {
        return baseMapper.selectExchangeOrdersByCustomer(customerId, status);
    }

    @Override
    public List<ExchangeOrder> getExchangeOrdersByCaseId(Long caseId, Integer status) {
        return baseMapper.selectExchangeOrdersByCaseId(caseId, status);
    }

    @Override
    public List<ExchangeOrder> getPendingExchangeOrders(Long reviewerId) {
        return baseMapper.selectPendingExchangeOrders(reviewerId);
    }

    @Override
    public Map<String, Object> getExchangeTypeStatistics(LocalDate startDate,
                                                        LocalDate endDate,
                                                        Long departmentId) {
        return baseMapper.selectExchangeTypeStatistics(startDate, endDate, departmentId);
    }

    @Override
    public Map<String, Object> getExchangeStatusStatistics(LocalDate startDate,
                                                          LocalDate endDate,
                                                          Long departmentId) {
        return baseMapper.selectExchangeStatusStatistics(startDate, endDate, departmentId);
    }

    @Override
    public List<Map<String, Object>> getExchangeTrendData(LocalDate startDate,
                                                         LocalDate endDate,
                                                         Integer exchangeType,
                                                         Long departmentId) {
        return baseMapper.selectExchangeTrendData(startDate, endDate, exchangeType, departmentId);
    }

    @Override
    public List<Map<String, Object>> getExchangeUserStatistics(LocalDate startDate,
                                                              LocalDate endDate,
                                                              String orderBy,
                                                              Integer limit) {
        return baseMapper.selectExchangeUserStatistics(startDate, endDate, orderBy, limit);
    }

    @Override
    public Map<String, Object> getExchangeAmountStatistics(LocalDate startDate,
                                                          LocalDate endDate,
                                                          Long departmentId) {
        return baseMapper.selectExchangeAmountStatistics(startDate, endDate, departmentId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchReviewExchangeOrders(List<Long> ids,
                                            Integer status,
                                            String reviewRemark,
                                            Long reviewerId) {
        try {
            int count = baseMapper.batchUpdateExchangeStatus(ids, status, reviewRemark,
                    reviewerId, LocalDateTime.now());
            return count > 0;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public boolean canExchangeCase(Long caseId) {
        return baseMapper.checkPendingExchangeByCaseId(caseId) == 0;
    }

    @Override
    public String generateExchangeNo() {
        String prefix = "EX";
        String datePart = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        
        // 获取当天的序号
        String pattern = prefix + datePart + "%";
        int count = baseMapper.selectCount(null);
        
        String sequence = String.format("%04d", count + 1);
        return prefix + datePart + sequence;
    }

    @Override
    public String exportExchangeRecords(LocalDate startDate,
                                      LocalDate endDate,
                                      Integer exchangeType,
                                      Integer status,
                                      Long departmentId) {
        // 这里应该实现Excel导出逻辑
        // 为了简化，返回一个模拟的文件路径
        String fileName = "exchange_records_" + startDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")) +
                "_" + endDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")) + ".xlsx";
        return "/exports/" + fileName;
    }

    @Override
    public Map<String, Object> getExchangeOverview(Long userId, Long departmentId) {
        Map<String, Object> overview = new HashMap<>();
        
        LocalDate today = LocalDate.now();
        LocalDate monthStart = today.withDayOfMonth(1);
        
        // 今日统计
        Map<String, Object> todayStats = new HashMap<>();
        todayStats.put("pending", getPendingExchangeOrders(userId).size());
        overview.put("today", todayStats);
        
        // 本月统计
        Map<String, Object> monthStats = getExchangeStatusStatistics(monthStart, today, departmentId);
        overview.put("month", monthStats);
        
        // 类型统计
        Map<String, Object> typeStats = getExchangeTypeStatistics(monthStart, today, departmentId);
        overview.put("types", typeStats);
        
        // 金额统计
        Map<String, Object> amountStats = getExchangeAmountStatistics(monthStart, today, departmentId);
        overview.put("amounts", amountStats);
        
        // 趋势数据
        List<Map<String, Object>> trendData = getExchangeTrendData(monthStart, today, null, departmentId);
        overview.put("trend", trendData);
        
        return overview;
    }
}