package com.cf.financing.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cf.financing.entity.SysRoleMenu;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色菜单关联Mapper接口
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Mapper
public interface SysRoleMenuMapper extends BaseMapper<SysRoleMenu> {

    /**
     * 根据角色ID删除角色菜单关联
     *
     * @param roleId 角色ID
     * @return 影响行数
     */
    int deleteByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据菜单ID删除角色菜单关联
     *
     * @param menuId 菜单ID
     * @return 影响行数
     */
    int deleteByMenuId(@Param("menuId") Long menuId);

    /**
     * 批量插入角色菜单关联
     *
     * @param roleId 角色ID
     * @param menuIds 菜单ID列表
     * @param createBy 创建人
     * @return 影响行数
     */
    int insertRoleMenuBatch(@Param("roleId") Long roleId,
                           @Param("menuIds") List<Long> menuIds,
                           @Param("createBy") Long createBy);

    /**
     * 根据角色ID查询菜单ID列表
     *
     * @param roleId 角色ID
     * @return 菜单ID列表
     */
    List<Long> selectMenuIdsByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据菜单ID查询角色ID列表
     *
     * @param menuId 菜单ID
     * @return 角色ID列表
     */
    List<Long> selectRoleIdsByMenuId(@Param("menuId") Long menuId);

    /**
     * 检查角色是否拥有指定菜单权限
     *
     * @param roleId 角色ID
     * @param menuId 菜单ID
     * @return 数量
     */
    int checkRoleHasMenu(@Param("roleId") Long roleId, @Param("menuId") Long menuId);

    /**
     * 根据角色ID列表删除角色菜单关联
     *
     * @param roleIds 角色ID列表
     * @return 影响行数
     */
    int deleteByRoleIds(@Param("roleIds") List<Long> roleIds);

    /**
     * 根据菜单ID列表删除角色菜单关联
     *
     * @param menuIds 菜单ID列表
     * @return 影响行数
     */
    int deleteByMenuIds(@Param("menuIds") List<Long> menuIds);

    /**
     * 查询角色菜单关联详情
     *
     * @param roleId 角色ID
     * @return 角色菜单关联列表（包含菜单信息）
     */
    List<SysRoleMenu> selectRoleMenuDetails(@Param("roleId") Long roleId);

    /**
     * 查询菜单角色关联详情
     *
     * @param menuId 菜单ID
     * @return 角色菜单关联列表（包含角色信息）
     */
    List<SysRoleMenu> selectMenuRoleDetails(@Param("menuId") Long menuId);

    /**
     * 批量更新角色菜单关联（先删除后插入）
     *
     * @param roleId 角色ID
     * @param menuIds 菜单ID列表
     * @param createBy 创建人
     * @return 影响行数
     */
    int updateRoleMenus(@Param("roleId") Long roleId,
                       @Param("menuIds") List<Long> menuIds,
                       @Param("createBy") Long createBy);

    /**
     * 根据用户ID查询菜单ID列表（通过用户角色关联）
     *
     * @param userId 用户ID
     * @return 菜单ID列表
     */
    List<Long> selectMenuIdsByUserId(@Param("userId") Long userId);
}