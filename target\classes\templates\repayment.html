<!DOCTYPE html>
<html lang="zh-CN" style="height: 100%;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>还款管理</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            height: 100vh;
            overflow: hidden;
        }

        .container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            padding: 10px;
            gap: 10px;
        }

        /* 筛选框样式 */
        .filter-box {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            height: 35vh;
            overflow-y: auto;
        }

        .filter-title {
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-label {
            min-width: 80px;
            font-size: 14px;
            color: #555;
            font-weight: 500;
        }

        .filter-control {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .filter-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .filter-btn-group {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        .filter-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: all 0.3s;
        }

        .filter-btn.search {
            background: #3498db;
            color: white;
        }

        .filter-btn.search:hover {
            background: #2980b9;
        }

        .filter-btn.reset {
            background: #95a5a6;
            color: white;
        }

        .filter-btn.reset:hover {
            background: #7f8c8d;
        }

        /* 功能框样式 */
        .function-box {
            background: white;
            border-radius: 8px;
            padding: 15px 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            height: 5vh;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .stats-info {
            display: flex;
            gap: 30px;
            align-items: center;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #555;
        }

        .stat-item i {
            color: #3498db;
        }

        .stat-item strong {
            color: #2c3e50;
            font-weight: 600;
        }

        .function-buttons {
            display: flex;
            gap: 10px;
        }

        .function-btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: all 0.3s;
        }

        .function-btn:hover {
            background: #f8f9fa;
            border-color: #3498db;
            color: #3498db;
        }

        .function-btn.primary {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .function-btn.primary:hover {
            background: #2980b9;
        }

        /* 数据展示框样式 */
        .data-box {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            height: 60vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .data-header {
            padding: 15px 20px;
            border-bottom: 1px solid #e1e4e8;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8f9fa;
        }

        .data-title {
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .data-actions {
            display: flex;
            gap: 10px;
        }

        .data-action-btn {
            padding: 6px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
            transition: all 0.3s;
        }

        .data-action-btn:hover {
            background: #f8f9fa;
            border-color: #3498db;
            color: #3498db;
        }

        .data-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .table-wrapper {
            flex: 1;
            overflow: auto;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
        }

        .data-table th,
        .data-table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #e1e4e8;
            white-space: nowrap;
        }

        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .data-table tbody tr:hover {
            background: #f8f9fa;
        }

        .amount {
            text-align: right;
            font-family: 'Courier New', monospace;
            font-weight: 500;
        }

        .status-tag {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }

        .audit-approved {
            background: #d1ecf1;
            color: #0c5460;
        }

        .audit-rejected {
            background: #f5c6cb;
            color: #721c24;
        }

        .audit-pending {
            background: #ffeaa7;
            color: #6c5ce7;
        }

        /* 分页样式 */
        .pagination {
            padding: 15px 20px;
            background: #f8f9fa;
            border-top: 1px solid #e1e4e8;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
        }

        .pagination-left, .pagination-right {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .pagination-select, .pagination-input {
            padding: 6px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .pagination-input {
            width: 60px;
            text-align: center;
        }

        .pagination-btn {
            padding: 6px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }

        .pagination-btn:hover:not(:disabled) {
            background: #f8f9fa;
            border-color: #3498db;
            color: #3498db;
        }

        .pagination-btn:disabled {
            background: #f8f9fa;
            color: #adb5bd;
            cursor: not-allowed;
        }

        .empty-value {
            color: #999;
            font-style: italic;
        }

        .client-id-link {
            color: #3498db;
            text-decoration: none;
            cursor: pointer;
        }

        .client-id-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 上框体：筛选框 -->
        <div class="filter-box">
            <div class="filter-title">
                <i class="fas fa-filter"></i>
                <span>筛选条件</span>
            </div>

            <div class="filter-grid">
                <div class="filter-group">
                    <label class="filter-label">客户姓名:</label>
                    <input type="text" class="filter-control" id="clientName" placeholder="请输入客户姓名">
                </div>

                <div class="filter-group">
                    <label class="filter-label">客户索引号:</label>
                    <input type="text" class="filter-control" id="clientId" placeholder="请输入客户索引号">
                </div>

                <div class="filter-group">
                    <label class="filter-label">还款编号:</label>
                    <input type="text" class="filter-control" id="repaymentNo" placeholder="请输入还款编号">
                </div>

                <div class="filter-group">
                    <label class="filter-label">还款状态:</label>
                    <select class="filter-control" id="repaymentStatus">
                        <option value="">全部状态</option>
                        <option value="SUCCESS">成功</option>
                        <option value="FAILED">失败</option>
                        <option value="PENDING">处理中</option>
                        <option value="CANCELLED">已取消</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-label">还款方式:</label>
                    <select class="filter-control" id="repaymentType">
                        <option value="">全部方式</option>
                        <option value="BANK">银行转账</option>
                        <option value="ALIPAY">支付宝</option>
                        <option value="WECHAT">微信</option>
                        <option value="CASH">现金</option>
                        <option value="POS">POS机</option>
                        <option value="OTHER">其他</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-label">审核状态:</label>
                    <select class="filter-control" id="auditStatus">
                        <option value="">全部状态</option>
                        <option value="PENDING">待审核</option>
                        <option value="APPROVED">已审核</option>
                        <option value="REJECTED">已拒绝</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-label">还款类型:</label>
                    <select class="filter-control" id="paymentType">
                        <option value="">全部类型</option>
                        <option value="ACTIVE">主动还款</option>
                        <option value="PASSIVE">被动还款</option>
                        <option value="PARTIAL">部分还款</option>
                        <option value="FULL">全额还款</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-label">还款日期:</label>
                    <input type="text" class="filter-control" id="dateRange" placeholder="选择日期范围">
                </div>

                <div class="filter-group">
                    <label class="filter-label">金额范围:</label>
                    <input type="number" class="filter-control" id="minAmount" placeholder="最小金额" style="width: 48%; margin-right: 4%;">
                    <input type="number" class="filter-control" id="maxAmount" placeholder="最大金额" style="width: 48%;">
                </div>
            </div>

            <div class="filter-btn-group">
                <button class="filter-btn search" id="searchBtn">
                    <i class="fas fa-search"></i>
                    <span>查询</span>
                </button>
                <button class="filter-btn reset" id="resetBtn">
                    <i class="fas fa-undo"></i>
                    <span>重置</span>
                </button>
            </div>
        </div>

        <!-- 中框体：功能框 -->
        <div class="function-box">
            <div class="stats-info">
                <div class="stat-item">
                    <i class="fas fa-list"></i>
                    <span>总记录数: <strong id="total-records">0</strong></span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-money-bill-wave"></i>
                    <span>总金额: <strong id="total-amount">¥0</strong></span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-check-circle"></i>
                    <span>勾选记录: <strong id="selected-records">0</strong></span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-calculator"></i>
                    <span>勾选金额: <strong id="selected-amount">¥0</strong></span>
                </div>
            </div>

            <div class="function-buttons">
                <button class="function-btn" id="auditBtn">
                    <i class="fas fa-check"></i>
                    <span>批量审核</span>
                </button>
                <button class="function-btn" id="exportBtn">
                    <i class="fas fa-download"></i>
                    <span>导出</span>
                </button>
                <button class="function-btn" id="addBtn">
                    <i class="fas fa-plus"></i>
                    <span>新增</span>
                </button>
                <button class="function-btn primary" id="refreshBtn">
                    <i class="fas fa-sync-alt"></i>
                    <span>刷新</span>
                </button>
            </div>
        </div>

        <!-- 下框体：数据展示框 -->
        <div class="data-box">
            <div class="data-header">
                <div class="data-title">
                    <i class="fas fa-table"></i>
                    <span>还款记录</span>
                </div>
                <div class="data-actions">
                    <button class="data-action-btn" id="columnsBtn">
                        <i class="fas fa-columns"></i>
                        <span>列设置</span>
                    </button>
                    <button class="data-action-btn" id="fullscreenBtn">
                        <i class="fas fa-expand"></i>
                        <span>全屏</span>
                    </button>
                </div>
            </div>

            <div class="data-container">
                <div class="table-wrapper">
                    <table class="data-table" id="repaymentTable">
                        <thead>
                            <tr>
                                <th><input type="checkbox" id="select-all"></th>
                                <th>客户姓名</th>
                                <th>客户索引号</th>
                                <th>还款编号</th>
                                <th>还款金额</th>
                                <th>本金还款</th>
                                <th>利息还款</th>
                                <th>费用还款</th>
                                <th>还款日期</th>
                                <th>还款方式</th>
                                <th>还款渠道</th>
                                <th>还款状态</th>
                                <th>还款类型</th>
                                <th>审核状态</th>
                                <th>交易流水号</th>
                                <th>银行流水号</th>
                                <th>减免金额</th>
                                <th>实收金额</th>
                                <th>手续费</th>
                                <th>备注</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="table-body">
                            <!-- 数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>

                <div class="pagination">
                    <div class="pagination-left">
                        <span>每页显示</span>
                        <select class="pagination-select" id="pageSizeSelect">
                            <option value="10">10</option>
                            <option value="20" selected>20</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                        <span>条记录</span>
                    </div>

                    <div class="pagination-right">
                        <span>共 <span id="total-count">0</span> 条记录</span>

                        <button class="pagination-btn" id="firstPageBtn">
                            <i class="fas fa-angle-double-left"></i>
                        </button>

                        <button class="pagination-btn" id="prevPageBtn">
                            <i class="fas fa-angle-left"></i>
                        </button>

                        <span>第</span>
                        <input type="number" class="pagination-input" id="pageNumInput" value="1" min="1">
                        <span>页</span>

                        <button class="pagination-btn" id="nextPageBtn">
                            <i class="fas fa-angle-right"></i>
                        </button>

                        <button class="pagination-btn" id="lastPageBtn">
                            <i class="fas fa-angle-double-right"></i>
                        </button>

                        <button class="pagination-btn" id="gotoPageBtn">跳转</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script th:src="@{/js/repayment.js}"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
</body>
</html>