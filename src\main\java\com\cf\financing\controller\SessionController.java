package com.cf.financing.controller;

import com.cf.financing.common.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.Map;

/**
 * Session管理控制器
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Api(tags = "Session管理")
@RestController
@RequestMapping("/api/session")
public class SessionController {

    private static final Logger log = LoggerFactory.getLogger(SessionController.class);

    /**
     * 检查session是否有效
     */
    @ApiOperation("检查session状态")
    @GetMapping("/check")
    public Result<Map<String, Object>> checkSession(HttpServletRequest request) {
        try {
            // 检查Spring Security认证状态
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            
            if (authentication == null || !authentication.isAuthenticated() || 
                "anonymousUser".equals(authentication.getPrincipal())) {
                log.warn("用户未认证或session已过期");
                return Result.error(401, "Session已过期，请重新登录");
            }
            
            // 检查HTTP Session
            HttpSession session = request.getSession(false);
            if (session == null) {
                log.warn("HTTP Session不存在");
                return Result.error(401, "Session已过期，请重新登录");
            }
            
            // 检查session是否过期
            try {
                session.getLastAccessedTime(); // 这会触发session验证
            } catch (IllegalStateException e) {
                log.warn("Session已失效: {}", e.getMessage());
                return Result.error(401, "Session已过期，请重新登录");
            }
            
            // 返回session信息
            Map<String, Object> sessionInfo = new HashMap<>();
            sessionInfo.put("sessionId", session.getId());
            sessionInfo.put("username", authentication.getName());
            sessionInfo.put("lastAccessTime", session.getLastAccessedTime());
            sessionInfo.put("maxInactiveInterval", session.getMaxInactiveInterval());
            sessionInfo.put("isAuthenticated", true);
            
            log.debug("Session检查通过，用户: {}", authentication.getName());
            return Result.success(sessionInfo);
            
        } catch (Exception e) {
            log.error("Session检查异常: {}", e.getMessage(), e);
            return Result.error(500, "Session检查失败");
        }
    }

    /**
     * 获取当前用户信息
     */
    @ApiOperation("获取当前用户信息")
    @GetMapping("/user")
    public Result<Map<String, Object>> getCurrentUser() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            
            if (authentication == null || !authentication.isAuthenticated() || 
                "anonymousUser".equals(authentication.getPrincipal())) {
                return Result.error(401, "用户未登录");
            }
            
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("username", authentication.getName());
            userInfo.put("authorities", authentication.getAuthorities());
            userInfo.put("isAuthenticated", authentication.isAuthenticated());
            
            return Result.success(userInfo);
            
        } catch (Exception e) {
            log.error("获取用户信息失败: {}", e.getMessage(), e);
            return Result.error(500, "获取用户信息失败");
        }
    }

    /**
     * 延长session有效期
     */
    @ApiOperation("延长session有效期")
    @GetMapping("/extend")
    public Result<String> extendSession(HttpServletRequest request) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            
            if (authentication == null || !authentication.isAuthenticated() || 
                "anonymousUser".equals(authentication.getPrincipal())) {
                return Result.error(401, "用户未登录");
            }
            
            HttpSession session = request.getSession(false);
            if (session != null) {
                // 更新session的最后访问时间
                session.setMaxInactiveInterval(session.getMaxInactiveInterval());
                log.debug("Session已延长，用户: {}", authentication.getName());
                return Result.success("Session已延长");
            } else {
                return Result.error(401, "Session不存在");
            }
            
        } catch (Exception e) {
            log.error("延长session失败: {}", e.getMessage(), e);
            return Result.error(500, "延长session失败");
        }
    }

    /**
     * 获取session统计信息
     */
    @ApiOperation("获取session统计信息")
    @GetMapping("/stats")
    public Result<Map<String, Object>> getSessionStats(HttpServletRequest request) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            
            if (authentication == null || !authentication.isAuthenticated() || 
                "anonymousUser".equals(authentication.getPrincipal())) {
                return Result.error(401, "用户未登录");
            }
            
            HttpSession session = request.getSession(false);
            if (session == null) {
                return Result.error(401, "Session不存在");
            }
            
            Map<String, Object> stats = new HashMap<>();
            stats.put("sessionId", session.getId());
            stats.put("creationTime", session.getCreationTime());
            stats.put("lastAccessedTime", session.getLastAccessedTime());
            stats.put("maxInactiveInterval", session.getMaxInactiveInterval());
            stats.put("isNew", session.isNew());
            
            // 计算剩余时间
            long currentTime = System.currentTimeMillis();
            long lastAccessTime = session.getLastAccessedTime();
            long maxInactiveInterval = session.getMaxInactiveInterval() * 1000L; // 转换为毫秒
            long remainingTime = maxInactiveInterval - (currentTime - lastAccessTime);
            stats.put("remainingTime", Math.max(0, remainingTime));
            
            return Result.success(stats);
            
        } catch (Exception e) {
            log.error("获取session统计信息失败: {}", e.getMessage(), e);
            return Result.error(500, "获取session统计信息失败");
        }
    }
}
