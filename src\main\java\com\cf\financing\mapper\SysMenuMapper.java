package com.cf.financing.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cf.financing.entity.SysMenu;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统菜单Mapper接口
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Mapper
public interface SysMenuMapper extends BaseMapper<SysMenu> {

    /**
     * 分页查询菜单列表
     *
     * @param page 分页参数
     * @param menuName 菜单名称
     * @param menuCode 菜单编码
     * @param status 状态
     * @param menuType 菜单类型
     * @return 菜单分页列表
     */
    IPage<SysMenu> selectMenuPage(Page<SysMenu> page,
                                  @Param("menuName") String menuName,
                                  @Param("menuCode") String menuCode,
                                  @Param("status") Integer status,
                                  @Param("menuType") Integer menuType);

    /**
     * 查询所有菜单树形结构
     *
     * @param status 状态
     * @return 菜单树列表
     */
    List<SysMenu> selectMenuTree(@Param("status") Integer status);

    /**
     * 根据用户ID查询菜单权限
     *
     * @param userId 用户ID
     * @return 菜单列表
     */
    List<SysMenu> selectMenusByUserId(@Param("userId") Long userId);

    /**
     * 根据角色ID查询菜单权限
     *
     * @param roleId 角色ID
     * @return 菜单列表
     */
    List<SysMenu> selectMenusByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据角色ID列表查询菜单权限
     *
     * @param roleIds 角色ID列表
     * @return 菜单列表
     */
    List<SysMenu> selectMenusByRoleIds(@Param("roleIds") List<Long> roleIds);

    /**
     * 查询用户菜单权限标识
     *
     * @param userId 用户ID
     * @return 权限标识列表
     */
    List<String> selectPermissionsByUserId(@Param("userId") Long userId);

    /**
     * 根据父菜单ID查询子菜单
     *
     * @param parentId 父菜单ID
     * @return 子菜单列表
     */
    List<SysMenu> selectMenusByParentId(@Param("parentId") Long parentId);

    /**
     * 检查菜单编码是否存在
     *
     * @param menuCode 菜单编码
     * @param excludeId 排除的菜单ID
     * @return 数量
     */
    int checkMenuCodeExists(@Param("menuCode") String menuCode, @Param("excludeId") Long excludeId);

    /**
     * 检查菜单名称是否存在（同级别下）
     *
     * @param menuName 菜单名称
     * @param parentId 父菜单ID
     * @param excludeId 排除的菜单ID
     * @return 数量
     */
    int checkMenuNameExists(@Param("menuName") String menuName,
                           @Param("parentId") Long parentId,
                           @Param("excludeId") Long excludeId);

    /**
     * 检查是否存在子菜单
     *
     * @param menuId 菜单ID
     * @return 子菜单数量
     */
    int hasChildMenus(@Param("menuId") Long menuId);

    /**
     * 检查菜单是否被角色使用
     *
     * @param menuId 菜单ID
     * @return 使用该菜单的角色数量
     */
    int countRolesByMenuId(@Param("menuId") Long menuId);

    /**
     * 根据菜单ID列表查询菜单
     *
     * @param menuIds 菜单ID列表
     * @return 菜单列表
     */
    List<SysMenu> selectMenusByIds(@Param("menuIds") List<Long> menuIds);

    /**
     * 查询所有父菜单（目录和菜单类型）
     *
     * @return 父菜单列表
     */
    List<SysMenu> selectParentMenus();

    /**
     * 根据路径查询菜单
     *
     * @param path 路径
     * @return 菜单信息
     */
    SysMenu selectByPath(@Param("path") String path);

    /**
     * 查询用户可访问的菜单树（用于前端导航）
     *
     * @param userId 用户ID
     * @return 菜单树列表
     */
    List<SysMenu> selectUserMenuTree(@Param("userId") Long userId);
}