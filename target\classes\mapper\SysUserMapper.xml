<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cf.financing.mapper.SysUserMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.cf.financing.entity.SysUser">
        <id column="id" property="id" />
        <result column="username" property="username" />
        <result column="password" property="password" />
        <result column="real_name" property="realName" />
        <result column="phone" property="phone" />
        <result column="email" property="email" />
        <result column="status" property="status" />
        <result column="dept_id" property="deptId" />
        <result column="role_id" property="roleId" />
        <result column="last_login_time" property="lastLoginTime" />
        <result column="last_login_ip" property="lastLoginIp" />
        <result column="login_count" property="loginCount" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 扩展结果映射（包含部门和角色信息） -->
    <resultMap id="ExtendedResultMap" type="com.cf.financing.entity.SysUser" extends="BaseResultMap">
        <result column="dept_name" property="deptName" />
        <result column="role_name" property="roleName" />
        <collection property="roles" ofType="com.cf.financing.entity.SysRole">
            <id column="role_id" property="id" />
            <result column="role_code" property="roleCode" />
            <result column="role_name" property="roleName" />
            <result column="role_description" property="description" />
            <result column="role_status" property="status" />
        </collection>
    </resultMap>

    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        id, username, password, real_name, phone, email, status, dept_id, role_id,
        last_login_time, last_login_ip, login_count, create_time, update_time
    </sql>

    <!-- 分页查询用户信息 -->
    <select id="selectUserPage" resultMap="ExtendedResultMap">
        SELECT 
            u.id, u.username, u.real_name, u.phone, u.email, u.status, u.dept_id, u.role_id,
            u.last_login_time, u.last_login_ip, u.login_count, u.create_time, u.update_time,
            d.dept_name, r.role_name
        FROM sys_user u
        LEFT JOIN sys_dept d ON u.dept_id = d.id
        LEFT JOIN sys_role r ON u.role_id = r.id
        <where>
            <if test="username != null and username != ''">
                AND u.username LIKE CONCAT('%', #{username}, '%')
            </if>
            <if test="realName != null and realName != ''">
                AND u.real_name LIKE CONCAT('%', #{realName}, '%')
            </if>
            <if test="phone != null and phone != ''">
                AND u.phone LIKE CONCAT('%', #{phone}, '%')
            </if>
            <if test="email != null and email != ''">
                AND u.email LIKE CONCAT('%', #{email}, '%')
            </if>
            <if test="status != null and status != ''">
                AND u.status = #{status}
            </if>
            <if test="deptId != null">
                AND u.dept_id = #{deptId}
            </if>
            <if test="roleId != null">
                AND u.role_id = #{roleId}
            </if>
        </where>
        ORDER BY u.create_time DESC
    </select>

    <!-- 根据用户名查询用户详细信息 -->
    <select id="selectUserByUsername" resultMap="ExtendedResultMap">
        SELECT
            u.id, u.username, u.password, u.real_name, u.phone, u.email, u.status, u.dept_id,
            u.last_login_time, u.last_login_ip, u.create_time, u.update_time,
            d.dept_name,
            r.id as role_id, r.role_code, r.role_name, r.description as role_description, r.status as role_status
        FROM sys_user u
        LEFT JOIN sys_dept d ON u.dept_id = d.id
        LEFT JOIN sys_user_role ur ON u.id = ur.user_id
        LEFT JOIN sys_role r ON ur.role_id = r.id
        WHERE u.username = #{username}
    </select>

    <!-- 根据用户ID查询用户详细信息 -->
    <select id="selectUserById" resultMap="ExtendedResultMap">
        SELECT 
            u.id, u.username, u.real_name, u.phone, u.email, u.status, u.dept_id, u.role_id,
            u.last_login_time, u.last_login_ip, u.login_count, u.create_time, u.update_time,
            d.dept_name, r.role_name
        FROM sys_user u
        LEFT JOIN sys_dept d ON u.dept_id = d.id
        LEFT JOIN sys_role r ON u.role_id = r.id
        WHERE u.id = #{userId}
    </select>

    <!-- 查询部门下的所有用户 -->
    <select id="selectUsersByDeptId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_user
        WHERE dept_id = #{deptId} AND status = 'ACTIVE'
        ORDER BY real_name
    </select>

    <!-- 查询角色下的所有用户 -->
    <select id="selectUsersByRoleId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_user
        WHERE role_id = #{roleId} AND status = 'ACTIVE'
        ORDER BY real_name
    </select>

    <!-- 更新用户登录信息 -->
    <update id="updateLoginInfo">
        UPDATE sys_user 
        SET last_login_time = NOW(),
            last_login_ip = #{loginIp},
            login_count = login_count + 1,
            update_time = NOW()
        WHERE id = #{userId}
    </update>

    <!-- 重置用户密码 -->
    <update id="resetPassword">
        UPDATE sys_user 
        SET password = #{newPassword},
            update_time = NOW()
        WHERE id = #{userId}
    </update>

    <!-- 批量更新用户状态 -->
    <update id="batchUpdateStatus">
        UPDATE sys_user 
        SET status = #{status},
            update_time = NOW()
        WHERE id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </update>

    <!-- 获取用户统计信息 -->
    <select id="selectUserStatistics" resultType="map">
        SELECT 
            COUNT(*) as totalUsers,
            COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) as activeUsers,
            COUNT(CASE WHEN status = 'INACTIVE' THEN 1 END) as inactiveUsers,
            COUNT(CASE WHEN DATE(last_login_time) = CURDATE() THEN 1 END) as todayLoginUsers,
            COUNT(CASE WHEN last_login_time >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as weekActiveUsers
        FROM sys_user
        <where>
            <if test="deptId != null">
                AND dept_id = #{deptId}
            </if>
        </where>
    </select>

    <!-- 检查用户名是否存在 -->
    <select id="checkUsernameExists" resultType="int">
        SELECT COUNT(*) 
        FROM sys_user 
        WHERE username = #{username}
        <if test="excludeUserId != null">
            AND id != #{excludeUserId}
        </if>
    </select>

    <!-- 检查手机号是否存在 -->
    <select id="checkPhoneExists" resultType="int">
        SELECT COUNT(*) 
        FROM sys_user 
        WHERE phone = #{phone}
        <if test="excludeUserId != null">
            AND id != #{excludeUserId}
        </if>
    </select>

    <!-- 检查邮箱是否存在 -->
    <select id="checkEmailExists" resultType="int">
        SELECT COUNT(*) 
        FROM sys_user 
        WHERE email = #{email}
        <if test="excludeUserId != null">
            AND id != #{excludeUserId}
        </if>
    </select>

    <!-- 获取在线用户列表 -->
    <select id="selectOnlineUsers" resultMap="ExtendedResultMap">
        SELECT
            u.id, u.username, u.real_name, u.phone, u.email, u.status, u.dept_id, u.role_id,
            u.last_login_time, u.last_login_ip, u.login_count, u.create_time, u.update_time,
            d.dept_name, r.role_name
        FROM sys_user u
        LEFT JOIN sys_dept d ON u.dept_id = d.id
        LEFT JOIN sys_role r ON u.role_id = r.id
        WHERE u.status = 'ACTIVE'
        AND u.last_login_time >= DATE_SUB(NOW(), INTERVAL 30 MINUTE)
        ORDER BY u.last_login_time DESC
    </select>

    <!-- 根据用户ID查询角色列表 -->
    <select id="selectRolesByUserId" resultType="com.cf.financing.entity.SysRole">
        SELECT
            r.id, r.role_code, r.role_name, r.description, r.status, r.sort_order, r.data_scope,
            r.create_by, r.create_time, r.update_by, r.update_time, r.remark
        FROM sys_role r
        INNER JOIN sys_user_role ur ON r.id = ur.role_id
        WHERE ur.user_id = #{userId} AND r.status = 1
        ORDER BY r.sort_order
    </select>

</mapper>