<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cf.financing.mapper.SysUserRoleMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.cf.financing.entity.SysUserRole">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="role_id" property="roleId" />
        <result column="create_time" property="createTime" />
        <result column="create_by" property="createBy" />
    </resultMap>

    <!-- 扩展结果映射（包含用户和角色信息） -->
    <resultMap id="ExtendedResultMap" type="com.cf.financing.entity.SysUserRole" extends="BaseResultMap">
        <result column="username" property="username" />
        <result column="role_name" property="roleName" />
    </resultMap>

    <!-- 根据用户ID删除用户角色关联 -->
    <delete id="deleteByUserId">
        DELETE FROM sys_user_role WHERE user_id = #{userId}
    </delete>

    <!-- 根据角色ID删除用户角色关联 -->
    <delete id="deleteByRoleId">
        DELETE FROM sys_user_role WHERE role_id = #{roleId}
    </delete>

    <!-- 批量插入用户角色关联 -->
    <insert id="insertUserRoleBatch">
        INSERT INTO sys_user_role (user_id, role_id, create_by, create_time)
        VALUES
        <foreach collection="roleIds" item="roleId" separator=",">
            (#{userId}, #{roleId}, #{createBy}, NOW())
        </foreach>
    </insert>

    <!-- 根据用户ID查询角色ID列表 -->
    <select id="selectRoleIdsByUserId" resultType="java.lang.Long">
        SELECT role_id FROM sys_user_role WHERE user_id = #{userId}
    </select>

    <!-- 根据角色ID查询用户ID列表 -->
    <select id="selectUserIdsByRoleId" resultType="java.lang.Long">
        SELECT user_id FROM sys_user_role WHERE role_id = #{roleId}
    </select>

    <!-- 检查用户是否拥有指定角色 -->
    <select id="checkUserHasRole" resultType="int">
        SELECT COUNT(*) FROM sys_user_role 
        WHERE user_id = #{userId} AND role_id = #{roleId}
    </select>

    <!-- 根据用户ID列表删除用户角色关联 -->
    <delete id="deleteByUserIds">
        DELETE FROM sys_user_role 
        WHERE user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <!-- 根据角色ID列表删除用户角色关联 -->
    <delete id="deleteByRoleIds">
        DELETE FROM sys_user_role 
        WHERE role_id IN
        <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </delete>

    <!-- 查询用户角色关联详情 -->
    <select id="selectUserRoleDetails" resultMap="ExtendedResultMap">
        SELECT 
            ur.id, ur.user_id, ur.role_id, ur.create_time, ur.create_by,
            u.username, r.role_name
        FROM sys_user_role ur
        LEFT JOIN sys_user u ON ur.user_id = u.id
        LEFT JOIN sys_role r ON ur.role_id = r.id
        WHERE ur.user_id = #{userId}
        ORDER BY r.sort_order
    </select>

    <!-- 查询角色用户关联详情 -->
    <select id="selectRoleUserDetails" resultMap="ExtendedResultMap">
        SELECT 
            ur.id, ur.user_id, ur.role_id, ur.create_time, ur.create_by,
            u.username, r.role_name
        FROM sys_user_role ur
        LEFT JOIN sys_user u ON ur.user_id = u.id
        LEFT JOIN sys_role r ON ur.role_id = r.id
        WHERE ur.role_id = #{roleId}
        ORDER BY u.username
    </select>

    <!-- 批量更新用户角色关联（先删除后插入） -->
    <update id="updateUserRoles">
        <!-- 先删除原有关联 -->
        DELETE FROM sys_user_role WHERE user_id = #{userId};
        
        <!-- 如果有新角色，则插入 -->
        <if test="roleIds != null and roleIds.size() > 0">
            INSERT INTO sys_user_role (user_id, role_id, create_by, create_time)
            VALUES
            <foreach collection="roleIds" item="roleId" separator=",">
                (#{userId}, #{roleId}, #{createBy}, NOW())
            </foreach>
        </if>
    </update>

</mapper>
