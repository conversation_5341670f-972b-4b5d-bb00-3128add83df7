-- 还款记录表
DROP TABLE IF EXISTS `repayment_record`;
CREATE TABLE `repayment_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '还款ID',
  `client_id` varchar(50) NOT NULL COMMENT '客户索引号',
  `client_name` varchar(100) NOT NULL COMMENT '客户姓名',
  `id_card` varchar(18) DEFAULT NULL COMMENT '身份证号',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `repayment_no` varchar(50) NOT NULL COMMENT '还款编号',
  `entrusted_amount` decimal(15,2) DEFAULT NULL COMMENT '委托金额',
  `overdue_amount` decimal(15,2) DEFAULT NULL COMMENT '逾期金额',
  `repayment_amount` decimal(15,2) NOT NULL COMMENT '还款金额',
  `principal_repayment` decimal(15,2) DEFAULT 0.00 COMMENT '本金还款',
  `interest_repayment` decimal(15,2) DEFAULT 0.00 COMMENT '利息还款',
  `fee_repayment` decimal(15,2) DEFAULT 0.00 COMMENT '费用还款',
  `repayment_date` date NOT NULL COMMENT '还款日期',
  `repayment_time` datetime DEFAULT NULL COMMENT '还款时间',
  `repayment_type` varchar(20) DEFAULT 'BANK' COMMENT '还款方式(BANK:银行转账,ALIPAY:支付宝,WECHAT:微信,CASH:现金,POS:POS机,OTHER:其他)',
  `repayment_channel` varchar(50) DEFAULT NULL COMMENT '还款渠道',
  `repayment_status` varchar(20) DEFAULT 'SUCCESS' COMMENT '还款状态(SUCCESS:成功,FAILED:失败,PENDING:处理中,CANCELLED:已取消)',
  `transaction_no` varchar(100) DEFAULT NULL COMMENT '交易流水号',
  `bank_serial_no` varchar(100) DEFAULT NULL COMMENT '银行流水号',
  `receive_account` varchar(50) DEFAULT NULL COMMENT '收款账户',
  `receive_bank` varchar(100) DEFAULT NULL COMMENT '收款银行',
  `pay_account` varchar(50) DEFAULT NULL COMMENT '付款账户',
  `pay_bank` varchar(100) DEFAULT NULL COMMENT '付款银行',
  `payment_type` varchar(20) DEFAULT 'ACTIVE' COMMENT '还款类型(ACTIVE:主动还款,PASSIVE:被动还款,PARTIAL:部分还款,FULL:全额还款)',
  `is_overdue` tinyint(1) DEFAULT 0 COMMENT '是否逾期还款',
  `overdue_days` int(11) DEFAULT 0 COMMENT '逾期天数',
  `reduction_amount` decimal(15,2) DEFAULT 0.00 COMMENT '减免金额',
  `actual_amount` decimal(15,2) DEFAULT NULL COMMENT '实收金额',
  `handling_fee` decimal(15,2) DEFAULT 0.00 COMMENT '手续费',
  `remark` text DEFAULT NULL COMMENT '备注',
  `operator_id` bigint(20) DEFAULT NULL COMMENT '操作员ID',
  `audit_status` varchar(20) DEFAULT 'PENDING' COMMENT '审核状态(PENDING:待审核,APPROVED:已审核,REJECTED:已拒绝)',
  `auditor_id` bigint(20) DEFAULT NULL COMMENT '审核人ID',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_remark` text DEFAULT NULL COMMENT '审核意见',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '删除标记(0:正常,1:删除)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_repayment_no` (`repayment_no`),
  KEY `idx_client_id` (`client_id`),
  KEY `idx_client_name` (`client_name`),
  KEY `idx_repayment_date` (`repayment_date`),
  KEY `idx_repayment_status` (`repayment_status`),
  KEY `idx_audit_status` (`audit_status`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='还款记录表';

-- 插入测试数据
INSERT INTO `repayment_record` (
  `client_id`, `client_name`, `id_card`, `phone`, `repayment_no`, 
  `entrusted_amount`, `overdue_amount`, `repayment_amount`, `principal_repayment`, 
  `interest_repayment`, `fee_repayment`, `repayment_date`, `repayment_time`,
  `repayment_type`, `repayment_channel`, `repayment_status`, `transaction_no`,
  `bank_serial_no`, `receive_account`, `receive_bank`, `pay_account`, `pay_bank`,
  `payment_type`, `is_overdue`, `overdue_days`, `reduction_amount`, `actual_amount`,
  `handling_fee`, `remark`, `operator_id`, `audit_status`, `auditor_id`, `audit_time`
) VALUES 
('ID1001', '张三', '110101199001011234', '***********', 'RP202401001', 
 25000.00, 23500.00, 5000.00, 4500.00, 400.00, 100.00, '2024-01-15', '2024-01-15 14:30:00',
 'BANK', '工商银行', 'SUCCESS', 'TXN20240115001', 'BSN20240115001', 
 '****************', '工商银行北京分行', '****************', '招商银行北京分行',
 'ACTIVE', 0, 0, 0.00, 5000.00, 5.00, '客户主动还款', 1, 'APPROVED', 2, '2024-01-15 15:00:00'),

('ID1002', '李四', '110101199002021234', '***********', 'RP202401002',
 18000.00, 17200.00, 3000.00, 2800.00, 150.00, 50.00, '2024-01-16', '2024-01-16 10:15:00',
 'ALIPAY', '支付宝', 'SUCCESS', 'TXN20240116001', NULL,
 '****************', '建设银行上海分行', '<EMAIL>', '支付宝',
 'PARTIAL', 1, 15, 200.00, 2800.00, 2.00, '部分还款，减免200元', 1, 'APPROVED', 2, '2024-01-16 11:00:00'),

('ID1003', '王五', '110101199003031234', '***********', 'RP202401003',
 35000.00, 33800.00, 8000.00, 7500.00, 400.00, 100.00, '2024-01-17', '2024-01-17 16:45:00',
 'WECHAT', '微信支付', 'SUCCESS', 'TXN20240117001', NULL,
 '****************', '农业银行广州分行', 'wechat_pay_123456', '微信支付',
 'ACTIVE', 0, 0, 0.00, 8000.00, 8.00, '微信转账还款', 1, 'APPROVED', 2, '2024-01-17 17:00:00'),

('ID1004', '赵六', '110101199004041234', '13800138004', 'RP202401004',
 12000.00, 11500.00, 2000.00, 1800.00, 150.00, 50.00, '2024-01-18', '2024-01-18 09:30:00',
 'CASH', '现金', 'SUCCESS', 'TXN20240118001', NULL,
 '6222021234567893', '中国银行深圳分行', NULL, '现金',
 'PASSIVE', 1, 25, 500.00, 1500.00, 0.00, '现金还款，减免500元', 1, 'APPROVED', 2, '2024-01-18 10:00:00'),

('ID1005', '钱七', '110101199005051234', '13800138005', 'RP202401005',
 28000.00, 26800.00, 6000.00, 5500.00, 400.00, 100.00, '2024-01-19', '2024-01-19 13:20:00',
 'POS', 'POS机', 'SUCCESS', 'TXN20240119001', 'BSN20240119001',
 '****************', '交通银行杭州分行', '****************', '民生银行杭州分行',
 'FULL', 0, 0, 0.00, 6000.00, 10.00, 'POS机刷卡还款', 1, 'APPROVED', 2, '2024-01-19 14:00:00'),

('ID1001', '张三', '110101199001011234', '***********', 'RP202401006',
 25000.00, 18500.00, 3000.00, 2700.00, 250.00, 50.00, '2024-01-20', '2024-01-20 11:45:00',
 'BANK', '工商银行', 'PENDING', 'TXN20240120001', 'BSN20240120001',
 '****************', '工商银行北京分行', '****************', '招商银行北京分行',
 'ACTIVE', 0, 0, 0.00, 3000.00, 3.00, '第二次还款', 1, 'PENDING', NULL, NULL),

('ID1002', '李四', '110101199002021234', '***********', 'RP202401007',
 18000.00, 14200.00, 2500.00, 2300.00, 150.00, 50.00, '2024-01-21', '2024-01-21 15:30:00',
 'ALIPAY', '支付宝', 'FAILED', 'TXN20240121001', NULL,
 '****************', '建设银行上海分行', '<EMAIL>', '支付宝',
 'ACTIVE', 0, 0, 0.00, 2500.00, 2.50, '支付失败，余额不足', 1, 'REJECTED', 2, '2024-01-21 16:00:00'),

('ID1003', '王五', '110101199003031234', '***********', 'RP202401008',
 35000.00, 25800.00, 10000.00, 9500.00, 400.00, 100.00, '2024-01-22', '2024-01-22 12:15:00',
 'BANK', '农业银行', 'SUCCESS', 'TXN20240122001', 'BSN20240122001',
 '****************', '农业银行广州分行', '****************', '工商银行广州分行',
 'ACTIVE', 0, 0, 1000.00, 9000.00, 10.00, '大额还款，减免1000元', 1, 'APPROVED', 2, '2024-01-22 13:00:00');
