package com.cf.financing.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cf.financing.common.Result;
import com.cf.financing.entity.SysRole;
import com.cf.financing.entity.SysUser;
import com.cf.financing.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 系统用户控制器
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Slf4j
@Api(tags = "系统用户管理")
@RestController
@RequestMapping("/api/system/user")
public class SysUserController {

    @Autowired
    private ISysUserService userService;

    /**
     * 分页查询用户列表
     */
    @ApiOperation("分页查询用户列表")
    @GetMapping("/page")
    @PreAuthorize("hasAuthority('system:user:list')")
    public Result<IPage<SysUser>> getUserPage(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("用户名") @RequestParam(required = false) String username,
            @ApiParam("真实姓名") @RequestParam(required = false) String realName,
            @ApiParam("手机号") @RequestParam(required = false) String phone,
            @ApiParam("邮箱") @RequestParam(required = false) String email,
            @ApiParam("状态") @RequestParam(required = false) String status,
            @ApiParam("部门ID") @RequestParam(required = false) Long deptId,
            @ApiParam("角色ID") @RequestParam(required = false) Long roleId) {
        
        Page<SysUser> page = new Page<>(current, size);
        IPage<SysUser> result = userService.getUserPage(page, username, realName, phone, email, status, deptId, roleId);
        
        return Result.success(result);
    }

    /**
     * 根据ID查询用户详情
     */
    @ApiOperation("根据ID查询用户详情")
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('system:user:query')")
    public Result<SysUser> getUserById(@ApiParam("用户ID") @PathVariable Long id) {
        SysUser user = userService.getUserById(id);
        if (user == null) {
            return Result.error("用户不存在");
        }
        return Result.success(user);
    }

    /**
     * 新增用户
     */
    @ApiOperation("新增用户")
    @PostMapping
    @PreAuthorize("hasAuthority('system:user:add')")
    public Result<String> addUser(@Valid @RequestBody SysUser user) {
        try {
            boolean success = userService.createUser(user);
            if (success) {
                return Result.success("用户创建成功");
            } else {
                return Result.error("用户创建失败");
            }
        } catch (Exception e) {
            log.error("创建用户失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 修改用户
     */
    @ApiOperation("修改用户")
    @PutMapping
    @PreAuthorize("hasAuthority('system:user:edit')")
    public Result<String> updateUser(@Valid @RequestBody SysUser user) {
        try {
            boolean success = userService.updateUser(user);
            if (success) {
                return Result.success("用户更新成功");
            } else {
                return Result.error("用户更新失败");
            }
        } catch (Exception e) {
            log.error("更新用户失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 删除用户
     */
    @ApiOperation("删除用户")
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('system:user:remove')")
    public Result<String> deleteUser(@ApiParam("用户ID") @PathVariable Long id) {
        try {
            boolean success = userService.deleteUser(id);
            if (success) {
                return Result.success("用户删除成功");
            } else {
                return Result.error("用户删除失败");
            }
        } catch (Exception e) {
            log.error("删除用户失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 批量删除用户
     */
    @ApiOperation("批量删除用户")
    @DeleteMapping("/batch")
    @PreAuthorize("hasAuthority('system:user:remove')")
    public Result<String> deleteUsers(@RequestBody List<Long> ids) {
        try {
            boolean success = userService.batchDeleteUsers(ids);
            if (success) {
                return Result.success("用户批量删除成功");
            } else {
                return Result.error("用户批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除用户失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 修改用户状态
     */
    @ApiOperation("修改用户状态")
    @PutMapping("/{id}/status")
    @PreAuthorize("hasAuthority('system:user:edit')")
    public Result<String> updateUserStatus(
            @ApiParam("用户ID") @PathVariable Long id,
            @ApiParam("状态") @RequestParam String status) {
        try {
            boolean success = userService.updateUserStatus(id, status);
            if (success) {
                return Result.success("用户状态更新成功");
            } else {
                return Result.error("用户状态更新失败");
            }
        } catch (Exception e) {
            log.error("更新用户状态失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 重置用户密码
     */
    @ApiOperation("重置用户密码")
    @PutMapping("/{id}/reset-password")
    @PreAuthorize("hasAuthority('system:user:resetPwd')")
    public Result<String> resetPassword(
            @ApiParam("用户ID") @PathVariable Long id,
            @ApiParam("新密码") @RequestParam String newPassword) {
        try {
            boolean success = userService.resetPassword(id, newPassword);
            if (success) {
                return Result.success("密码重置成功");
            } else {
                return Result.error("密码重置失败");
            }
        } catch (Exception e) {
            log.error("重置密码失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 修改密码
     */
    @ApiOperation("修改密码")
    @PutMapping("/change-password")
    public Result<String> changePassword(
            @ApiParam("旧密码") @RequestParam String oldPassword,
            @ApiParam("新密码") @RequestParam String newPassword) {
        try {
            // 这里应该从当前登录用户获取用户ID，暂时使用1L
            boolean success = userService.changePassword(1L, oldPassword, newPassword);
            if (success) {
                return Result.success("密码修改成功");
            } else {
                return Result.error("密码修改失败");
            }
        } catch (Exception e) {
            log.error("修改密码失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 查询用户角色列表
     */
    @ApiOperation("查询用户角色列表")
    @GetMapping("/{id}/roles")
    @PreAuthorize("hasAuthority('system:user:query')")
    public Result<List<SysRole>> getUserRoles(@ApiParam("用户ID") @PathVariable Long id) {
        List<SysRole> roles = userService.getUserRoles(id);
        return Result.success(roles);
    }

    /**
     * 分配用户角色
     */
    @ApiOperation("分配用户角色")
    @PostMapping("/{id}/roles")
    @PreAuthorize("hasAuthority('system:user:auth')")
    public Result<String> assignUserRoles(
            @ApiParam("用户ID") @PathVariable Long id,
            @RequestBody List<Long> roleIds) {
        try {
            // 这里应该从当前登录用户获取操作人ID，暂时使用1L
            boolean success = userService.assignUserRoles(id, roleIds, 1L);
            if (success) {
                return Result.success("用户角色分配成功");
            } else {
                return Result.error("用户角色分配失败");
            }
        } catch (Exception e) {
            log.error("分配用户角色失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 移除用户角色
     */
    @ApiOperation("移除用户角色")
    @DeleteMapping("/{id}/roles")
    @PreAuthorize("hasAuthority('system:user:auth')")
    public Result<String> removeUserRoles(
            @ApiParam("用户ID") @PathVariable Long id,
            @RequestBody List<Long> roleIds) {
        try {
            boolean success = userService.removeUserRoles(id, roleIds);
            if (success) {
                return Result.success("用户角色移除成功");
            } else {
                return Result.error("用户角色移除失败");
            }
        } catch (Exception e) {
            log.error("移除用户角色失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 检查用户是否拥有指定角色
     */
    @ApiOperation("检查用户是否拥有指定角色")
    @GetMapping("/{id}/has-role")
    @PreAuthorize("hasAuthority('system:user:query')")
    public Result<Boolean> hasRole(
            @ApiParam("用户ID") @PathVariable Long id,
            @ApiParam("角色编码") @RequestParam String roleCode) {
        boolean hasRole = userService.hasRole(id, roleCode);
        return Result.success(hasRole);
    }

    /**
     * 检查用户是否拥有指定权限
     */
    @ApiOperation("检查用户是否拥有指定权限")
    @GetMapping("/{id}/has-permission")
    @PreAuthorize("hasAuthority('system:user:query')")
    public Result<Boolean> hasPermission(
            @ApiParam("用户ID") @PathVariable Long id,
            @ApiParam("权限标识") @RequestParam String permission) {
        boolean hasPermission = userService.hasPermission(id, permission);
        return Result.success(hasPermission);
    }

    /**
     * 获取用户权限列表
     */
    @ApiOperation("获取用户权限列表")
    @GetMapping("/{id}/permissions")
    @PreAuthorize("hasAuthority('system:user:query')")
    public Result<List<String>> getUserPermissions(@ApiParam("用户ID") @PathVariable Long id) {
        List<String> permissions = userService.getUserPermissions(id);
        return Result.success(permissions);
    }

    /**
     * 检查用户名是否存在
     */
    @ApiOperation("检查用户名是否存在")
    @GetMapping("/check-username")
    @PreAuthorize("hasAuthority('system:user:query')")
    public Result<Boolean> checkUsernameExists(
            @ApiParam("用户名") @RequestParam String username,
            @ApiParam("排除的用户ID") @RequestParam(required = false) Long excludeUserId) {
        boolean exists = userService.checkUsernameExists(username, excludeUserId);
        return Result.success(exists);
    }

    /**
     * 检查手机号是否存在
     */
    @ApiOperation("检查手机号是否存在")
    @GetMapping("/check-phone")
    @PreAuthorize("hasAuthority('system:user:query')")
    public Result<Boolean> checkPhoneExists(
            @ApiParam("手机号") @RequestParam String phone,
            @ApiParam("排除的用户ID") @RequestParam(required = false) Long excludeUserId) {
        boolean exists = userService.checkPhoneExists(phone, excludeUserId);
        return Result.success(exists);
    }

    /**
     * 检查邮箱是否存在
     */
    @ApiOperation("检查邮箱是否存在")
    @GetMapping("/check-email")
    @PreAuthorize("hasAuthority('system:user:query')")
    public Result<Boolean> checkEmailExists(
            @ApiParam("邮箱") @RequestParam String email,
            @ApiParam("排除的用户ID") @RequestParam(required = false) Long excludeUserId) {
        boolean exists = userService.checkEmailExists(email, excludeUserId);
        return Result.success(exists);
    }

    /**
     * 获取用户统计信息
     */
    @ApiOperation("获取用户统计信息")
    @GetMapping("/statistics")
    @PreAuthorize("hasAuthority('system:user:query')")
    public Result<Map<String, Object>> getUserStatistics(
            @ApiParam("部门ID") @RequestParam(required = false) Long deptId) {
        Map<String, Object> statistics = userService.getUserStatistics(deptId);
        return Result.success(statistics);
    }

    /**
     * 获取在线用户列表
     */
    @ApiOperation("获取在线用户列表")
    @GetMapping("/online")
    @PreAuthorize("hasAuthority('system:user:query')")
    public Result<List<SysUser>> getOnlineUsers() {
        List<SysUser> onlineUsers = userService.getOnlineUsers();
        return Result.success(onlineUsers);
    }

    /**
     * 根据部门ID查询用户
     */
    @ApiOperation("根据部门ID查询用户")
    @GetMapping("/dept/{deptId}")
    @PreAuthorize("hasAuthority('system:user:list')")
    public Result<List<SysUser>> getUsersByDeptId(@ApiParam("部门ID") @PathVariable Long deptId) {
        List<SysUser> users = userService.getUsersByDeptId(deptId);
        return Result.success(users);
    }

    /**
     * 根据角色ID查询用户
     */
    @ApiOperation("根据角色ID查询用户")
    @GetMapping("/role/{roleId}")
    @PreAuthorize("hasAuthority('system:user:list')")
    public Result<List<SysUser>> getUsersByRoleId(@ApiParam("角色ID") @PathVariable Long roleId) {
        List<SysUser> users = userService.getUsersByRoleId(roleId);
        return Result.success(users);
    }

    /**
     * 生成用户编号
     */
    @ApiOperation("生成用户编号")
    @GetMapping("/generate-no")
    @PreAuthorize("hasAuthority('system:user:add')")
    public Result<String> generateUserNo() {
        String userNo = userService.generateUserNo();
        return Result.success(userNo);
    }
}
