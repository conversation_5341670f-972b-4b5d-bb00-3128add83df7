package com.cf.financing.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cf.financing.entity.Statistics;
import com.cf.financing.service.IStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 统计信息控制器
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/api/statistics")
public class StatisticsController {

    @Autowired
    private IStatisticsService statisticsService;

    /**
     * 分页查询统计信息
     */
    @GetMapping("/page")
    public Map<String, Object> getStatisticsPage(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Integer statType,
            @RequestParam(required = false) Long departmentId,
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            Page<Statistics> page = new Page<>(current, size);
            
            LocalDate startLocalDate = null;
            LocalDate endLocalDate = null;
            if (startDate != null && !startDate.isEmpty()) {
                startLocalDate = LocalDate.parse(startDate);
            }
            if (endDate != null && !endDate.isEmpty()) {
                endLocalDate = LocalDate.parse(endDate);
            }
            
            IPage<Statistics> statisticsPage = statisticsService.getStatisticsPage(page, statType,
                    departmentId, userId, startLocalDate, endLocalDate);
            
            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", statisticsPage);
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "查询失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 生成日统计
     */
    @PostMapping("/generate-daily")
    public Map<String, Object> generateDailyStatistics(
            @RequestParam String statDate,
            @RequestParam(required = false) Long userId) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            LocalDate date = LocalDate.parse(statDate);
            boolean success = statisticsService.generateDailyStatistics(date, userId);
            if (success) {
                result.put("code", 200);
                result.put("message", "生成日统计成功");
            } else {
                result.put("code", 500);
                result.put("message", "生成日统计失败");
            }
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "生成日统计失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 生成月统计
     */
    @PostMapping("/generate-monthly")
    public Map<String, Object> generateMonthlyStatistics(
            @RequestParam Integer year,
            @RequestParam Integer month,
            @RequestParam(required = false) Long userId) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = statisticsService.generateMonthlyStatistics(year, month, userId);
            if (success) {
                result.put("code", 200);
                result.put("message", "生成月统计成功");
            } else {
                result.put("code", 500);
                result.put("message", "生成月统计失败");
            }
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "生成月统计失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 自动生成统计
     */
    @PostMapping("/auto-generate")
    public Map<String, Object> autoGenerateStatistics(@RequestParam String statDate) {
        Map<String, Object> result = new HashMap<>();
        try {
            LocalDate date = LocalDate.parse(statDate);
            boolean success = statisticsService.autoGenerateStatistics(date);
            if (success) {
                result.put("code", 200);
                result.put("message", "自动生成统计成功");
            } else {
                result.put("code", 500);
                result.put("message", "自动生成统计失败");
            }
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "自动生成统计失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 获取部门统计
     */
    @GetMapping("/department")
    public Map<String, Object> getDepartmentStatistics(
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam(required = false) Integer statType) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            LocalDate startLocalDate = LocalDate.parse(startDate);
            LocalDate endLocalDate = LocalDate.parse(endDate);
            
            List<Map<String, Object>> statistics = statisticsService.getDepartmentStatistics(
                    startLocalDate, endLocalDate, statType);
            
            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", statistics);
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "查询失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 获取用户排行
     */
    @GetMapping("/user-ranking")
    public Map<String, Object> getUserRanking(
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam(defaultValue = "recovered_amount") String orderBy,
            @RequestParam(defaultValue = "10") Integer limit) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            LocalDate startLocalDate = LocalDate.parse(startDate);
            LocalDate endLocalDate = LocalDate.parse(endDate);
            
            List<Map<String, Object>> ranking = statisticsService.getUserRanking(
                    startLocalDate, endLocalDate, orderBy, limit);
            
            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", ranking);
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "查询失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 获取趋势数据
     */
    @GetMapping("/trend")
    public Map<String, Object> getTrendData(
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam(required = false) Integer statType,
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) Long departmentId) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            LocalDate startLocalDate = LocalDate.parse(startDate);
            LocalDate endLocalDate = LocalDate.parse(endDate);
            
            List<Map<String, Object>> trendData = statisticsService.getTrendData(
                    startLocalDate, endLocalDate, statType, userId, departmentId);
            
            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", trendData);
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "查询失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 获取实时统计
     */
    @GetMapping("/realtime")
    public Map<String, Object> getRealTimeStatistics(
            @RequestParam(required = false) String statDate,
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) Long departmentId) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            LocalDate date = statDate != null ? LocalDate.parse(statDate) : LocalDate.now();
            
            Map<String, Object> statistics = statisticsService.getRealTimeStatistics(
                    date, userId, departmentId);
            
            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", statistics);
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "查询失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 获取对比数据
     */
    @GetMapping("/comparison")
    public Map<String, Object> getComparisonData(
            @RequestParam String currentStartDate,
            @RequestParam String currentEndDate,
            @RequestParam String previousStartDate,
            @RequestParam String previousEndDate,
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) Long departmentId) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            LocalDate currentStart = LocalDate.parse(currentStartDate);
            LocalDate currentEnd = LocalDate.parse(currentEndDate);
            LocalDate previousStart = LocalDate.parse(previousStartDate);
            LocalDate previousEnd = LocalDate.parse(previousEndDate);
            
            Map<String, Object> comparisonData = statisticsService.getComparisonData(
                    currentStart, currentEnd, previousStart, previousEnd, userId, departmentId);
            
            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", comparisonData);
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "查询失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 导出统计数据
     */
    @GetMapping("/export")
    public Map<String, Object> exportStatistics(
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam(required = false) Integer statType,
            @RequestParam(required = false) Long departmentId,
            @RequestParam(required = false) Long userId) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            LocalDate startLocalDate = LocalDate.parse(startDate);
            LocalDate endLocalDate = LocalDate.parse(endDate);
            
            String filePath = statisticsService.exportStatistics(
                    startLocalDate, endLocalDate, statType, departmentId, userId);
            
            result.put("code", 200);
            result.put("message", "导出成功");
            result.put("data", filePath);
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "导出失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 删除统计数据
     */
    @DeleteMapping("/delete")
    public Map<String, Object> deleteStatistics(
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam(required = false) Integer statType) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            LocalDate startLocalDate = LocalDate.parse(startDate);
            LocalDate endLocalDate = LocalDate.parse(endDate);
            
            boolean success = statisticsService.deleteStatisticsByDateRange(
                    startLocalDate, endLocalDate, statType);
            
            if (success) {
                result.put("code", 200);
                result.put("message", "删除成功");
            } else {
                result.put("code", 500);
                result.put("message", "删除失败");
            }
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "删除失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 重新计算统计数据
     */
    @PostMapping("/recalculate")
    public Map<String, Object> recalculateStatistics(
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam(required = false) Long userId) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            LocalDate startLocalDate = LocalDate.parse(startDate);
            LocalDate endLocalDate = LocalDate.parse(endDate);
            
            boolean success = statisticsService.recalculateStatistics(
                    startLocalDate, endLocalDate, userId);
            
            if (success) {
                result.put("code", 200);
                result.put("message", "重新计算成功");
            } else {
                result.put("code", 500);
                result.put("message", "重新计算失败");
            }
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "重新计算失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 获取统计概览
     */
    @GetMapping("/overview")
    public Map<String, Object> getStatisticsOverview(
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) Long departmentId) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            Map<String, Object> overview = statisticsService.getStatisticsOverview(userId, departmentId);
            
            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", overview);
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "查询失败: " + e.getMessage());
        }
        return result;
    }
}