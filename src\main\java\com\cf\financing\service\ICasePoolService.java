package com.cf.financing.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cf.financing.entity.CasePool;

import java.util.List;
import java.util.Map;

/**
 * 案池管理Service接口
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
public interface ICasePoolService extends IService<CasePool> {

    /**
     * 分页查询案池数据
     *
     * @param current 当前页
     * @param size 每页大小
     * @param params 查询参数
     * @return 分页结果
     */
    IPage<CasePool> getCasePoolPage(Long current, Long size, Map<String, Object> params);

    /**
     * 获取案池统计信息
     *
     * @param params 查询参数
     * @return 统计信息
     */
    Map<String, Object> getCasePoolStatistics(Map<String, Object> params);

    /**
     * 批量锁定案件
     *
     * @param clientIds 客户索引号列表
     * @return 操作结果
     */
    boolean batchLockCases(List<String> clientIds);

    /**
     * 批量解锁案件
     *
     * @param clientIds 客户索引号列表
     * @return 操作结果
     */
    boolean batchUnlockCases(List<String> clientIds);

    /**
     * 批量分派案件
     *
     * @param clientIds 客户索引号列表
     * @param assignUserId 分派用户ID
     * @return 操作结果
     */
    boolean batchAssignCases(List<String> clientIds, Long assignUserId);

    /**
     * 根据客户索引号查询案件详情
     *
     * @param clientId 客户索引号
     * @return 案件详情
     */
    CasePool getCaseByClientId(String clientId);

    /**
     * 导入案池数据
     *
     * @param casePoolList 案池数据列表
     * @return 导入结果
     */
    Map<String, Object> importCasePoolData(List<CasePool> casePoolList);

    /**
     * 导出案池数据
     *
     * @param params 查询参数
     * @return 导出数据
     */
    List<CasePool> exportCasePoolData(Map<String, Object> params);

    /**
     * 获取案池数据统计图表
     *
     * @return 图表数据
     */
    Map<String, Object> getCasePoolChartData();

    /**
     * 更新案件跟进状态
     *
     * @param clientId 客户索引号
     * @param followupResult 跟进结果
     * @return 更新结果
     */
    boolean updateFollowupStatus(String clientId, String followupResult);

    /**
     * 获取案件详细信息（包含关联数据）
     *
     * @param clientId 客户索引号
     * @return 详细信息
     */
    Map<String, Object> getCaseDetailInfo(String clientId);
}
