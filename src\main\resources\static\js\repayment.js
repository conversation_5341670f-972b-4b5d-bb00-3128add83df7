// 还款管理页面JavaScript功能

// 全局变量
let currentPage = 1;
let pageSize = 20;
let totalRecords = 0;
let totalPages = 1;
let currentFilters = {};
let repaymentData = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    loadRepaymentData();
    setupEventListeners();
});

// 初始化页面
function initializePage() {
    // 初始化日期选择器
    flatpickr("#dateRange", {
        mode: "range",
        dateFormat: "Y-m-d",
        locale: "zh"
    });

    // 设置默认页面大小
    document.getElementById('pageSizeSelect').value = pageSize;
}

// 设置事件监听器
function setupEventListeners() {
    // 全选复选框
    document.getElementById('select-all').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.row-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateStatistics();
    });

    // 搜索按钮
    document.getElementById('searchBtn').addEventListener('click', searchRepayments);

    // 重置按钮
    document.getElementById('resetBtn').addEventListener('click', resetFilters);

    // 功能按钮
    document.getElementById('auditBtn').addEventListener('click', batchAuditRepayments);
    document.getElementById('exportBtn').addEventListener('click', exportData);
    document.getElementById('addBtn').addEventListener('click', addRepayment);
    document.getElementById('refreshBtn').addEventListener('click', refreshData);

    // 分页相关
    document.getElementById('pageSizeSelect').addEventListener('change', changePageSize);
    document.getElementById('gotoPageBtn').addEventListener('click', goToPage);
    document.getElementById('firstPageBtn').addEventListener('click', () => changePage(1));
    document.getElementById('prevPageBtn').addEventListener('click', () => changePage(currentPage - 1));
    document.getElementById('nextPageBtn').addEventListener('click', () => changePage(currentPage + 1));
    document.getElementById('lastPageBtn').addEventListener('click', () => changePage(totalPages));

    // 回车键搜索
    document.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchRepayments();
        }
    });
}

// 加载还款数据
function loadRepaymentData() {
    showLoading();

    const params = new URLSearchParams({
        current: currentPage,
        size: pageSize,
        ...currentFilters
    });

    fetch(`/api/repayment/page?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                repaymentData = data.data;
                renderRepaymentTable(data.data);
                updatePagination(data);
                loadStatistics();
            } else {
                showError('加载数据失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('加载数据失败:', error);
            showError('网络错误，请稍后重试');
        })
        .finally(() => {
            hideLoading();
        });
}

// 渲染还款表格
function renderRepaymentTable(repayments) {
    const tbody = document.getElementById('table-body');
    tbody.innerHTML = '';

    if (!repayments || repayments.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="21" style="text-align: center; padding: 40px; color: #999;">
                    <i class="fas fa-inbox" style="font-size: 48px; margin-bottom: 16px; display: block;"></i>
                    暂无数据
                </td>
            </tr>
        `;
        return;
    }

    repayments.forEach(repayment => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td><input type="checkbox" class="row-checkbox" data-id="${repayment.id}"></td>
            <td>${repayment.clientName || '<span class="empty-value">空值</span>'}</td>
            <td><a href="javascript:void(0);" class="client-id-link" data-id="${repayment.clientId}">${repayment.clientId || '<span class="empty-value">空值</span>'}</a></td>
            <td>${repayment.repaymentNo || '<span class="empty-value">空值</span>'}</td>
            <td class="amount">${repayment.repaymentAmount ? '¥' + formatAmount(repayment.repaymentAmount) : '<span class="empty-value">空值</span>'}</td>
            <td class="amount">${repayment.principalRepayment ? '¥' + formatAmount(repayment.principalRepayment) : '<span class="empty-value">空值</span>'}</td>
            <td class="amount">${repayment.interestRepayment ? '¥' + formatAmount(repayment.interestRepayment) : '<span class="empty-value">空值</span>'}</td>
            <td class="amount">${repayment.feeRepayment ? '¥' + formatAmount(repayment.feeRepayment) : '<span class="empty-value">空值</span>'}</td>
            <td>${formatDate(repayment.repaymentDate)}</td>
            <td>${getRepaymentTypeText(repayment.repaymentType)}</td>
            <td>${repayment.repaymentChannel || '<span class="empty-value">空值</span>'}</td>
            <td><span class="status-tag ${getRepaymentStatusClass(repayment.repaymentStatus)}">${getRepaymentStatusText(repayment.repaymentStatus)}</span></td>
            <td>${getPaymentTypeText(repayment.paymentType)}</td>
            <td><span class="status-tag ${getAuditStatusClass(repayment.auditStatus)}">${getAuditStatusText(repayment.auditStatus)}</span></td>
            <td>${repayment.transactionNo || '<span class="empty-value">空值</span>'}</td>
            <td>${repayment.bankSerialNo || '<span class="empty-value">空值</span>'}</td>
            <td class="amount">${repayment.reductionAmount ? '¥' + formatAmount(repayment.reductionAmount) : '<span class="empty-value">空值</span>'}</td>
            <td class="amount">${repayment.actualAmount ? '¥' + formatAmount(repayment.actualAmount) : '<span class="empty-value">空值</span>'}</td>
            <td class="amount">${repayment.handlingFee ? '¥' + formatAmount(repayment.handlingFee) : '<span class="empty-value">空值</span>'}</td>
            <td>${repayment.remark || '<span class="empty-value">空值</span>'}</td>
            <td>
                <button class="data-action-btn" onclick="viewRepayment(${repayment.id})" title="查看详情">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="data-action-btn" onclick="editRepayment(${repayment.id})" title="编辑">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="data-action-btn" onclick="auditRepayment(${repayment.id})" title="审核">
                    <i class="fas fa-check"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);

        // 为行复选框添加事件监听器
        const checkbox = row.querySelector('.row-checkbox');
        checkbox.addEventListener('change', updateStatistics);
    });
}

// 更新分页信息
function updatePagination(pageData) {
    totalRecords = pageData.total;
    totalPages = pageData.pages;
    currentPage = pageData.current;

    // 更新分页显示
    document.getElementById('total-count').textContent = totalRecords;
    document.getElementById('pageNumInput').value = currentPage;
    document.getElementById('pageNumInput').max = totalPages;

    // 更新按钮状态
    document.getElementById('firstPageBtn').disabled = currentPage <= 1;
    document.getElementById('prevPageBtn').disabled = currentPage <= 1;
    document.getElementById('nextPageBtn').disabled = currentPage >= totalPages;
    document.getElementById('lastPageBtn').disabled = currentPage >= totalPages;
}

// 加载统计信息
function loadStatistics() {
    fetch('/api/repayment/statistics?' + new URLSearchParams(currentFilters))
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateStatisticsDisplay(data.data);
            }
        })
        .catch(error => {
            console.error('加载统计信息失败:', error);
        });
}

// 更新统计信息显示
function updateStatisticsDisplay(stats) {
    document.getElementById('total-records').textContent = stats.totalCount || 0;
    document.getElementById('total-amount').textContent = '¥' + formatAmount(stats.totalAmount || 0);

    // 更新勾选统计
    updateSelectedStatistics();
}

// 更新勾选统计
function updateSelectedStatistics() {
    const selectedCheckboxes = document.querySelectorAll('.row-checkbox:checked');
    document.getElementById('selected-records').textContent = selectedCheckboxes.length;

    // 计算勾选金额
    let selectedAmount = 0;
    selectedCheckboxes.forEach(checkbox => {
        const repaymentId = checkbox.getAttribute('data-id');
        const item = repaymentData.find(d => d.id == repaymentId);
        if (item && item.repaymentAmount) {
            selectedAmount += parseFloat(item.repaymentAmount);
        }
    });

    document.getElementById('selected-amount').textContent = '¥' + formatAmount(selectedAmount);
}

// 更新统计信息（用于复选框变化时调用）
function updateStatistics() {
    updateSelectedStatistics();
}

// 搜索还款数据
function searchRepayments() {
    currentFilters = {
        clientName: document.getElementById('clientName').value.trim(),
        clientId: document.getElementById('clientId').value.trim(),
        repaymentNo: document.getElementById('repaymentNo').value.trim(),
        repaymentStatus: document.getElementById('repaymentStatus').value,
        repaymentType: document.getElementById('repaymentType').value,
        auditStatus: document.getElementById('auditStatus').value,
        paymentType: document.getElementById('paymentType').value,
        minAmount: document.getElementById('minAmount').value,
        maxAmount: document.getElementById('maxAmount').value
    };

    // 处理日期范围
    const dateRange = document.getElementById('dateRange').value;
    if (dateRange) {
        const dates = dateRange.split(' to ');
        if (dates.length === 2) {
            currentFilters.startDate = dates[0];
            currentFilters.endDate = dates[1];
        }
    }

    // 移除空值
    Object.keys(currentFilters).forEach(key => {
        if (!currentFilters[key]) {
            delete currentFilters[key];
        }
    });

    currentPage = 1;
    loadRepaymentData();
}

// 重置筛选条件
function resetFilters() {
    // 清空所有输入框
    const inputs = document.querySelectorAll('.filter-control');
    inputs.forEach(input => {
        if (input.type === 'select-one') {
            input.selectedIndex = 0;
        } else {
            input.value = '';
        }
    });

    currentFilters = {};
    currentPage = 1;
    loadRepaymentData();
}

// 分页相关函数
function changePageSize() {
    pageSize = parseInt(document.getElementById('pageSizeSelect').value);
    currentPage = 1;
    loadRepaymentData();
}

function changePage(page) {
    if (page >= 1 && page <= totalPages) {
        currentPage = page;
        loadRepaymentData();
    }
}

function goToPage() {
    const page = parseInt(document.getElementById('pageNumInput').value);
    if (page >= 1 && page <= totalPages) {
        currentPage = page;
        loadRepaymentData();
    } else {
        document.getElementById('pageNumInput').value = currentPage;
    }
}

// 功能按钮
function exportData() {
    const params = new URLSearchParams(currentFilters);
    window.open(`/api/repayment/export?${params}`, '_blank');
}

function batchAuditRepayments() {
    const selectedIds = getSelectedRepaymentIds();
    if (selectedIds.length === 0) {
        showError('请选择要审核的还款记录');
        return;
    }

    // 这里应该打开审核对话框，暂时使用简单的prompt
    const auditStatus = prompt('请选择审核状态 (APPROVED/REJECTED):');
    if (auditStatus && (auditStatus === 'APPROVED' || auditStatus === 'REJECTED')) {
        const auditRemark = prompt('请输入审核意见:') || '';

        fetch('/api/repayment/batch-audit', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                ids: selectedIds,
                auditStatus: auditStatus,
                auditorId: 1, // 暂时使用固定值
                auditRemark: auditRemark
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess(data.message);
                loadRepaymentData();
            } else {
                showError(data.message);
            }
        })
        .catch(error => {
            console.error('审核失败:', error);
            showError('审核失败，请稍后重试');
        });
    }
}

function addRepayment() {
    // 这里应该打开新增还款对话框
    alert('新增还款功能开发中...');
}

function refreshData() {
    loadRepaymentData();
}

// 单个操作函数
function viewRepayment(id) {
    window.open(`/repayment/detail/${id}`, '_blank');
}

function editRepayment(id) {
    // 这里应该打开编辑对话框
    alert('编辑功能开发中...');
}

function auditRepayment(id) {
    // 这里应该打开审核对话框
    alert('审核功能开发中...');
}

// 工具函数
function getSelectedRepaymentIds() {
    const checkboxes = document.querySelectorAll('.row-checkbox:checked');
    return Array.from(checkboxes).map(cb => parseInt(cb.getAttribute('data-id')));
}

function getRepaymentTypeText(type) {
    const typeMap = {
        'BANK': '银行转账',
        'ALIPAY': '支付宝',
        'WECHAT': '微信',
        'CASH': '现金',
        'POS': 'POS机',
        'OTHER': '其他'
    };
    return typeMap[type] || type || '<span class="empty-value">空值</span>';
}

function getRepaymentStatusClass(status) {
    const statusMap = {
        'SUCCESS': 'status-success',
        'FAILED': 'status-failed',
        'PENDING': 'status-pending',
        'CANCELLED': 'status-failed'
    };
    return statusMap[status] || 'status-pending';
}

function getRepaymentStatusText(status) {
    const statusMap = {
        'SUCCESS': '成功',
        'FAILED': '失败',
        'PENDING': '处理中',
        'CANCELLED': '已取消'
    };
    return statusMap[status] || status || '<span class="empty-value">空值</span>';
}

function getPaymentTypeText(type) {
    const typeMap = {
        'ACTIVE': '主动还款',
        'PASSIVE': '被动还款',
        'PARTIAL': '部分还款',
        'FULL': '全额还款'
    };
    return typeMap[type] || type || '<span class="empty-value">空值</span>';
}

function getAuditStatusClass(status) {
    const statusMap = {
        'APPROVED': 'audit-approved',
        'REJECTED': 'audit-rejected',
        'PENDING': 'audit-pending'
    };
    return statusMap[status] || 'audit-pending';
}

function getAuditStatusText(status) {
    const statusMap = {
        'APPROVED': '已审核',
        'REJECTED': '已拒绝',
        'PENDING': '待审核'
    };
    return statusMap[status] || status || '<span class="empty-value">空值</span>';
}

function formatAmount(amount) {
    if (!amount) return '0.00';
    return parseFloat(amount).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
}

function formatDate(dateStr) {
    if (!dateStr) return '<span class="empty-value">空值</span>';
    const date = new Date(dateStr);
    return date.toLocaleDateString('zh-CN');
}

function showLoading() {
    const tbody = document.getElementById('table-body');
    tbody.innerHTML = `
        <tr>
            <td colspan="21" style="text-align: center; padding: 40px;">
                <i class="fas fa-spinner fa-spin" style="font-size: 24px; margin-right: 8px;"></i>
                加载中...
            </td>
        </tr>
    `;
}

function hideLoading() {
    // 加载完成后会被数据替换，无需特殊处理
}

function showError(message) {
    alert('错误: ' + message);
}

function showSuccess(message) {
    alert('成功: ' + message);
}