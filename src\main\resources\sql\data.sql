-- 插入默认用户（密码：123456）
INSERT INTO sys_user (username, password, real_name, phone, email, status) VALUES 
('admin', '$2a$10$7JB720yubVSOfvVWbGReyO.Zt8Qd3XOKmVqjpxnkgMlDv.sK8QM8K', '系统管理员', '13800138000', '<EMAIL>', 1),
('user1', '$2a$10$7JB720yubVSOfvVWbGReyO.Zt8Qd3XOKmVqjpxnkgMlDv.sK8QM8K', '张三', '13800138001', '<EMAIL>', 1),
('user2', '$2a$10$7JB720yubVSOfvVWbGReyO.Zt8Qd3XOKmVqjpxnkgMlDv.sK8QM8K', '李四', '13800138002', '<EMAIL>', 1);

-- 插入默认角色
INSERT INTO sys_role (role_code, role_name, description, status, sort_order) VALUES 
('ADMIN', '系统管理员', '系统管理员角色，拥有所有权限', 1, 1),
('USER', '普通用户', '普通用户角色', 1, 2),
('MANAGER', '部门经理', '部门经理角色', 1, 3);

-- 插入菜单数据
INSERT INTO sys_menu (menu_name, menu_code, parent_id, menu_type, path, component, permission, icon, sort_order, visible, status) VALUES 
-- 一级菜单
('首页', 'home', 0, 'C', '/home', 'home', 'system:home:view', 'fa fa-home', 1, 1, 1),
('案池管理', 'casepool', 0, 'C', '/case-pool', 'case-pool', 'system:casepool:view', 'fa fa-folder-open', 2, 1, 1),
('还款管理', 'repayment', 0, 'C', '/repayment', 'repayment', 'system:repayment:view', 'fa fa-credit-card', 3, 1, 1),
('作业清单', 'tasklist', 0, 'C', '/tasklist', 'tasklist', 'system:tasklist:view', 'fa fa-tasks', 4, 1, 1),
('新增统计', 'statistics', 0, 'C', '/statistics', 'statistics', 'system:statistics:view', 'fa fa-bar-chart', 5, 1, 1),
('换单查询', 'exchange', 0, 'C', '/exchange', 'exchange', 'system:exchange:view', 'fa fa-exchange', 6, 1, 1),
('系统管理', 'system', 0, 'M', '/system', '', 'system:manage', 'fa fa-cog', 7, 1, 1);

-- 系统管理子菜单
INSERT INTO sys_menu (menu_name, menu_code, parent_id, menu_type, path, component, permission, icon, sort_order, visible, status) VALUES 
('用户管理', 'user-management', 7, 'C', '/system/user', 'system/user-management', 'system:user:list', 'fa fa-users', 1, 1, 1),
('角色管理', 'role-management', 7, 'C', '/system/role', 'system/role-management', 'system:role:list', 'fa fa-user-circle', 2, 1, 1),
('菜单管理', 'menu-management', 7, 'C', '/system/menu', 'system/menu-management', 'system:menu:list', 'fa fa-list', 3, 1, 1);

-- 用户管理按钮权限
INSERT INTO sys_menu (menu_name, menu_code, parent_id, menu_type, permission, sort_order, visible, status) VALUES 
('用户查询', 'user-query', 8, 'F', 'system:user:query', 1, 0, 1),
('用户新增', 'user-add', 8, 'F', 'system:user:add', 2, 0, 1),
('用户修改', 'user-edit', 8, 'F', 'system:user:edit', 3, 0, 1),
('用户删除', 'user-remove', 8, 'F', 'system:user:remove', 4, 0, 1),
('重置密码', 'user-resetPwd', 8, 'F', 'system:user:resetPwd', 5, 0, 1),
('分配角色', 'user-auth', 8, 'F', 'system:user:auth', 6, 0, 1);

-- 角色管理按钮权限
INSERT INTO sys_menu (menu_name, menu_code, parent_id, menu_type, permission, sort_order, visible, status) VALUES 
('角色查询', 'role-query', 9, 'F', 'system:role:query', 1, 0, 1),
('角色新增', 'role-add', 9, 'F', 'system:role:add', 2, 0, 1),
('角色修改', 'role-edit', 9, 'F', 'system:role:edit', 3, 0, 1),
('角色删除', 'role-remove', 9, 'F', 'system:role:remove', 4, 0, 1),
('分配权限', 'role-auth', 9, 'F', 'system:role:auth', 5, 0, 1);

-- 菜单管理按钮权限
INSERT INTO sys_menu (menu_name, menu_code, parent_id, menu_type, permission, sort_order, visible, status) VALUES 
('菜单查询', 'menu-query', 10, 'F', 'system:menu:query', 1, 0, 1),
('菜单新增', 'menu-add', 10, 'F', 'system:menu:add', 2, 0, 1),
('菜单修改', 'menu-edit', 10, 'F', 'system:menu:edit', 3, 0, 1),
('菜单删除', 'menu-remove', 10, 'F', 'system:menu:remove', 4, 0, 1);

-- 分配用户角色关系
INSERT INTO sys_user_role (user_id, role_id) VALUES 
(1, 1), -- admin用户分配管理员角色
(2, 2), -- user1分配普通用户角色
(3, 3); -- user2分配部门经理角色

-- 分配角色菜单权限（管理员拥有所有权限）
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT 1, id FROM sys_menu WHERE deleted = 0;
