# RBAC系统部署和测试指南

## 编译状态
✅ **编译成功** - 所有依赖问题已解决

## 部署步骤

### 1. 数据库准备
```sql
-- 1. 执行菜单数据初始化
source src/main/resources/sql/sys_menu.sql;

-- 2. 执行角色权限初始化
source src/main/resources/sql/init_role_permissions.sql;

-- 3. 验证数据
SELECT COUNT(*) as menu_count FROM sys_menu;
SELECT COUNT(*) as role_menu_count FROM sys_role_menu;
SELECT COUNT(*) as user_role_count FROM sys_user_role;
```

### 2. 启动应用
```bash
# 编译项目
mvn clean compile

# 启动应用
mvn spring-boot:run
```

### 3. 访问测试页面
- **RBAC功能测试**: http://localhost:8080/test/rbac
- **主控制台**: http://localhost:8080/dashboard
- **用户管理**: http://localhost:8080/system/user
- **角色管理**: http://localhost:8080/system/role
- **菜单管理**: http://localhost:8080/system/menu

## 功能测试清单

### ✅ 已实现的功能

#### 1. 动态菜单系统
- [x] 菜单数据库存储
- [x] 基于角色的菜单过滤
- [x] 动态菜单加载API
- [x] 前端菜单渲染

#### 2. 用户角色管理
- [x] 角色分配界面
- [x] 多角色选择支持
- [x] 后端API集成
- [x] 实时数据更新

#### 3. 角色权限管理
- [x] 权限树形结构
- [x] 父子节点联动
- [x] 全选/全不选功能
- [x] 展开/收起功能
- [x] 后端API集成

#### 4. 菜单管理增强
- [x] 树形表格显示
- [x] 可展开/收起子菜单
- [x] 层级缩进显示
- [x] 新增子菜单功能

## API接口测试

### 菜单相关API
```bash
# 获取用户菜单树
GET /api/menu/user-menus

# 获取完整菜单树
GET /api/menu/tree

# 获取角色菜单权限
GET /api/menu/role/{roleId}
```

### 用户角色API
```bash
# 获取所有角色
GET /api/user-role/roles

# 获取用户角色
GET /api/user-role/user/{userId}

# 分配用户角色
POST /api/user-role/assign
Content-Type: application/x-www-form-urlencoded
Body: userId=1&roleIds=1,2
```

### 角色权限API
```bash
# 获取角色权限
GET /api/role-menu/role/{roleId}

# 分配角色权限
POST /api/role-menu/assign
Content-Type: application/x-www-form-urlencoded
Body: roleId=1&menuIds=1,2,3,7,8,9
```

## 测试场景

### 场景1: 用户角色分配
1. 访问用户管理页面
2. 点击"分配角色"按钮
3. 选择角色并保存
4. 验证角色分配成功

### 场景2: 角色权限管理
1. 访问角色管理页面
2. 点击"权限"按钮
3. 在权限树中选择菜单
4. 保存权限配置
5. 验证权限分配成功

### 场景3: 动态菜单加载
1. 使用不同角色的用户登录
2. 查看侧边栏菜单
3. 验证菜单根据角色权限显示

### 场景4: 菜单管理
1. 访问菜单管理页面
2. 查看树形结构
3. 测试展开/收起功能
4. 测试新增子菜单

## 故障排除

### 常见问题

#### 1. 编译错误
- **问题**: 找不到符号错误
- **解决**: 确保所有依赖已正确导入，运行 `mvn clean compile`

#### 2. 数据库连接问题
- **问题**: 无法连接数据库
- **解决**: 检查 `application.yml` 中的数据库配置

#### 3. API返回404
- **问题**: 接口无法访问
- **解决**: 确保控制器类已添加正确的注解，检查请求路径

#### 4. 前端功能异常
- **问题**: 页面功能不正常
- **解决**: 检查浏览器控制台错误，确认API调用正常

### 调试技巧

1. **查看日志**: 检查应用启动日志和运行时日志
2. **API测试**: 使用测试页面或Postman测试API
3. **数据库检查**: 直接查询数据库验证数据正确性
4. **浏览器调试**: 使用F12开发者工具检查网络请求

## 性能优化建议

1. **缓存优化**: 添加Redis缓存菜单和权限数据
2. **数据库优化**: 为常用查询字段添加索引
3. **前端优化**: 实现菜单懒加载和虚拟滚动
4. **API优化**: 减少不必要的数据传输

## 安全注意事项

1. **权限验证**: 确保所有API都有适当的权限检查
2. **数据校验**: 对用户输入进行严格验证
3. **日志记录**: 记录重要操作的审计日志
4. **会话管理**: 实现安全的会话超时机制

## 下一步计划

1. **按钮级权限**: 实现更细粒度的权限控制
2. **权限继承**: 实现角色权限继承机制
3. **批量操作**: 支持批量分配角色和权限
4. **审计日志**: 添加完整的操作审计功能
5. **权限缓存**: 实现高效的权限缓存机制
