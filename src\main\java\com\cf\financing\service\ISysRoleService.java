package com.cf.financing.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cf.financing.entity.SysRole;

import java.util.List;

/**
 * 系统角色服务接口
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
public interface ISysRoleService extends IService<SysRole> {

    /**
     * 分页查询角色列表
     *
     * @param page 分页参数
     * @param roleName 角色名称
     * @param roleCode 角色编码
     * @param status 状态
     * @return 角色分页列表
     */
    IPage<SysRole> selectRolePage(Page<SysRole> page, String roleName, String roleCode, Integer status);

    /**
     * 根据用户ID查询角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<SysRole> selectRolesByUserId(Long userId);

    /**
     * 查询所有启用的角色
     *
     * @return 角色列表
     */
    List<SysRole> selectEnabledRoles();

    /**
     * 根据角色编码查询角色
     *
     * @param roleCode 角色编码
     * @return 角色信息
     */
    SysRole selectByRoleCode(String roleCode);

    /**
     * 新增角色
     *
     * @param role 角色信息
     * @return 是否成功
     */
    boolean insertRole(SysRole role);

    /**
     * 修改角色
     *
     * @param role 角色信息
     * @return 是否成功
     */
    boolean updateRole(SysRole role);

    /**
     * 删除角色
     *
     * @param roleId 角色ID
     * @return 是否成功
     */
    boolean deleteRole(Long roleId);

    /**
     * 批量删除角色
     *
     * @param roleIds 角色ID列表
     * @return 是否成功
     */
    boolean deleteRoles(List<Long> roleIds);

    /**
     * 检查角色编码是否存在
     *
     * @param roleCode 角色编码
     * @param excludeId 排除的角色ID
     * @return 是否存在
     */
    boolean checkRoleCodeExists(String roleCode, Long excludeId);

    /**
     * 检查角色名称是否存在
     *
     * @param roleName 角色名称
     * @param excludeId 排除的角色ID
     * @return 是否存在
     */
    boolean checkRoleNameExists(String roleName, Long excludeId);

    /**
     * 分配角色菜单权限
     *
     * @param roleId 角色ID
     * @param menuIds 菜单ID列表
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean assignRoleMenus(Long roleId, List<Long> menuIds, Long operatorId);

    /**
     * 查询角色的菜单权限ID列表
     *
     * @param roleId 角色ID
     * @return 菜单ID列表
     */
    List<Long> selectMenuIdsByRoleId(Long roleId);

    /**
     * 修改角色状态
     *
     * @param roleId 角色ID
     * @param status 状态
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean updateRoleStatus(Long roleId, Integer status, Long operatorId);

    /**
     * 检查角色是否被使用
     *
     * @param roleId 角色ID
     * @return 是否被使用
     */
    boolean isRoleInUse(Long roleId);

    /**
     * 根据角色ID列表查询角色
     *
     * @param roleIds 角色ID列表
     * @return 角色列表
     */
    List<SysRole> selectRolesByIds(List<Long> roleIds);

    /**
     * 获取角色统计信息
     *
     * @return 统计信息
     */
    Object getRoleStatistics();

    /**
     * 导出角色数据
     *
     * @param roleName 角色名称
     * @param roleCode 角色编码
     * @param status 状态
     * @return 角色列表
     */
    List<SysRole> exportRoles(String roleName, String roleCode, Integer status);

    /**
     * 复制角色
     *
     * @param sourceRoleId 源角色ID
     * @param newRoleName 新角色名称
     * @param newRoleCode 新角色编码
     * @param operatorId 操作人ID
     * @return 是否成功
     */
    boolean copyRole(Long sourceRoleId, String newRoleName, String newRoleCode, Long operatorId);
}