-- 权限管理系统初始化数据
-- 创建时间: 2024-01-01
-- 说明: 初始化用户、角色、菜单数据

-- 清理现有数据（可选，根据需要取消注释）
-- DELETE FROM sys_user_role;
-- DELETE FROM sys_role_menu;
-- DELETE FROM sys_menu WHERE id > 0;
-- DELETE FROM sys_role WHERE id > 0;
-- DELETE FROM sys_user WHERE id > 1;

-- 插入系统菜单数据
INSERT INTO sys_menu (id, menu_name, parent_id, menu_type, path, component, perms, icon, sort, status, visible, is_frame, is_cache, create_time, update_time, remark) VALUES
-- 系统管理（一级菜单）
(1, '系统管理', 0, 'M', '/system', NULL, NULL, 'fas fa-cogs', 1, '1', '1', '0', '1', NOW(), NOW(), '系统管理目录'),

-- 用户管理（二级菜单）
(2, '用户管理', 1, 'C', '/system/user', 'system/user-management', 'system:user:list', 'fas fa-users', 1, '1', '1', '0', '1', NOW(), NOW(), '用户管理菜单'),
(3, '用户查询', 2, 'F', '', '', 'system:user:query', '', 1, '1', '1', '0', '1', NOW(), NOW(), ''),
(4, '用户新增', 2, 'F', '', '', 'system:user:add', '', 2, '1', '1', '0', '1', NOW(), NOW(), ''),
(5, '用户修改', 2, 'F', '', '', 'system:user:edit', '', 3, '1', '1', '0', '1', NOW(), NOW(), ''),
(6, '用户删除', 2, 'F', '', '', 'system:user:remove', '', 4, '1', '1', '0', '1', NOW(), NOW(), ''),
(7, '用户导出', 2, 'F', '', '', 'system:user:export', '', 5, '1', '1', '0', '1', NOW(), NOW(), ''),
(8, '用户导入', 2, 'F', '', '', 'system:user:import', '', 6, '1', '1', '0', '1', NOW(), NOW(), ''),
(9, '重置密码', 2, 'F', '', '', 'system:user:resetPwd', '', 7, '1', '1', '0', '1', NOW(), NOW(), ''),

-- 角色管理（二级菜单）
(10, '角色管理', 1, 'C', '/system/role', 'system/role-management', 'system:role:list', 'fas fa-user-tag', 2, '1', '1', '0', '1', NOW(), NOW(), '角色管理菜单'),
(11, '角色查询', 10, 'F', '', '', 'system:role:query', '', 1, '1', '1', '0', '1', NOW(), NOW(), ''),
(12, '角色新增', 10, 'F', '', '', 'system:role:add', '', 2, '1', '1', '0', '1', NOW(), NOW(), ''),
(13, '角色修改', 10, 'F', '', '', 'system:role:edit', '', 3, '1', '1', '0', '1', NOW(), NOW(), ''),
(14, '角色删除', 10, 'F', '', '', 'system:role:remove', '', 4, '1', '1', '0', '1', NOW(), NOW(), ''),
(15, '角色导出', 10, 'F', '', '', 'system:role:export', '', 5, '1', '1', '0', '1', NOW(), NOW(), ''),
(16, '分配权限', 10, 'F', '', '', 'system:role:authorize', '', 6, '1', '1', '0', '1', NOW(), NOW(), ''),

-- 菜单管理（二级菜单）
(17, '菜单管理', 1, 'C', '/system/menu', 'system/menu-management', 'system:menu:list', 'fas fa-sitemap', 3, '1', '1', '0', '1', NOW(), NOW(), '菜单管理菜单'),
(18, '菜单查询', 17, 'F', '', '', 'system:menu:query', '', 1, '1', '1', '0', '1', NOW(), NOW(), ''),
(19, '菜单新增', 17, 'F', '', '', 'system:menu:add', '', 2, '1', '1', '0', '1', NOW(), NOW(), ''),
(20, '菜单修改', 17, 'F', '', '', 'system:menu:edit', '', 3, '1', '1', '0', '1', NOW(), NOW(), ''),
(21, '菜单删除', 17, 'F', '', '', 'system:menu:remove', '', 4, '1', '1', '0', '1', NOW(), NOW(), ''),

-- 业务菜单（一级菜单）
(100, '业务管理', 0, 'M', '/business', NULL, NULL, 'fas fa-briefcase', 2, '1', '1', '0', '1', NOW(), NOW(), '业务管理目录'),
(101, '案池管理', 100, 'C', '/case-pool', 'case-pool', 'business:case:list', 'fas fa-folder-open', 1, '1', '1', '0', '1', NOW(), NOW(), '案池管理菜单'),
(102, '还款管理', 100, 'C', '/repayment', 'repayment', 'business:repayment:list', 'fas fa-money-bill-wave', 2, '1', '1', '0', '1', NOW(), NOW(), '还款管理菜单'),
(103, '作业清单', 100, 'C', '/tasklist', 'tasklist', 'business:task:list', 'fas fa-tasks', 3, '1', '1', '0', '1', NOW(), NOW(), '作业清单菜单'),
(104, '换单查询', 100, 'C', '/exchange', 'exchange', 'business:exchange:list', 'fas fa-exchange-alt', 4, '1', '1', '0', '1', NOW(), NOW(), '换单查询菜单'),
(105, '统计分析', 100, 'C', '/statistics', 'statistics', 'business:statistics:view', 'fas fa-chart-bar', 5, '1', '1', '0', '1', NOW(), NOW(), '统计分析菜单');

-- 插入系统角色数据
INSERT INTO sys_role (id, role_name, role_code, description, status, sort, create_time, update_time, remark) VALUES
(1, '超级管理员', 'ROLE_ADMIN', '超级管理员，拥有所有权限', '1', 1, NOW(), NOW(), '超级管理员角色'),
(2, '系统管理员', 'ROLE_SYSTEM', '系统管理员，拥有系统管理权限', '1', 2, NOW(), NOW(), '系统管理员角色'),
(3, '业务管理员', 'ROLE_BUSINESS', '业务管理员，拥有业务管理权限', '1', 3, NOW(), NOW(), '业务管理员角色'),
(4, '普通用户', 'ROLE_USER', '普通用户，拥有基本查看权限', '1', 4, NOW(), NOW(), '普通用户角色');

-- 插入系统用户数据
INSERT INTO sys_user (id, username, password, real_name, phone, email, status, create_time, update_time, remark) VALUES
(1, 'admin', '$2a$10$7JB720yubVSOfvVWbGReyO.ZhMh6vcVqh4OcHrd1GXPY5klE/YLW.', '系统管理员', '***********', '<EMAIL>', '1', NOW(), NOW(), '系统管理员账户'),
(2, 'system', '$2a$10$7JB720yubVSOfvVWbGReyO.ZhMh6vcVqh4OcHrd1GXPY5klE/YLW.', '系统用户', '***********', '<EMAIL>', '1', NOW(), NOW(), '系统用户账户'),
(3, 'business', '$2a$10$7JB720yubVSOfvVWbGReyO.ZhMh6vcVqh4OcHrd1GXPY5klE/YLW.', '业务用户', '***********', '<EMAIL>', '1', NOW(), NOW(), '业务用户账户'),
(4, 'user', '$2a$10$7JB720yubVSOfvVWbGReyO.ZhMh6vcVqh4OcHrd1GXPY5klE/YLW.', '普通用户', '***********', '<EMAIL>', '1', NOW(), NOW(), '普通用户账户');

-- 分配用户角色关系
INSERT INTO sys_user_role (user_id, role_id, create_time) VALUES
(1, 1, NOW()), -- admin用户分配超级管理员角色
(2, 2, NOW()), -- system用户分配系统管理员角色
(3, 3, NOW()), -- business用户分配业务管理员角色
(4, 4, NOW()); -- user用户分配普通用户角色

-- 分配角色菜单权限
-- 超级管理员拥有所有权限
INSERT INTO sys_role_menu (role_id, menu_id, create_time)
SELECT 1, id, NOW() FROM sys_menu;

-- 系统管理员拥有系统管理权限
INSERT INTO sys_role_menu (role_id, menu_id, create_time) VALUES
(2, 1, NOW()),   -- 系统管理目录
(2, 2, NOW()),   -- 用户管理
(2, 3, NOW()),   -- 用户查询
(2, 4, NOW()),   -- 用户新增
(2, 5, NOW()),   -- 用户修改
(2, 6, NOW()),   -- 用户删除
(2, 7, NOW()),   -- 用户导出
(2, 8, NOW()),   -- 用户导入
(2, 9, NOW()),   -- 重置密码
(2, 10, NOW()),  -- 角色管理
(2, 11, NOW()),  -- 角色查询
(2, 12, NOW()),  -- 角色新增
(2, 13, NOW()),  -- 角色修改
(2, 14, NOW()),  -- 角色删除
(2, 15, NOW()),  -- 角色导出
(2, 16, NOW()),  -- 分配权限
(2, 17, NOW()),  -- 菜单管理
(2, 18, NOW()),  -- 菜单查询
(2, 19, NOW()),  -- 菜单新增
(2, 20, NOW()),  -- 菜单修改
(2, 21, NOW());  -- 菜单删除

-- 业务管理员拥有业务管理权限
INSERT INTO sys_role_menu (role_id, menu_id, create_time) VALUES
(3, 100, NOW()), -- 业务管理目录
(3, 101, NOW()), -- 案池管理
(3, 102, NOW()), -- 还款管理
(3, 103, NOW()), -- 作业清单
(3, 104, NOW()), -- 换单查询
(3, 105, NOW()); -- 统计分析

-- 普通用户拥有基本查看权限
INSERT INTO sys_role_menu (role_id, menu_id, create_time) VALUES
(4, 100, NOW()), -- 业务管理目录
(4, 101, NOW()), -- 案池管理（仅查看）
(4, 102, NOW()), -- 还款管理（仅查看）
(4, 103, NOW()), -- 作业清单（仅查看）
(4, 104, NOW()), -- 换单查询（仅查看）
(4, 105, NOW()); -- 统计分析（仅查看）

-- 提交事务
COMMIT;

-- 查询验证数据
SELECT '用户数据' as table_name, COUNT(*) as count FROM sys_user
UNION ALL
SELECT '角色数据', COUNT(*) FROM sys_role
UNION ALL
SELECT '菜单数据', COUNT(*) FROM sys_menu
UNION ALL
SELECT '用户角色关系', COUNT(*) FROM sys_user_role
UNION ALL
SELECT '角色菜单关系', COUNT(*) FROM sys_role_menu;

-- 默认密码说明
-- 所有用户的默认密码都是: 123456
-- 建议首次登录后立即修改密码