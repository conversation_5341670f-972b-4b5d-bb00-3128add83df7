<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cf.financing.mapper.SysMenuMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.cf.financing.entity.SysMenu">
        <id column="id" property="id" />
        <result column="parent_id" property="parentId" />
        <result column="menu_name" property="menuName" />
        <result column="menu_code" property="menuCode" />
        <result column="menu_type" property="menuType" />
        <result column="path" property="path" />
        <result column="component" property="component" />
        <result column="permission" property="permission" />
        <result column="icon" property="icon" />
        <result column="sort_order" property="sortOrder" />
        <result column="visible" property="visible" />
        <result column="status" property="status" />
        <result column="is_frame" property="isFrame" />
        <result column="is_cache" property="isCache" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="deleted" property="deleted" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 扩展结果映射（包含父菜单信息） -->
    <resultMap id="ExtendedResultMap" type="com.cf.financing.entity.SysMenu" extends="BaseResultMap">
        <result column="parent_name" property="parentName" />
        <result column="create_by_name" property="createByName" />
        <result column="update_by_name" property="updateByName" />
    </resultMap>

    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        id, parent_id, menu_name, menu_code, menu_type, path, component, permission, icon, 
        sort_order, visible, status, is_frame, is_cache, create_by, create_time, 
        update_by, update_time, deleted, remark
    </sql>

    <!-- 分页查询菜单列表 -->
    <select id="selectMenuPage" resultMap="ExtendedResultMap">
        SELECT 
            m.id, m.parent_id, m.menu_name, m.menu_code, m.menu_type, m.path, m.component, 
            m.permission, m.icon, m.sort_order, m.visible, m.status, m.is_frame, m.is_cache,
            m.create_by, m.create_time, m.update_by, m.update_time, m.remark,
            pm.menu_name as parent_name,
            cu.real_name as create_by_name,
            uu.real_name as update_by_name
        FROM sys_menu m
        LEFT JOIN sys_menu pm ON m.parent_id = pm.id
        LEFT JOIN sys_user cu ON m.create_by = cu.id
        LEFT JOIN sys_user uu ON m.update_by = uu.id
        <where>
            m.deleted = 0
            <if test="menuName != null and menuName != ''">
                AND m.menu_name LIKE CONCAT('%', #{menuName}, '%')
            </if>
            <if test="menuCode != null and menuCode != ''">
                AND m.menu_code LIKE CONCAT('%', #{menuCode}, '%')
            </if>
            <if test="status != null">
                AND m.status = #{status}
            </if>
            <if test="menuType != null">
                AND m.menu_type = #{menuType}
            </if>
        </where>
        ORDER BY m.sort_order, m.create_time DESC
    </select>

    <!-- 查询所有菜单树形结构 -->
    <select id="selectMenuTree" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_menu
        <where>
            deleted = 0
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        ORDER BY sort_order
    </select>

    <!-- 根据用户ID查询菜单权限 -->
    <select id="selectMenusByUserId" resultMap="BaseResultMap">
        SELECT DISTINCT 
            m.id, m.parent_id, m.menu_name, m.menu_code, m.menu_type, 
            m.path, m.component, m.permission, m.icon, m.sort_order,
            m.visible, m.status, m.is_frame, m.is_cache, m.remark
        FROM sys_menu m
        INNER JOIN sys_role_menu rm ON m.id = rm.menu_id
        INNER JOIN sys_user_role ur ON rm.role_id = ur.role_id
        WHERE ur.user_id = #{userId} AND m.status = 1 AND m.deleted = 0
        ORDER BY m.sort_order
    </select>

    <!-- 根据角色ID查询菜单权限 -->
    <select id="selectMenusByRoleId" resultMap="BaseResultMap">
        SELECT 
            m.id, m.parent_id, m.menu_name, m.menu_code, m.menu_type, 
            m.path, m.component, m.permission, m.icon, m.sort_order,
            m.visible, m.status, m.is_frame, m.is_cache, m.remark
        FROM sys_menu m
        INNER JOIN sys_role_menu rm ON m.id = rm.menu_id
        WHERE rm.role_id = #{roleId} AND m.status = 1 AND m.deleted = 0
        ORDER BY m.sort_order
    </select>

    <!-- 查询用户菜单权限标识 -->
    <select id="selectPermissionsByUserId" resultType="java.lang.String">
        SELECT DISTINCT m.permission
        FROM sys_menu m
        INNER JOIN sys_role_menu rm ON m.id = rm.menu_id
        INNER JOIN sys_user_role ur ON rm.role_id = ur.role_id
        WHERE ur.user_id = #{userId} 
        AND m.status = 1 
        AND m.deleted = 0
        AND m.permission IS NOT NULL 
        AND m.permission != ''
    </select>

    <!-- 查询用户可访问的菜单树（用于前端导航） -->
    <select id="selectUserMenuTree" resultMap="BaseResultMap">
        SELECT DISTINCT 
            m.id, m.parent_id, m.menu_name, m.menu_code, m.menu_type, 
            m.path, m.component, m.permission, m.icon, m.sort_order,
            m.visible, m.status, m.is_frame, m.is_cache, m.remark
        FROM sys_menu m
        INNER JOIN sys_role_menu rm ON m.id = rm.menu_id
        INNER JOIN sys_user_role ur ON rm.role_id = ur.role_id
        WHERE ur.user_id = #{userId} 
        AND m.status = 1 
        AND m.visible = 1 
        AND m.deleted = 0
        AND m.menu_type IN (1, 2)  -- 只查询目录和菜单，不包括按钮
        ORDER BY m.sort_order
    </select>

    <!-- 检查菜单编码是否存在 -->
    <select id="checkMenuCodeExists" resultType="int">
        SELECT COUNT(*) FROM sys_menu 
        WHERE menu_code = #{menuCode} 
        AND deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 检查菜单名称是否存在 -->
    <select id="checkMenuNameExists" resultType="int">
        SELECT COUNT(*) FROM sys_menu 
        WHERE menu_name = #{menuName} 
        AND parent_id = #{parentId}
        AND deleted = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 查询子菜单数量 -->
    <select id="countChildrenByParentId" resultType="int">
        SELECT COUNT(*) FROM sys_menu 
        WHERE parent_id = #{parentId} AND deleted = 0
    </select>

    <!-- 查询菜单的所有子菜单ID -->
    <select id="selectChildrenIdsByParentId" resultType="java.lang.Long">
        SELECT id FROM sys_menu WHERE parent_id = #{parentId} AND deleted = 0
    </select>

</mapper>
