<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>角色管理 - CF系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        /* 筛选框样式 */
        .filter-box {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .filter-title {
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            color: white;
            padding: 15px 20px;
            font-weight: 600;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .filter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            padding: 20px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-label {
            font-weight: 500;
            color: #374151;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .filter-control {
            padding: 10px 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: white;
        }

        .filter-control:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .filter-btn-group {
            padding: 0 20px 20px;
            display: flex;
            gap: 10px;
        }

        .filter-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-btn.search {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
        }

        .filter-btn.search:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
        }

        .filter-btn.reset {
            background: linear-gradient(135deg, #6b7280, #4b5563);
            color: white;
        }

        .filter-btn.reset:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(107, 114, 128, 0.3);
        }

        /* 功能框样式 */
        .function-box {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }

        .stats-info {
            display: flex;
            gap: 30px;
            flex-wrap: wrap;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #374151;
            font-size: 14px;
        }

        .stat-item i {
            color: #4f46e5;
            font-size: 16px;
        }

        .function-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .function-btn {
            padding: 10px 16px;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .function-btn.primary {
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            color: white;
        }

        .function-btn.primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
        }

        .function-btn.danger {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
        }

        .function-btn.danger:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
        }

        .function-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: none !important;
        }

        .function-btn {
            background: linear-gradient(135deg, #6b7280, #4b5563);
            color: white;
        }

        .function-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(107, 114, 128, 0.3);
        }

        /* 数据表格样式 */
        .data-table {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .data-header {
            background: linear-gradient(135deg, #1f2937, #111827);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .data-title {
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .data-actions {
            display: flex;
            gap: 10px;
        }

        .action-btn {
            padding: 8px 16px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
        }

        .action-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }

        .table-container {
            max-height: 600px;
            overflow-y: auto;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table thead {
            background: #f8fafc;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table th {
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
            color: #374151;
            border-bottom: 2px solid #e5e7eb;
            font-size: 14px;
        }

        .table td {
            padding: 12px;
            border-bottom: 1px solid #f3f4f6;
            color: #374151;
            font-size: 14px;
        }

        .table tbody tr {
            transition: all 0.2s ease;
        }

        .table tbody tr:hover {
            background: #f8fafc;
        }

        .checkbox {
            width: 16px;
            height: 16px;
            accent-color: #4f46e5;
        }

        .status-tag {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
        }

        .status-active {
            background: #dcfce7;
            color: #166534;
        }

        .status-inactive {
            background: #fee2e2;
            color: #991b1b;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .action-buttons button {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .btn-edit {
            background: #dbeafe;
            color: #1d4ed8;
        }

        .btn-edit:hover {
            background: #bfdbfe;
            transform: translateY(-1px);
        }

        .btn-permission {
            background: #fef3c7;
            color: #d97706;
        }

        .btn-permission:hover {
            background: #fde68a;
            transform: translateY(-1px);
        }

        .btn-delete {
            background: #fee2e2;
            color: #dc2626;
        }

        .btn-delete:hover {
            background: #fecaca;
            transform: translateY(-1px);
        }

        /* 分页样式 */
        .pagination {
            padding: 20px;
            background: #f8fafc;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: 1px solid #e5e7eb;
        }

        .pagination-info {
            color: #6b7280;
            font-size: 14px;
        }

        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .pagination-btn {
            padding: 8px 16px;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
            color: #374151;
        }

        .pagination-btn:hover:not(:disabled) {
            background: #f3f4f6;
            border-color: #9ca3af;
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .page-numbers {
            display: flex;
            gap: 4px;
        }

        .page-number {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
            color: #374151;
        }

        .page-number:hover {
            background: #f3f4f6;
        }

        .page-number.active {
            background: #4f46e5;
            color: white;
            border-color: #4f46e5;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            max-width: 800px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            animation: modalSlideIn 0.3s ease;
        }

        .modal-content.modal-sm {
            max-width: 500px;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal-header {
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 5px;
            border-radius: 4px;
            transition: background 0.2s ease;
        }

        .modal-close:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .modal-body {
            padding: 30px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-label {
            font-weight: 500;
            color: #374151;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .form-label.required::after {
            content: ' *';
            color: #ef4444;
        }

        .form-control {
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: white;
        }

        .form-control:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .modal-footer {
            padding: 20px 30px;
            background: #f8fafc;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            border-top: 1px solid #e5e7eb;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4f46e5, #7c3aed);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
        }

        /* 权限树样式 */
        .permission-tree {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            background: #f9fafb;
        }

        .tree-node {
            margin-bottom: 8px;
        }

        .tree-item {
            display: flex;
            align-items: center;
            padding: 6px 0;
            cursor: pointer;
            border-radius: 4px;
            transition: background 0.2s ease;
        }

        .tree-item:hover {
            background: rgba(79, 70, 229, 0.05);
        }

        .tree-checkbox {
            margin-right: 8px;
            width: 16px;
            height: 16px;
            accent-color: #4f46e5;
        }

        .tree-label {
            font-size: 14px;
            color: #374151;
            user-select: none;
        }

        .tree-children {
            margin-left: 24px;
            border-left: 1px dashed #d1d5db;
            padding-left: 12px;
        }

        .user-info {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .info-text {
            color: #0369a1;
            font-size: 14px;
            margin: 0;
        }

        .info-value {
            font-weight: 600;
            color: #1e40af;
        }

        @media (max-width: 768px) {
            .filter-grid {
                grid-template-columns: 1fr;
            }
            
            .function-box {
                flex-direction: column;
                align-items: stretch;
            }
            
            .stats-info {
                justify-content: center;
            }
            
            .function-buttons {
                justify-content: center;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .pagination {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- 主容器 -->
    <div class="container">
        <!-- 筛选框 -->
        <div class="filter-box">
            <div class="filter-title">
                <i class="fas fa-filter"></i>
                角色管理 - 筛选条件
            </div>
            <div class="filter-grid">
                <div class="filter-group">
                    <label class="filter-label">角色名称:</label>
                    <input type="text" id="searchRoleName" placeholder="请输入角色名称" class="filter-control">
                </div>
                <div class="filter-group">
                    <label class="filter-label">角色编码:</label>
                    <input type="text" id="searchRoleCode" placeholder="请输入角色编码" class="filter-control">
                </div>
                <div class="filter-group">
                    <label class="filter-label">状态:</label>
                    <select id="searchStatus" class="filter-control">
                        <option value="">全部状态</option>
                        <option value="1">启用</option>
                        <option value="0">禁用</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">创建时间:</label>
                    <input type="date" id="searchCreateTime" class="filter-control">
                </div>
            </div>
            <div class="filter-btn-group">
                <button onclick="searchRoles()" class="filter-btn search">
                    <i class="fas fa-search"></i>搜索
                </button>
                <button onclick="resetSearch()" class="filter-btn reset">
                    <i class="fas fa-refresh"></i>重置
                </button>
            </div>
        </div>

        <!-- 功能框 -->
        <div class="function-box">
            <div class="stats-info">
                <div class="stat-item">
                    <i class="fas fa-user-tag"></i>
                    <span>总角色数: <strong id="totalRoles">0</strong></span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-check-circle"></i>
                    <span>启用角色: <strong id="activeRoles">0</strong></span>
                </div>
                <div class="stat-item">
                    <i class="fas fa-times-circle"></i>
                    <span>禁用角色: <strong id="inactiveRoles">0</strong></span>
                </div>
            </div>
            <div class="function-buttons">
                <button onclick="openAddRoleModal()" class="function-btn primary">
                    <i class="fas fa-plus"></i>新增角色
                </button>
                <button onclick="batchDeleteRoles()" class="function-btn danger" disabled id="batchDeleteBtn">
                    <i class="fas fa-trash"></i>批量删除
                </button>
                <button onclick="exportRoles()" class="function-btn">
                    <i class="fas fa-download"></i>导出
                </button>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="data-table">
            <div class="data-header">
                <div class="data-title">
                    <i class="fas fa-user-tag"></i>
                    角色列表
                </div>
                <div class="data-actions">
                    <button onclick="refreshData()" class="action-btn">
                        <i class="fas fa-sync-alt"></i>刷新
                    </button>
                </div>
            </div>
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th width="50">
                                <input type="checkbox" id="selectAll" onchange="toggleSelectAll()" class="checkbox">
                            </th>
                            <th width="150">角色名称</th>
                            <th width="120">角色编码</th>
                            <th width="200">描述</th>
                            <th width="80">状态</th>
                            <th width="150">创建时间</th>
                            <th width="200">操作</th>
                        </tr>
                    </thead>
                    <tbody id="roleTableBody">
                        <!-- 角色数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div class="pagination">
                <div class="pagination-info">
                    显示第 <span id="startRecord">1</span> 到 <span id="endRecord">10</span> 条，
                    共 <span id="totalRecords">0</span> 条记录
                </div>
                <div class="pagination-controls">
                    <button onclick="previousPage()" class="pagination-btn" id="prevBtn">
                        <i class="fas fa-chevron-left"></i>上一页
                    </button>
                    <div id="pageNumbers" class="page-numbers">
                        <!-- 页码将通过JavaScript动态生成 -->
                    </div>
                    <button onclick="nextPage()" class="pagination-btn" id="nextBtn">
                        下一页<i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增角色模态框 -->
    <div id="addRoleModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-user-plus"></i>
                    新增角色
                </h3>
                <button onclick="closeAddRoleModal()" class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="addRoleForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label required">角色名称:</label>
                            <input type="text" id="addRoleName" name="roleName" required class="form-control" placeholder="请输入角色名称">
                        </div>
                        <div class="form-group">
                            <label class="form-label required">角色编码:</label>
                            <input type="text" id="addRoleCode" name="roleCode" required class="form-control" placeholder="请输入角色编码">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">排序:</label>
                            <input type="number" id="addSort" name="sort" class="form-control" placeholder="请输入排序号" value="0">
                        </div>
                        <div class="form-group">
                            <label class="form-label">状态:</label>
                            <select id="addStatus" name="status" class="form-control">
                                <option value="1">启用</option>
                                <option value="0">禁用</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group full-width">
                            <label class="form-label">描述:</label>
                            <textarea id="addDescription" name="description" rows="3" class="form-control" placeholder="请输入角色描述"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" onclick="closeAddRoleModal()" class="btn btn-secondary">
                    <i class="fas fa-times"></i>取消
                </button>
                <button type="submit" form="addRoleForm" class="btn btn-primary">
                    <i class="fas fa-save"></i>保存
                </button>
            </div>
        </div>
    </div>

    <!-- 编辑角色模态框 -->
    <div id="roleModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle" class="modal-title">
                    <i class="fas fa-user-edit"></i>
                    编辑角色
                </h3>
                <button onclick="closeRoleModal()" class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="roleForm">
                    <input type="hidden" id="roleId">
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label required">角色名称:</label>
                            <input type="text" id="roleName" required class="form-control" placeholder="请输入角色名称">
                        </div>
                        <div class="form-group">
                            <label class="form-label required">角色编码:</label>
                            <input type="text" id="roleCode" required class="form-control" placeholder="请输入角色编码">
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">排序:</label>
                            <input type="number" id="sort" class="form-control" placeholder="请输入排序号">
                        </div>
                        <div class="form-group">
                            <label class="form-label">状态:</label>
                            <select id="status" class="form-control">
                                <option value="1">启用</option>
                                <option value="0">禁用</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group full-width">
                            <label class="form-label">描述:</label>
                            <textarea id="description" rows="3" class="form-control" placeholder="请输入角色描述"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" onclick="closeRoleModal()" class="btn btn-secondary">
                    <i class="fas fa-times"></i>取消
                </button>
                <button type="button" onclick="saveRole()" class="btn btn-primary">
                    <i class="fas fa-save"></i>保存
                </button>
            </div>
        </div>
    </div>

    <!-- 权限分配模态框 -->
    <div id="permissionModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-key"></i>
                    分配权限
                </h3>
                <button onclick="closePermissionModal()" class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="permissionRoleId">
                <div class="user-info">
                    <p class="info-text">角色：<span id="permissionRoleName" class="info-value"></span></p>
                </div>
                <div class="permission-tree" id="permissionTree">
                    <!-- 权限树将动态加载 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" onclick="closePermissionModal()" class="btn btn-secondary">
                    <i class="fas fa-times"></i>取消
                </button>
                <button type="button" onclick="saveRolePermissions()" class="btn btn-primary">
                    <i class="fas fa-save"></i>保存
                </button>
            </div>
        </div>
    </div>

    <script th:src="@{/js/role-management.js}"></script>
</body>
</html>