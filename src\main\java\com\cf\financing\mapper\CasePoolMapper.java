package com.cf.financing.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cf.financing.entity.CasePool;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 案池管理Mapper接口
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Mapper
public interface CasePoolMapper extends BaseMapper<CasePool> {

    /**
     * 分页查询案池数据
     *
     * @param page 分页参数
     * @param params 查询参数
     * @return 分页结果
     */
    IPage<CasePool> selectCasePoolPage(Page<CasePool> page, @Param("params") Map<String, Object> params);

    /**
     * 统计案池总数
     *
     * @param params 查询参数
     * @return 总数
     */
    Long countCasePool(@Param("params") Map<String, Object> params);

    /**
     * 统计委托总金额
     *
     * @param params 查询参数
     * @return 总金额
     */
    BigDecimal sumEntrustedAmount(@Param("params") Map<String, Object> params);

    /**
     * 批量锁定案件
     *
     * @param clientIds 客户索引号列表
     * @return 影响行数
     */
    int batchLockCases(@Param("clientIds") List<String> clientIds);

    /**
     * 批量解锁案件
     *
     * @param clientIds 客户索引号列表
     * @return 影响行数
     */
    int batchUnlockCases(@Param("clientIds") List<String> clientIds);

    /**
     * 批量分派案件
     *
     * @param clientIds 客户索引号列表
     * @param assignUserId 分派用户ID
     * @return 影响行数
     */
    int batchAssignCases(@Param("clientIds") List<String> clientIds, @Param("assignUserId") Long assignUserId);

    /**
     * 根据客户索引号查询案件
     *
     * @param clientId 客户索引号
     * @return 案件信息
     */
    CasePool selectByClientId(@Param("clientId") String clientId);

    /**
     * 获取案池统计数据
     *
     * @return 统计数据
     */
    Map<String, Object> getCasePoolStatistics();

    /**
     * 根据配置状态统计案件数量
     *
     * @return 统计结果
     */
    List<Map<String, Object>> countByConfigStatus();

    /**
     * 根据案件类型统计案件数量
     *
     * @return 统计结果
     */
    List<Map<String, Object>> countByCaseType();

    /**
     * 根据城市统计案件数量
     *
     * @return 统计结果
     */
    List<Map<String, Object>> countByCity();
}
