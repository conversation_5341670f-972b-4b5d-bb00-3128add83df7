package com.cf.financing.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 角色菜单关联实体类
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_role_menu")
public class SysRoleMenu implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 角色ID
     */
    @TableField("role_id")
    private Long roleId;

    /**
     * 菜单ID
     */
    @TableField("menu_id")
    private Long menuId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private Long createBy;

    // 非数据库字段
    /**
     * 角色名称
     */
    @TableField(exist = false)
    private String roleName;

    /**
     * 角色编码
     */
    @TableField(exist = false)
    private String roleCode;

    /**
     * 菜单名称
     */
    @TableField(exist = false)
    private String menuName;

    /**
     * 菜单编码
     */
    @TableField(exist = false)
    private String menuCode;

    /**
     * 权限标识
     */
    @TableField(exist = false)
    private String permission;
}