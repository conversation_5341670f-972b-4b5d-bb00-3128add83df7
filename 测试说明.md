# CF金融催收管理系统 - 登录功能和权限查询测试说明

## 功能实现概述

本次实现了以下两个主要功能：

### 1. 登录功能改进
- 修改登录系统从 `sys_user` 表查询用户信息
- 登录成功后在dashboard页面显示用户的真实姓名
- 完善了用户角色权限管理

### 2. 超级管理员权限查询功能
- 实现了根据当前用户查询超级管理角色显示所有权限的功能
- 创建了权限测试页面用于验证功能

## 测试账号

系统已预置以下测试账号（密码均为：123456）：

| 用户名 | 密码 | 真实姓名 | 角色 | 说明 |
|--------|------|----------|------|------|
| admin | 123456 | 系统管理员 | 超级管理员 | 拥有所有权限 |
| manager | 123456 | 催收主管 | 催收主管 | 催收部门管理权限 |
| collector1 | 123456 | 催收员1 | 催收员 | 基础催收权限 |

## 测试步骤

### 第一步：测试登录功能

1. 访问系统首页：http://localhost:8080
2. 系统会自动跳转到登录页面
3. 使用测试账号登录：
   - 用户名：admin
   - 密码：123456
   - 验证码：按页面显示输入
4. 登录成功后，检查dashboard页面右上角是否显示"系统管理员"（真实姓名）

### 第二步：测试权限查询功能

1. 登录成功后，访问权限测试页面：http://localhost:8080/permission-test
2. 页面会显示当前用户信息
3. 点击"查询当前用户权限"按钮，查看当前用户的权限列表
4. 点击"查询超级管理员权限"按钮，查看超级管理员角色的所有权限
5. 点击"检查特定权限"按钮，可以测试是否拥有特定权限（如：system:user:list）

### 第三步：验证权限控制

1. 使用admin账号登录，应该能看到所有权限
2. 使用其他账号登录，权限应该受到限制
3. 在权限测试页面验证不同用户的权限差异

## 预期结果

### 登录功能
- ✅ 能够使用数据库中的用户账号登录
- ✅ 登录成功后dashboard页面显示用户真实姓名
- ✅ 密码验证正确（BCrypt加密）

### 权限查询功能
- ✅ admin用户能够查询到超级管理员的所有权限
- ✅ 权限以树形结构展示
- ✅ 能够检查特定权限是否存在
- ✅ 非管理员用户访问管理员权限查询时会被拒绝

## 技术实现要点

### 1. 数据库结构
- `sys_user`: 用户基本信息表
- `sys_role`: 角色信息表
- `sys_menu`: 菜单权限表
- `sys_user_role`: 用户角色关联表
- `sys_role_menu`: 角色菜单关联表

### 2. 核心组件
- `CustomUserDetailsService`: 自定义用户详情服务，从数据库加载用户和权限
- `PermissionController`: 权限查询API控制器
- `WebController`: 页面路由控制器，添加用户信息到模型

### 3. 安全配置
- Spring Security配置，支持基于角色的访问控制
- BCrypt密码加密
- 会话管理

## 故障排除

### 如果登录失败
1. 检查数据库连接是否正常
2. 确认用户表中有测试数据
3. 检查密码是否正确（123456）

### 如果权限查询失败
1. 确认用户已登录
2. 检查是否使用admin账号访问管理员权限
3. 查看浏览器控制台是否有错误信息

### 如果页面无法访问
1. 确认应用已启动（端口8080）
2. 检查防火墙设置
3. 查看应用日志是否有错误

## API接口

### 权限查询相关API
- `GET /api/permission/current` - 获取当前用户权限
- `GET /api/permission/admin` - 获取超级管理员权限
- `GET /api/permission/check?permission=xxx` - 检查特定权限

### 页面路由
- `/login` - 登录页面
- `/dashboard` - 仪表板页面
- `/permission-test` - 权限测试页面

## 注意事项

1. 首次运行需要确保数据库已创建并执行了初始化脚本
2. 测试时建议使用Chrome或Firefox浏览器
3. 如需修改测试数据，请参考 `src/main/resources/sql/init.sql` 文件
4. 生产环境部署时请修改默认密码和数据库配置

## 下一步扩展

1. 可以添加更多的权限控制粒度
2. 实现动态菜单加载
3. 添加用户管理界面
4. 完善角色权限分配功能
