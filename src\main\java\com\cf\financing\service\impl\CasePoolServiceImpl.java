package com.cf.financing.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cf.financing.entity.CasePool;
import com.cf.financing.mapper.CasePoolMapper;
import com.cf.financing.service.ICasePoolService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 案池管理Service实现类
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Slf4j
@Service
public class CasePoolServiceImpl extends ServiceImpl<CasePoolMapper, CasePool> implements ICasePoolService {

    @Autowired
    private CasePoolMapper casePoolMapper;

    @Override
    public IPage<CasePool> getCasePoolPage(Long current, Long size, Map<String, Object> params) {
        Page<CasePool> page = new Page<>(current, size);
        return casePoolMapper.selectCasePoolPage(page, params);
    }

    @Override
    public Map<String, Object> getCasePoolStatistics(Map<String, Object> params) {
        Map<String, Object> result = new HashMap<>();
        
        // 统计总户数
        Long totalAccounts = casePoolMapper.countCasePool(params);
        result.put("totalAccounts", totalAccounts);
        
        // 统计总委托金额
        BigDecimal totalAmount = casePoolMapper.sumEntrustedAmount(params);
        result.put("totalAmount", totalAmount != null ? totalAmount : BigDecimal.ZERO);
        
        // 统计各状态案件数量
        List<Map<String, Object>> statusStats = casePoolMapper.countByConfigStatus();
        result.put("statusStats", statusStats);
        
        // 统计各类型案件数量
        List<Map<String, Object>> typeStats = casePoolMapper.countByCaseType();
        result.put("typeStats", typeStats);
        
        // 统计各城市案件数量
        List<Map<String, Object>> cityStats = casePoolMapper.countByCity();
        result.put("cityStats", cityStats);
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchLockCases(List<String> clientIds) {
        if (clientIds == null || clientIds.isEmpty()) {
            return false;
        }
        int result = casePoolMapper.batchLockCases(clientIds);
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUnlockCases(List<String> clientIds) {
        if (clientIds == null || clientIds.isEmpty()) {
            return false;
        }
        int result = casePoolMapper.batchUnlockCases(clientIds);
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchAssignCases(List<String> clientIds, Long assignUserId) {
        if (clientIds == null || clientIds.isEmpty() || assignUserId == null) {
            return false;
        }
        int result = casePoolMapper.batchAssignCases(clientIds, assignUserId);
        return result > 0;
    }

    @Override
    public CasePool getCaseByClientId(String clientId) {
        return casePoolMapper.selectByClientId(clientId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> importCasePoolData(List<CasePool> casePoolList) {
        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failCount = 0;
        
        for (CasePool casePool : casePoolList) {
            try {
                // 检查是否已存在
                CasePool existing = casePoolMapper.selectByClientId(casePool.getClientId());
                if (existing != null) {
                    // 更新现有记录
                    casePool.setId(existing.getId());
                    casePoolMapper.updateById(casePool);
                } else {
                    // 插入新记录
                    casePoolMapper.insert(casePool);
                }
                successCount++;
            } catch (Exception e) {
                failCount++;
                // 记录错误日志
                log.error("导入案池数据失败，客户索引号：{}, 错误信息：{}", casePool.getClientId(), e.getMessage());
            }
        }
        
        result.put("successCount", successCount);
        result.put("failCount", failCount);
        result.put("totalCount", casePoolList.size());
        
        return result;
    }

    @Override
    public List<CasePool> exportCasePoolData(Map<String, Object> params) {
        QueryWrapper<CasePool> queryWrapper = buildQueryWrapper(params);
        return casePoolMapper.selectList(queryWrapper);
    }

    @Override
    public Map<String, Object> getCasePoolChartData() {
        Map<String, Object> result = new HashMap<>();
        
        // 获取基础统计数据
        Map<String, Object> statistics = casePoolMapper.getCasePoolStatistics();
        result.put("statistics", statistics);
        
        // 获取状态分布数据
        List<Map<String, Object>> statusData = casePoolMapper.countByConfigStatus();
        result.put("statusData", statusData);
        
        // 获取类型分布数据
        List<Map<String, Object>> typeData = casePoolMapper.countByCaseType();
        result.put("typeData", typeData);
        
        // 获取城市分布数据
        List<Map<String, Object>> cityData = casePoolMapper.countByCity();
        result.put("cityData", cityData);
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateFollowupStatus(String clientId, String followupResult) {
        CasePool casePool = casePoolMapper.selectByClientId(clientId);
        if (casePool != null) {
            casePool.setLastFollowupDate(LocalDate.now());
            casePool.setUnfollowedDays(0);
            // 可以根据需要添加更多跟进相关字段的更新
            return casePoolMapper.updateById(casePool) > 0;
        }
        return false;
    }

    @Override
    public Map<String, Object> getCaseDetailInfo(String clientId) {
        Map<String, Object> result = new HashMap<>();
        
        // 获取案件基本信息
        CasePool casePool = casePoolMapper.selectByClientId(clientId);
        result.put("caseInfo", casePool);
        
        // 可以添加更多关联信息，如联系记录、还款记录等
        // TODO: 添加关联数据查询
        
        return result;
    }

    /**
     * 构建查询条件
     */
    private QueryWrapper<CasePool> buildQueryWrapper(Map<String, Object> params) {
        QueryWrapper<CasePool> queryWrapper = new QueryWrapper<>();
        
        if (params != null) {
            // 客户姓名
            if (params.containsKey("clientName") && params.get("clientName") != null) {
                queryWrapper.like("client_name", params.get("clientName"));
            }
            
            // 客户索引号
            if (params.containsKey("clientId") && params.get("clientId") != null) {
                queryWrapper.like("client_id", params.get("clientId"));
            }
            
            // 城市
            if (params.containsKey("city") && params.get("city") != null) {
                queryWrapper.like("city", params.get("city"));
            }
            
            // 配置状态
            if (params.containsKey("configStatus") && params.get("configStatus") != null) {
                queryWrapper.eq("config_status", params.get("configStatus"));
            }
            
            // 案件类型
            if (params.containsKey("caseType") && params.get("caseType") != null) {
                queryWrapper.like("case_type", params.get("caseType"));
            }
            
            // 批次号
            if (params.containsKey("batchNumber") && params.get("batchNumber") != null) {
                queryWrapper.like("batch_number", params.get("batchNumber"));
            }
            
            // 持卡人代码
            if (params.containsKey("cardholderCode") && params.get("cardholderCode") != null) {
                queryWrapper.like("cardholder_code", params.get("cardholderCode"));
            }
            
            // 委托开始日期范围
            if (params.containsKey("startDateBegin") && params.get("startDateBegin") != null) {
                queryWrapper.ge("entrust_start_date", params.get("startDateBegin"));
            }
            if (params.containsKey("startDateEnd") && params.get("startDateEnd") != null) {
                queryWrapper.le("entrust_start_date", params.get("startDateEnd"));
            }
            
            // 委托结束日期范围
            if (params.containsKey("endDateBegin") && params.get("endDateBegin") != null) {
                queryWrapper.ge("entrust_end_date", params.get("endDateBegin"));
            }
            if (params.containsKey("endDateEnd") && params.get("endDateEnd") != null) {
                queryWrapper.le("entrust_end_date", params.get("endDateEnd"));
            }
        }
        
        return queryWrapper;
    }
}
