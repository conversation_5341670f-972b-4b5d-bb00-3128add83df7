package com.cf.financing.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cf.financing.entity.RepaymentRecord;
import com.cf.financing.service.IRepaymentRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 还款管理控制器
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/api/repayment")
public class RepaymentController {

    @Autowired
    private IRepaymentRecordService repaymentRecordService;

    /**
     * 分页查询还款记录
     */
    @GetMapping("/page")
    public Map<String, Object> getRepaymentPage(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "20") Long size,
            @RequestParam(required = false) String clientName,
            @RequestParam(required = false) String clientId,
            @RequestParam(required = false) String repaymentNo,
            @RequestParam(required = false) String repaymentStatus,
            @RequestParam(required = false) String repaymentType,
            @RequestParam(required = false) String auditStatus,
            @RequestParam(required = false) String paymentType,
            @RequestParam(required = false) Long operatorId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) String minAmount,
            @RequestParam(required = false) String maxAmount) {
        
        Map<String, Object> params = new HashMap<>();
        params.put("clientName", clientName);
        params.put("clientId", clientId);
        params.put("repaymentNo", repaymentNo);
        params.put("repaymentStatus", repaymentStatus);
        params.put("repaymentType", repaymentType);
        params.put("auditStatus", auditStatus);
        params.put("paymentType", paymentType);
        params.put("operatorId", operatorId);
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        params.put("minAmount", minAmount);
        params.put("maxAmount", maxAmount);

        IPage<RepaymentRecord> page = repaymentRecordService.getRepaymentPage(current, size, params);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("data", page.getRecords());
        result.put("total", page.getTotal());
        result.put("current", page.getCurrent());
        result.put("size", page.getSize());
        result.put("pages", page.getPages());
        
        return result;
    }

    /**
     * 获取还款统计信息
     */
    @GetMapping("/statistics")
    public Map<String, Object> getRepaymentStatistics(
            @RequestParam(required = false) String clientName,
            @RequestParam(required = false) String repaymentStatus) {
        
        Map<String, Object> params = new HashMap<>();
        params.put("clientName", clientName);
        params.put("repaymentStatus", repaymentStatus);
        
        Map<String, Object> statistics = repaymentRecordService.getRepaymentStatistics(params);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("data", statistics);
        
        return result;
    }

    /**
     * 根据客户索引号查询还款记录
     */
    @GetMapping("/client/{clientId}")
    public Map<String, Object> getRepaymentsByClientId(@PathVariable String clientId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<RepaymentRecord> repayments = repaymentRecordService.getRepaymentsByClientId(clientId);
            result.put("success", true);
            result.put("data", repayments);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 批量审核还款记录
     */
    @PostMapping("/batch-audit")
    public Map<String, Object> batchAuditRepayment(@RequestBody Map<String, Object> requestData) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            @SuppressWarnings("unchecked")
            List<Long> ids = (List<Long>) requestData.get("ids");
            String auditStatus = requestData.get("auditStatus").toString();
            Long auditorId = Long.valueOf(requestData.get("auditorId").toString());
            String auditRemark = requestData.get("auditRemark") != null ? 
                                requestData.get("auditRemark").toString() : "";
            
            boolean success = repaymentRecordService.batchAuditRepayment(ids, auditStatus, auditorId, auditRemark);
            result.put("success", success);
            result.put("message", success ? "审核成功" : "审核失败");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "审核失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 新增还款记录
     */
    @PostMapping("/add")
    public Map<String, Object> addRepayment(@RequestBody RepaymentRecord repaymentRecord) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 生成还款编号
            if (repaymentRecord.getRepaymentNo() == null || repaymentRecord.getRepaymentNo().isEmpty()) {
                repaymentRecord.setRepaymentNo(repaymentRecordService.generateRepaymentNo());
            }
            
            boolean success = repaymentRecordService.save(repaymentRecord);
            result.put("success", success);
            result.put("message", success ? "新增成功" : "新增失败");
            if (success) {
                result.put("data", repaymentRecord);
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "新增失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 更新还款记录
     */
    @PutMapping("/update")
    public Map<String, Object> updateRepayment(@RequestBody RepaymentRecord repaymentRecord) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean success = repaymentRecordService.updateById(repaymentRecord);
            result.put("success", success);
            result.put("message", success ? "更新成功" : "更新失败");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "更新失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 删除还款记录
     */
    @DeleteMapping("/delete/{id}")
    public Map<String, Object> deleteRepayment(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean success = repaymentRecordService.removeById(id);
            result.put("success", success);
            result.put("message", success ? "删除成功" : "删除失败");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "删除失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取还款详情
     */
    @GetMapping("/detail/{id}")
    public Map<String, Object> getRepaymentDetail(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            RepaymentRecord repayment = repaymentRecordService.getById(id);
            result.put("success", true);
            result.put("data", repayment);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 导出还款数据
     */
    @GetMapping("/export")
    public Map<String, Object> exportRepaymentData(
            @RequestParam(required = false) String clientName,
            @RequestParam(required = false) String repaymentStatus) {
        
        Map<String, Object> params = new HashMap<>();
        params.put("clientName", clientName);
        params.put("repaymentStatus", repaymentStatus);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<RepaymentRecord> exportData = repaymentRecordService.exportRepaymentData(params);
            result.put("success", true);
            result.put("data", exportData);
            result.put("message", "导出成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "导出失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取图表数据
     */
    @GetMapping("/chart-data")
    public Map<String, Object> getRepaymentChartData() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Map<String, Object> chartData = repaymentRecordService.getRepaymentChartData();
            result.put("success", true);
            result.put("data", chartData);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取图表数据失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取客户还款汇总
     */
    @GetMapping("/summary/{clientId}")
    public Map<String, Object> getClientRepaymentSummary(@PathVariable String clientId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Map<String, Object> summary = repaymentRecordService.getClientRepaymentSummary(clientId);
            result.put("success", true);
            result.put("data", summary);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取汇总失败：" + e.getMessage());
        }
        
        return result;
    }
}
