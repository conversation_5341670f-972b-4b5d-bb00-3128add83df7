-- 插入默认部门
INSERT INTO sys_dept (id, parent_id, dept_name, dept_code, leader, sort_order) VALUES
(1, 0, '总公司', 'ROOT', '系统管理员', 1),
(2, 1, '催收部', 'COLLECTION', '催收主管', 2),
(3, 1, '风控部', 'RISK', '风控主管', 3),
(4, 1, '客服部', 'SERVICE', '客服主管', 4);

-- 插入默认角色
INSERT INTO sys_role (id, role_name, role_code, description, status, sort_order) VALUES
(1, '超级管理员', 'ADMIN', '系统超级管理员，拥有所有权限', 1, 1),
(2, '催收主管', 'COLLECTION_MANAGER', '催收部门主管，管理催收业务', 1, 2),
(3, '催收员', 'COLLECTOR', '催收员，执行催收任务', 1, 3),
(4, '客服', 'SERVICE', '客服人员，处理客户咨询', 1, 4);

-- 插入默认用户 (密码都是123456)
INSERT INTO sys_user (id, username, password, real_name, phone, email, dept_id, status) VALUES
(1, 'admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXISwrCZJ/6HpVfS2tVZHKHHbdC', '系统管理员', '13800138000', '<EMAIL>', 1, 1),
(2, 'manager', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXISwrCZJ/6HpVfS2tVZHKHHbdC', '催收主管', '13800138001', '<EMAIL>', 2, 1),
(3, 'collector1', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXISwrCZJ/6HpVfS2tVZHKHHbdC', '催收员1', '13800138002', '<EMAIL>', 2, 1);

-- 插入用户角色关联数据
INSERT INTO sys_user_role (user_id, role_id, create_by) VALUES
(1, 1, 1), -- admin用户关联超级管理员角色
(2, 2, 1), -- manager用户关联催收主管角色
(3, 3, 1); -- collector1用户关联催收员角色

-- 插入系统菜单
INSERT INTO sys_menu (id, menu_name, menu_code, parent_id, menu_type, path, permission, icon, sort_order, visible, status) VALUES
(1, '系统管理', 'SYSTEM', 0, 1, '/system', '', 'fa fa-gear', 1, 1, 1),
(100, '用户管理', 'USER_MGMT', 1, 2, '/system/user', 'system:user:view', 'fa fa-user', 1, 1, 1),
(101, '角色管理', 'ROLE_MGMT', 1, 2, '/system/role', 'system:role:view', 'fa fa-users', 2, 1, 1),
(102, '菜单管理', 'MENU_MGMT', 1, 2, '/system/menu', 'system:menu:view', 'fa fa-list', 3, 1, 1),
(1001, '用户查询', 'USER_QUERY', 100, 3, '', 'system:user:list', '', 1, 1, 1),
(1002, '用户新增', 'USER_ADD', 100, 3, '', 'system:user:add', '', 2, 1, 1),
(1003, '用户修改', 'USER_EDIT', 100, 3, '', 'system:user:edit', '', 3, 1, 1),
(1004, '用户删除', 'USER_DELETE', 100, 3, '', 'system:user:remove', '', 4, 1, 1),
(1011, '角色查询', 'ROLE_QUERY', 101, 3, '', 'system:role:list', '', 1, 1, 1),
(1012, '角色新增', 'ROLE_ADD', 101, 3, '', 'system:role:add', '', 2, 1, 1),
(1013, '角色修改', 'ROLE_EDIT', 101, 3, '', 'system:role:edit', '', 3, 1, 1),
(1014, '角色删除', 'ROLE_DELETE', 101, 3, '', 'system:role:remove', '', 4, 1, 1),
(1021, '菜单查询', 'MENU_QUERY', 102, 3, '', 'system:menu:list', '', 1, 1, 1),
(1022, '菜单新增', 'MENU_ADD', 102, 3, '', 'system:menu:add', '', 2, 1, 1),
(1023, '菜单修改', 'MENU_EDIT', 102, 3, '', 'system:menu:edit', '', 3, 1, 1),
(1024, '菜单删除', 'MENU_DELETE', 102, 3, '', 'system:menu:remove', '', 4, 1, 1);

-- 插入超级管理员角色的菜单权限（拥有所有菜单权限）
INSERT INTO sys_role_menu (role_id, menu_id, create_by) VALUES
(1, 1, 1),    -- 系统管理目录
(1, 100, 1),  -- 用户管理
(1, 101, 1),  -- 角色管理
(1, 102, 1),  -- 菜单管理
(1, 1001, 1), -- 用户查询
(1, 1002, 1), -- 用户新增
(1, 1003, 1), -- 用户修改
(1, 1004, 1), -- 用户删除
(1, 1011, 1), -- 角色查询
(1, 1012, 1), -- 角色新增
(1, 1013, 1), -- 角色修改
(1, 1014, 1), -- 角色删除
(1, 1021, 1), -- 菜单查询
(1, 1022, 1), -- 菜单新增
(1, 1023, 1), -- 菜单修改
(1, 1024, 1); -- 菜单删除

-- 插入催收主管角色的基础权限
INSERT INTO sys_role_menu (role_id, menu_id, create_by) VALUES
(2, 100, 1),  -- 用户管理
(2, 1001, 1), -- 用户查询
(2, 1002, 1); -- 用户新增

-- 插入催收员角色的基础权限
INSERT INTO sys_role_menu (role_id, menu_id, create_by) VALUES
(3, 100, 1),  -- 用户管理
(3, 1001, 1); -- 用户查询
