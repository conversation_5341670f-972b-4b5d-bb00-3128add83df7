// 案池管理页面JavaScript功能

// 全局变量
let currentPage = 1;
let pageSize = 20;
let totalRecords = 0;
let totalPages = 1;
let currentFilters = {};
let caseData = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    loadCasePoolData();
    setupEventListeners();
});

// 初始化页面
function initializePage() {
    // 初始化日期选择器
    flatpickr("#startDateRange", {
        mode: "range",
        dateFormat: "Y-m-d",
        locale: "zh"
    });

    flatpickr("#endDateRange", {
        mode: "range",
        dateFormat: "Y-m-d",
        locale: "zh"
    });

    // 设置默认页面大小
    document.getElementById('pageSizeSelect').value = pageSize;
}

// 设置事件监听器
function setupEventListeners() {
    // 全选复选框
    document.getElementById('select-all').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.row-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateStatistics();
    });

    // 搜索按钮
    document.getElementById('searchBtn').addEventListener('click', searchCases);

    // 重置按钮
    document.getElementById('resetBtn').addEventListener('click', resetFilters);

    // 功能按钮
    document.getElementById('lockBtn').addEventListener('click', batchLockCases);
    document.getElementById('unlockBtn').addEventListener('click', batchUnlockCases);
    document.getElementById('assignBtn').addEventListener('click', batchAssignCases);
    document.getElementById('exportBtn').addEventListener('click', exportData);

    // 分页相关
    document.getElementById('pageSizeSelect').addEventListener('change', changePageSize);
    document.getElementById('gotoPageBtn').addEventListener('click', goToPage);
    document.getElementById('firstPageBtn').addEventListener('click', () => changePage(1));
    document.getElementById('prevPageBtn').addEventListener('click', () => changePage(currentPage - 1));
    document.getElementById('nextPageBtn').addEventListener('click', () => changePage(currentPage + 1));
    document.getElementById('lastPageBtn').addEventListener('click', () => changePage(totalPages));

    // 回车键搜索
    document.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchCases();
        }
    });
}

// 加载案池数据
function loadCasePoolData() {
    showLoading();

    const params = new URLSearchParams({
        current: currentPage,
        size: pageSize,
        ...currentFilters
    });

    fetch(`/api/case-pool/page?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                caseData = data.data;
                renderCaseTable(data.data);
                updatePagination(data);
                loadStatistics();
            } else {
                showError('加载数据失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('加载数据失败:', error);
            showError('网络错误，请稍后重试');
        })
        .finally(() => {
            hideLoading();
        });
}

// 渲染案池表格
function renderCaseTable(cases) {
    const tbody = document.getElementById('table-body');
    tbody.innerHTML = '';

    if (!cases || cases.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="46" style="text-align: center; padding: 40px; color: #999;">
                    <i class="fas fa-inbox" style="font-size: 48px; margin-bottom: 16px; display: block;"></i>
                    暂无数据
                </td>
            </tr>
        `;
        return;
    }

    cases.forEach(caseItem => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td><div class="check-cell"><input type="checkbox" class="row-checkbox" data-id="${caseItem.clientId}"></div></td>
            <td>${caseItem.clientName || '<span class="empty-value">空值</span>'}</td>
            <td><a href="javascript:void(0);" class="client-id-link" data-id="${caseItem.clientId}">${caseItem.clientId || '<span class="empty-value">空值</span>'}</a></td>
            <td>${caseItem.cardholderCode || '<span class="empty-value">空值</span>'}</td>
            <td class="amount">${caseItem.entrustedAmount ? '¥' + formatAmount(caseItem.entrustedAmount) : '<span class="empty-value">空值</span>'}</td>
            <td class="amount">${caseItem.entrustedPrincipal ? '¥' + formatAmount(caseItem.entrustedPrincipal) : '<span class="empty-value">空值</span>'}</td>
            <td>${caseItem.batchNumber || '<span class="empty-value">空值</span>'}</td>
            <td>${caseItem.poolCount || '<span class="empty-value">空值</span>'}</td>
            <td>${caseItem.unfollowedDays || '<span class="empty-value">空值</span>'}</td>
            <td><span class="status-tag ${getConfigStatusClass(caseItem.configStatus)}">${caseItem.configStatus || '<span class="empty-value">空值</span>'}</span></td>
            <td>${caseItem.balanceOps || '<span class="empty-value">空值</span>'}</td>
            <td>${caseItem.principalOps || '<span class="empty-value">空值</span>'}</td>
            <td>${caseItem.caseType || '<span class="empty-value">空值</span>'}</td>
            <td>${caseItem.specialType || '<span class="empty-value">空值</span>'}</td>
            <td>${caseItem.repairResult || '<span class="empty-value">空值</span>'}</td>
            <td>${formatDate(caseItem.entrustStartDate)}</td>
            <td>${formatDate(caseItem.entrustEndDate)}</td>
            <td>${caseItem.retentionCount || '<span class="empty-value">空值</span>'}</td>
            <td>${formatDate(caseItem.cardOpeningDate)}</td>
            <td>${caseItem.creditLimit ? '¥' + formatAmount(caseItem.creditLimit) : '<span class="empty-value">空值</span>'}</td>
            <td>${formatDate(caseItem.lastPaymentDate)}</td>
            <td>${caseItem.overduePeriodAtEntrust || '<span class="empty-value">空值</span>'}</td>
            <td>${caseItem.targetPeriod || '<span class="empty-value">空值</span>'}</td>
            <td>${caseItem.city || '<span class="empty-value">空值</span>'}</td>
            <td>${formatDate(caseItem.lastFollowupDate)}</td>
            <td>${caseItem.caseFlag || '<span class="empty-value">空值</span>'}</td>
            <td>${caseItem.currentOverduePeriod || '<span class="empty-value">空值</span>'}</td>
            <td>${caseItem.residenceCity || '<span class="empty-value">空值</span>'}</td>
            <td>${caseItem.clientAge || '<span class="empty-value">空值</span>'}</td>
            <td>${caseItem.occupation || '<span class="empty-value">空值</span>'}</td>
            <td>${caseItem.accountLast7 || '<span class="empty-value">空值</span>'}</td>
            <td>${caseItem.monthRangeAtEntrust || '<span class="empty-value">空值</span>'}</td>
            <td>${caseItem.amountRangeAtEntrust || '<span class="empty-value">空值</span>'}</td>
            <td>${caseItem.commissionRangeAtEntrust || '<span class="empty-value">空值</span>'}</td>
            <td>${caseItem.rating || '<span class="empty-value">空值</span>'}</td>
            <td>${caseItem.isLitigation || '<span class="empty-value">空值</span>'}</td>
            <td>${caseItem.currentMonthPayment ? '¥' + formatAmount(caseItem.currentMonthPayment) : '<span class="empty-value">空值</span>'}</td>
            <td>${caseItem.previousDayPayment ? '¥' + formatAmount(caseItem.previousDayPayment) : '<span class="empty-value">空值</span>'}</td>
            <td>${caseItem.installmentStatus || '<span class="empty-value">空值</span>'}</td>
            <td>${caseItem.installmentPerformance || '<span class="empty-value">空值</span>'}</td>
            <td>${caseItem.complaintTag || '<span class="empty-value">空值</span>'}</td>
            <td>${caseItem.installmentPerformanceTag || '<span class="empty-value">空值</span>'}</td>
            <td>${caseItem.litigationTag || '<span class="empty-value">空值</span>'}</td>
            <td>${caseItem.voiceTag || '<span class="empty-value">空值</span>'}</td>
            <td>${caseItem.specialTag || '<span class="empty-value">空值</span>'}</td>
            <td>${caseItem.isLitigationCommission || '<span class="empty-value">空值</span>'}</td>
        `;
        tbody.appendChild(row);

        // 为行复选框添加事件监听器
        const checkbox = row.querySelector('.row-checkbox');
        checkbox.addEventListener('change', updateStatistics);
    });
}

// 更新分页信息
function updatePagination(pageData) {
    totalRecords = pageData.total;
    totalPages = pageData.pages;
    currentPage = pageData.current;

    // 更新分页显示
    document.getElementById('total-records').textContent = totalRecords;
    document.getElementById('pageNumInput').value = currentPage;
    document.getElementById('pageNumInput').max = totalPages;

    const startRecord = (currentPage - 1) * pageSize + 1;
    const endRecord = Math.min(currentPage * pageSize, totalRecords);
    document.getElementById('start-record').textContent = startRecord;
    document.getElementById('end-record').textContent = endRecord;

    // 更新页码按钮
    for (let i = 1; i <= 3; i++) {
        const btn = document.getElementById(`page${i}`);
        if (btn && i <= totalPages) {
            btn.style.display = 'block';
            btn.textContent = i;
            if (i === currentPage) {
                btn.classList.add('active');
            } else {
                btn.classList.remove('active');
            }
        } else if (btn) {
            btn.style.display = 'none';
        }
    }

    // 更新按钮状态
    document.getElementById('firstPageBtn').disabled = currentPage <= 1;
    document.getElementById('prevPageBtn').disabled = currentPage <= 1;
    document.getElementById('nextPageBtn').disabled = currentPage >= totalPages;
    document.getElementById('lastPageBtn').disabled = currentPage >= totalPages;
}

// 加载统计信息
function loadStatistics() {
    fetch('/api/case-pool/statistics?' + new URLSearchParams(currentFilters))
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateStatisticsDisplay(data.data);
            }
        })
        .catch(error => {
            console.error('加载统计信息失败:', error);
        });
}

// 更新统计信息显示
function updateStatisticsDisplay(stats) {
    document.getElementById('total-accounts').textContent = stats.totalAccounts || 0;
    document.getElementById('total-amount').textContent = '¥' + formatAmount(stats.totalAmount || 0);

    // 更新勾选统计
    updateSelectedStatistics();
}

// 更新勾选统计
function updateSelectedStatistics() {
    const selectedCheckboxes = document.querySelectorAll('.row-checkbox:checked');
    document.getElementById('selected-accounts').textContent = selectedCheckboxes.length;

    // 计算勾选金额
    let selectedAmount = 0;
    selectedCheckboxes.forEach(checkbox => {
        const clientId = checkbox.getAttribute('data-id');
        const item = caseData.find(d => d.clientId === clientId);
        if (item && item.entrustedAmount) {
            selectedAmount += parseFloat(item.entrustedAmount);
        }
    });

    document.getElementById('selected-amount').textContent = '¥' + formatAmount(selectedAmount);
}

// 更新统计信息（用于复选框变化时调用）
function updateStatistics() {
    updateSelectedStatistics();
}

// 搜索案池数据
function searchCases() {
    currentFilters = {
        clientName: document.getElementById('clientName').value.trim(),
        clientId: document.getElementById('clientId').value.trim(),
        city: document.getElementById('city').value.trim(),
        currentOverduePeriod: document.getElementById('overduePeriod').value.trim(),
        poolCount: document.getElementById('poolCount').value.trim(),
        batchNumber: document.getElementById('batchNumber').value.trim(),
        cardholderCode: document.getElementById('cardholderCode').value.trim(),
        specialType: document.getElementById('specialType').value.trim(),
        amountRange: document.getElementById('amountRange').value.trim(),
        unfollowedDays: document.getElementById('unfollowedDays').value.trim(),
        institution: document.getElementById('institution').value.trim(),
        targetPeriod: document.getElementById('targetPeriod').value.trim(),
        monthRange: document.getElementById('monthRange').value.trim(),
        retentionCount: document.getElementById('retentionCount').value.trim(),
        caseType: document.getElementById('caseType').value.trim(),
        litigationTag: document.getElementById('litigationTag').value.trim(),
        commissionRange: document.getElementById('commissionRange').value.trim(),
        rating: document.getElementById('rating').value.trim(),
        repairResult: document.getElementById('repairResult').value,
        configStatus: document.getElementById('configStatus').value
    };

    // 处理日期范围
    const startDateRange = document.getElementById('startDateRange').value;
    if (startDateRange) {
        const dates = startDateRange.split(' to ');
        if (dates.length === 2) {
            currentFilters.startDateBegin = dates[0];
            currentFilters.startDateEnd = dates[1];
        }
    }

    const endDateRange = document.getElementById('endDateRange').value;
    if (endDateRange) {
        const dates = endDateRange.split(' to ');
        if (dates.length === 2) {
            currentFilters.endDateBegin = dates[0];
            currentFilters.endDateEnd = dates[1];
        }
    }

    // 移除空值
    Object.keys(currentFilters).forEach(key => {
        if (!currentFilters[key]) {
            delete currentFilters[key];
        }
    });

    currentPage = 1;
    loadCasePoolData();
}

// 重置筛选条件
function resetFilters() {
    // 清空所有输入框
    const inputs = document.querySelectorAll('.filter-control');
    inputs.forEach(input => {
        if (input.type === 'select-one') {
            input.selectedIndex = 0;
        } else {
            input.value = '';
        }
    });

    currentFilters = {};
    currentPage = 1;
    loadCasePoolData();
}

// 分页相关函数
function changePageSize() {
    pageSize = parseInt(document.getElementById('pageSizeSelect').value);
    currentPage = 1;
    loadCasePoolData();
}

function changePage(page) {
    if (page >= 1 && page <= totalPages) {
        currentPage = page;
        loadCasePoolData();
    }
}

function goToPage() {
    const page = parseInt(document.getElementById('pageNumInput').value);
    if (page >= 1 && page <= totalPages) {
        currentPage = page;
        loadCasePoolData();
    } else {
        document.getElementById('pageNumInput').value = currentPage;
    }
}

// 功能按钮
function exportData() {
    const params = new URLSearchParams(currentFilters);
    window.open(`/api/case-pool/export?${params}`, '_blank');
}

function batchLockCases() {
    const selectedIds = getSelectedClientIds();
    if (selectedIds.length === 0) {
        showError('请选择要锁定的案件');
        return;
    }

    if (confirm(`确定要锁定选中的 ${selectedIds.length} 个案件吗？`)) {
        fetch('/api/case-pool/batch-lock', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(selectedIds)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess(data.message);
                loadCasePoolData();
            } else {
                showError(data.message);
            }
        })
        .catch(error => {
            console.error('锁定失败:', error);
            showError('锁定失败，请稍后重试');
        });
    }
}

function batchUnlockCases() {
    const selectedIds = getSelectedClientIds();
    if (selectedIds.length === 0) {
        showError('请选择要解锁的案件');
        return;
    }

    if (confirm(`确定要解锁选中的 ${selectedIds.length} 个案件吗？`)) {
        fetch('/api/case-pool/batch-unlock', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(selectedIds)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess(data.message);
                loadCasePoolData();
            } else {
                showError(data.message);
            }
        })
        .catch(error => {
            console.error('解锁失败:', error);
            showError('解锁失败，请稍后重试');
        });
    }
}

function batchAssignCases() {
    const selectedIds = getSelectedClientIds();
    if (selectedIds.length === 0) {
        showError('请选择要分派的案件');
        return;
    }

    // 这里应该打开分派对话框，暂时使用简单的prompt
    const assignUserId = prompt('请输入分派用户ID:');
    if (assignUserId) {
        fetch('/api/case-pool/batch-assign', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                clientIds: selectedIds,
                assignUserId: assignUserId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess(data.message);
                loadCasePoolData();
            } else {
                showError(data.message);
            }
        })
        .catch(error => {
            console.error('分派失败:', error);
            showError('分派失败，请稍后重试');
        });
    }
}

// 工具函数
function getSelectedClientIds() {
    const checkboxes = document.querySelectorAll('.row-checkbox:checked');
    return Array.from(checkboxes).map(cb => cb.getAttribute('data-id'));
}

function formatAmount(amount) {
    if (!amount) return '0.00';
    return parseFloat(amount).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
}

function formatDate(dateStr) {
    if (!dateStr) return '-';
    const date = new Date(dateStr);
    return date.toLocaleDateString('zh-CN');
}

function getConfigStatusClass(status) {
    const statusMap = {
        '已锁定': 'status-locked',
        '未锁定': 'status-unlocked'
    };
    return statusMap[status] || 'status-unlocked';
}

function showLoading() {
    // 显示加载指示器
    const tbody = document.getElementById('table-body');
    tbody.innerHTML = `
        <tr>
            <td colspan="46" style="text-align: center; padding: 40px;">
                <i class="fas fa-spinner fa-spin" style="font-size: 24px; margin-right: 8px;"></i>
                加载中...
            </td>
        </tr>
    `;
}

function hideLoading() {
    // 加载完成后会被数据替换，无需特殊处理
}

function showError(message) {
    alert('错误: ' + message);
}

function showSuccess(message) {
    alert('成功: ' + message);
}
