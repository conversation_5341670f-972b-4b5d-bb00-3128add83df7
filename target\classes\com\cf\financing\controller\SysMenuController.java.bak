package com.cf.financing.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cf.financing.common.Result;
import com.cf.financing.entity.SysMenu;
import com.cf.financing.service.ISysMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 系统菜单控制器
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Slf4j
@Api(tags = "系统菜单管理")
@RestController
@RequestMapping("/api/system/menu")
public class SysMenuController {

    @Autowired
    private ISysMenuService menuService;

    /**
     * 分页查询菜单列表
     */
    @ApiOperation("分页查询菜单列表")
    @GetMapping("/page")
    @PreAuthorize("hasAuthority('system:menu:list')")
    public Result<IPage<SysMenu>> getMenuPage(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("菜单名称") @RequestParam(required = false) String menuName,
            @ApiParam("菜单编码") @RequestParam(required = false) String menuCode,
            @ApiParam("状态") @RequestParam(required = false) Integer status,
            @ApiParam("菜单类型") @RequestParam(required = false) Integer menuType) {
        
        Page<SysMenu> page = new Page<>(current, size);
        IPage<SysMenu> result = menuService.selectMenuPage(page, menuName, menuCode, status, menuType);
        
        return Result.success(result);
    }

    /**
     * 查询菜单树形结构
     */
    @ApiOperation("查询菜单树形结构")
    @GetMapping("/tree")
    @PreAuthorize("hasAuthority('system:menu:list')")
    public Result<List<SysMenu>> getMenuTree(
            @ApiParam("状态") @RequestParam(required = false) Integer status) {
        List<SysMenu> menuTree = menuService.selectMenuTree(status);
        return Result.success(menuTree);
    }

    /**
     * 查询用户菜单树（用于前端导航）
     */
    @ApiOperation("查询用户菜单树")
    @GetMapping("/user-tree")
    public Result<List<SysMenu>> getUserMenuTree() {
        // 这里应该从当前登录用户获取用户ID，暂时使用1L
        List<SysMenu> menuTree = menuService.selectUserMenuTree(1L);
        return Result.success(menuTree);
    }

    /**
     * 查询用户权限列表
     */
    @ApiOperation("查询用户权限列表")
    @GetMapping("/user-permissions")
    public Result<List<String>> getUserPermissions() {
        // 这里应该从当前登录用户获取用户ID，暂时使用1L
        List<String> permissions = menuService.selectPermissionsByUserId(1L);
        return Result.success(permissions);
    }

    /**
     * 根据ID查询菜单详情
     */
    @ApiOperation("根据ID查询菜单详情")
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('system:menu:query')")
    public Result<SysMenu> getMenuById(@ApiParam("菜单ID") @PathVariable Long id) {
        SysMenu menu = menuService.getById(id);
        if (menu == null) {
            return Result.error("菜单不存在");
        }
        return Result.success(menu);
    }

    /**
     * 新增菜单
     */
    @ApiOperation("新增菜单")
    @PostMapping
    @PreAuthorize("hasAuthority('system:menu:add')")
    public Result<String> addMenu(@Valid @RequestBody SysMenu menu) {
        try {
            boolean success = menuService.insertMenu(menu);
            if (success) {
                return Result.success("菜单创建成功");
            } else {
                return Result.error("菜单创建失败");
            }
        } catch (Exception e) {
            log.error("创建菜单失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 修改菜单
     */
    @ApiOperation("修改菜单")
    @PutMapping
    @PreAuthorize("hasAuthority('system:menu:edit')")
    public Result<String> updateMenu(@Valid @RequestBody SysMenu menu) {
        try {
            boolean success = menuService.updateMenu(menu);
            if (success) {
                return Result.success("菜单更新成功");
            } else {
                return Result.error("菜单更新失败");
            }
        } catch (Exception e) {
            log.error("更新菜单失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 删除菜单
     */
    @ApiOperation("删除菜单")
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('system:menu:remove')")
    public Result<String> deleteMenu(@ApiParam("菜单ID") @PathVariable Long id) {
        try {
            boolean success = menuService.deleteMenu(id);
            if (success) {
                return Result.success("菜单删除成功");
            } else {
                return Result.error("菜单删除失败");
            }
        } catch (Exception e) {
            log.error("删除菜单失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 批量删除菜单
     */
    @ApiOperation("批量删除菜单")
    @DeleteMapping("/batch")
    @PreAuthorize("hasAuthority('system:menu:remove')")
    public Result<String> deleteMenus(@RequestBody List<Long> ids) {
        try {
            boolean success = menuService.deleteMenus(ids);
            if (success) {
                return Result.success("菜单批量删除成功");
            } else {
                return Result.error("菜单批量删除失败");
            }
        } catch (Exception e) {
            log.error("批量删除菜单失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 修改菜单状态
     */
    @ApiOperation("修改菜单状态")
    @PutMapping("/{id}/status")
    @PreAuthorize("hasAuthority('system:menu:edit')")
    public Result<String> updateMenuStatus(
            @ApiParam("菜单ID") @PathVariable Long id,
            @ApiParam("状态") @RequestParam Integer status) {
        try {
            // 这里应该从当前登录用户获取操作人ID，暂时使用1L
            boolean success = menuService.updateMenuStatus(id, status, 1L);
            if (success) {
                return Result.success("菜单状态更新成功");
            } else {
                return Result.error("菜单状态更新失败");
            }
        } catch (Exception e) {
            log.error("更新菜单状态失败", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 根据父菜单ID查询子菜单
     */
    @ApiOperation("根据父菜单ID查询子菜单")
    @GetMapping("/children/{parentId}")
    @PreAuthorize("hasAuthority('system:menu:list')")
    public Result<List<SysMenu>> getMenusByParentId(@ApiParam("父菜单ID") @PathVariable Long parentId) {
        List<SysMenu> menus = menuService.selectMenusByParentId(parentId);
        return Result.success(menus);
    }

    /**
     * 查询所有父菜单（目录和菜单类型）
     */
    @ApiOperation("查询所有父菜单")
    @GetMapping("/parents")
    @PreAuthorize("hasAuthority('system:menu:list')")
    public Result<List<SysMenu>> getParentMenus() {
        List<SysMenu> menus = menuService.selectParentMenus();
        return Result.success(menus);
    }

    /**
     * 检查菜单编码是否存在
     */
    @ApiOperation("检查菜单编码是否存在")
    @GetMapping("/check-code")
    @PreAuthorize("hasAuthority('system:menu:query')")
    public Result<Boolean> checkMenuCodeExists(
            @ApiParam("菜单编码") @RequestParam String menuCode,
            @ApiParam("排除的菜单ID") @RequestParam(required = false) Long excludeId) {
        boolean exists = menuService.checkMenuCodeExists(menuCode, excludeId);
        return Result.success(exists);
    }

    /**
     * 检查菜单名称是否存在（同级别下）
     */
    @ApiOperation("检查菜单名称是否存在")
    @GetMapping("/check-name")
    @PreAuthorize("hasAuthority('system:menu:query')")
    public Result<Boolean> checkMenuNameExists(
            @ApiParam("菜单名称") @RequestParam String menuName,
            @ApiParam("父菜单ID") @RequestParam(required = false) Long parentId,
            @ApiParam("排除的菜单ID") @RequestParam(required = false) Long excludeId) {
        boolean exists = menuService.checkMenuNameExists(menuName, parentId, excludeId);
        return Result.success(exists);
    }

    /**
     * 检查是否存在子菜单
     */
    @ApiOperation("检查是否存在子菜单")
    @GetMapping("/{id}/has-children")
    @PreAuthorize("hasAuthority('system:menu:query')")
    public Result<Boolean> hasChildMenus(@ApiParam("菜单ID") @PathVariable Long id) {
        boolean hasChildren = menuService.hasChildMenus(id);
        return Result.success(hasChildren);
    }

    /**
     * 检查菜单是否被角色使用
     */
    @ApiOperation("检查菜单是否被角色使用")
    @GetMapping("/{id}/in-use")
    @PreAuthorize("hasAuthority('system:menu:query')")
    public Result<Boolean> isMenuInUse(@ApiParam("菜单ID") @PathVariable Long id) {
        boolean inUse = menuService.isMenuInUse(id);
        return Result.success(inUse);
    }

    /**
     * 获取菜单统计信息
     */
    @ApiOperation("获取菜单统计信息")
    @GetMapping("/statistics")
    @PreAuthorize("hasAuthority('system:menu:query')")
    public Result<Object> getMenuStatistics() {
        Object statistics = menuService.getMenuStatistics();
        return Result.success(statistics);
    }

    /**
     * 导出菜单数据
     */
    @ApiOperation("导出菜单数据")
    @GetMapping("/export")
    @PreAuthorize("hasAuthority('system:menu:export')")
    public Result<List<SysMenu>> exportMenus(
            @ApiParam("菜单名称") @RequestParam(required = false) String menuName,
            @ApiParam("菜单编码") @RequestParam(required = false) String menuCode,
            @ApiParam("状态") @RequestParam(required = false) Integer status,
            @ApiParam("菜单类型") @RequestParam(required = false) Integer menuType) {
        List<SysMenu> menus = menuService.exportMenus(menuName, menuCode, status, menuType);
        return Result.success(menus);
    }

    /**
     * 刷新菜单缓存
     */
    @ApiOperation("刷新菜单缓存")
    @PostMapping("/refresh-cache")
    @PreAuthorize("hasAuthority('system:menu:edit')")
    public Result<String> refreshMenuCache() {
        try {
            menuService.refreshMenuCache();
            return Result.success("菜单缓存刷新成功");
        } catch (Exception e) {
            log.error("刷新菜单缓存失败", e);
            return Result.error("菜单缓存刷新失败");
        }
    }
}