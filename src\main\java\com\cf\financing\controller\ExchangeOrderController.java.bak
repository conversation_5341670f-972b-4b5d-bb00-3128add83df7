package com.cf.financing.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cf.financing.entity.ExchangeOrder;
import com.cf.financing.service.IExchangeOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 换单管理控制器
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@RestController
@RequestMapping("/api/exchange")
public class ExchangeOrderController {

    @Autowired
    private IExchangeOrderService exchangeOrderService;

    /**
     * 分页查询换单记录
     */
    @GetMapping("/page")
    public Map<String, Object> getExchangeOrderPage(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String exchangeNo,
            @RequestParam(required = false) Long customerId,
            @RequestParam(required = false) Long caseId,
            @RequestParam(required = false) Integer exchangeType,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            Page<ExchangeOrder> page = new Page<>(current, size);
            
            LocalDateTime startDateTime = null;
            LocalDateTime endDateTime = null;
            if (startTime != null && !startTime.isEmpty()) {
                startDateTime = LocalDateTime.parse(startTime);
            }
            if (endTime != null && !endTime.isEmpty()) {
                endDateTime = LocalDateTime.parse(endTime);
            }
            
            IPage<ExchangeOrder> exchangeOrderPage = exchangeOrderService.getExchangeOrderPage(
                    page, exchangeNo, customerId, caseId, exchangeType, status, startDateTime, endDateTime);
            
            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", exchangeOrderPage);
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "查询失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 创建换单申请
     */
    @PostMapping("/create")
    public Map<String, Object> createExchangeOrder(@RequestBody ExchangeOrder exchangeOrder) {
        Map<String, Object> result = new HashMap<>();
        try {
            // 检查案件是否可以换单
            if (!exchangeOrderService.canExchangeCase(exchangeOrder.getCaseId())) {
                result.put("code", 400);
                result.put("message", "该案件存在待处理的换单申请，无法重复申请");
                return result;
            }
            
            boolean success = exchangeOrderService.createExchangeOrder(exchangeOrder);
            if (success) {
                result.put("code", 200);
                result.put("message", "创建成功");
            } else {
                result.put("code", 500);
                result.put("message", "创建失败");
            }
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "创建失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 更新换单信息
     */
    @PutMapping("/update")
    public Map<String, Object> updateExchangeOrder(@RequestBody ExchangeOrder exchangeOrder) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = exchangeOrderService.updateExchangeOrder(exchangeOrder);
            if (success) {
                result.put("code", 200);
                result.put("message", "更新成功");
            } else {
                result.put("code", 500);
                result.put("message", "更新失败");
            }
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "更新失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 审核换单申请
     */
    @PutMapping("/review/{id}")
    public Map<String, Object> reviewExchangeOrder(
            @PathVariable Long id,
            @RequestParam Integer status,
            @RequestParam(required = false) String reviewRemark,
            @RequestParam Long reviewerId) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = exchangeOrderService.reviewExchangeOrder(id, status, reviewRemark, reviewerId);
            if (success) {
                result.put("code", 200);
                result.put("message", "审核成功");
            } else {
                result.put("code", 500);
                result.put("message", "审核失败");
            }
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "审核失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 执行换单
     */
    @PutMapping("/execute/{id}")
    public Map<String, Object> executeExchangeOrder(
            @PathVariable Long id,
            @RequestParam Long executorId) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = exchangeOrderService.executeExchangeOrder(id, executorId);
            if (success) {
                result.put("code", 200);
                result.put("message", "执行成功");
            } else {
                result.put("code", 500);
                result.put("message", "执行失败");
            }
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "执行失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 取消换单
     */
    @PutMapping("/cancel/{id}")
    public Map<String, Object> cancelExchangeOrder(
            @PathVariable Long id,
            @RequestParam String cancelReason,
            @RequestParam Long operatorId) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = exchangeOrderService.cancelExchangeOrder(id, cancelReason, operatorId);
            if (success) {
                result.put("code", 200);
                result.put("message", "取消成功");
            } else {
                result.put("code", 500);
                result.put("message", "取消失败");
            }
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "取消失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 根据客户查询换单记录
     */
    @GetMapping("/customer/{customerId}")
    public Map<String, Object> getExchangeOrdersByCustomer(
            @PathVariable Long customerId,
            @RequestParam(required = false) Integer status) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            List<ExchangeOrder> orders = exchangeOrderService.getExchangeOrdersByCustomer(customerId, status);
            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", orders);
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "查询失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 根据案件查询换单记录
     */
    @GetMapping("/case/{caseId}")
    public Map<String, Object> getExchangeOrdersByCaseId(
            @PathVariable Long caseId,
            @RequestParam(required = false) Integer status) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            List<ExchangeOrder> orders = exchangeOrderService.getExchangeOrdersByCaseId(caseId, status);
            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", orders);
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "查询失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 获取待审核换单
     */
    @GetMapping("/pending")
    public Map<String, Object> getPendingExchangeOrders(
            @RequestParam(required = false) Long reviewerId) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            List<ExchangeOrder> orders = exchangeOrderService.getPendingExchangeOrders(reviewerId);
            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", orders);
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "查询失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 获取换单类型统计
     */
    @GetMapping("/statistics/type")
    public Map<String, Object> getExchangeTypeStatistics(
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam(required = false) Long departmentId) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            LocalDate startLocalDate = LocalDate.parse(startDate);
            LocalDate endLocalDate = LocalDate.parse(endDate);
            
            Map<String, Object> statistics = exchangeOrderService.getExchangeTypeStatistics(
                    startLocalDate, endLocalDate, departmentId);
            
            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", statistics);
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "查询失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 获取换单状态统计
     */
    @GetMapping("/statistics/status")
    public Map<String, Object> getExchangeStatusStatistics(
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam(required = false) Long departmentId) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            LocalDate startLocalDate = LocalDate.parse(startDate);
            LocalDate endLocalDate = LocalDate.parse(endDate);
            
            Map<String, Object> statistics = exchangeOrderService.getExchangeStatusStatistics(
                    startLocalDate, endLocalDate, departmentId);
            
            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", statistics);
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "查询失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 获取换单趋势数据
     */
    @GetMapping("/statistics/trend")
    public Map<String, Object> getExchangeTrendData(
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam(required = false) Integer exchangeType,
            @RequestParam(required = false) Long departmentId) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            LocalDate startLocalDate = LocalDate.parse(startDate);
            LocalDate endLocalDate = LocalDate.parse(endDate);
            
            List<Map<String, Object>> trendData = exchangeOrderService.getExchangeTrendData(
                    startLocalDate, endLocalDate, exchangeType, departmentId);
            
            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", trendData);
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "查询失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 获取用户换单统计
     */
    @GetMapping("/statistics/user")
    public Map<String, Object> getExchangeUserStatistics(
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam(defaultValue = "exchange_count") String orderBy,
            @RequestParam(defaultValue = "10") Integer limit) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            LocalDate startLocalDate = LocalDate.parse(startDate);
            LocalDate endLocalDate = LocalDate.parse(endDate);
            
            List<Map<String, Object>> userStats = exchangeOrderService.getExchangeUserStatistics(
                    startLocalDate, endLocalDate, orderBy, limit);
            
            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", userStats);
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "查询失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 获取换单金额统计
     */
    @GetMapping("/statistics/amount")
    public Map<String, Object> getExchangeAmountStatistics(
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam(required = false) Long departmentId) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            LocalDate startLocalDate = LocalDate.parse(startDate);
            LocalDate endLocalDate = LocalDate.parse(endDate);
            
            Map<String, Object> amountStats = exchangeOrderService.getExchangeAmountStatistics(
                    startLocalDate, endLocalDate, departmentId);
            
            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", amountStats);
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "查询失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 批量审核换单
     */
    @PutMapping("/batch-review")
    public Map<String, Object> batchReviewExchangeOrders(
            @RequestBody Map<String, Object> params) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            @SuppressWarnings("unchecked")
            List<Long> ids = (List<Long>) params.get("ids");
            Integer status = (Integer) params.get("status");
            String reviewRemark = (String) params.get("reviewRemark");
            Long reviewerId = Long.valueOf(params.get("reviewerId").toString());
            
            boolean success = exchangeOrderService.batchReviewExchangeOrders(ids, status, reviewRemark, reviewerId);
            if (success) {
                result.put("code", 200);
                result.put("message", "批量审核成功");
            } else {
                result.put("code", 500);
                result.put("message", "批量审核失败");
            }
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "批量审核失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 检查案件是否可以换单
     */
    @GetMapping("/check-case/{caseId}")
    public Map<String, Object> checkCanExchangeCase(@PathVariable Long caseId) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean canExchange = exchangeOrderService.canExchangeCase(caseId);
            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", canExchange);
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "查询失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 导出换单记录
     */
    @GetMapping("/export")
    public Map<String, Object> exportExchangeRecords(
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam(required = false) Integer exchangeType,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) Long departmentId) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            LocalDate startLocalDate = LocalDate.parse(startDate);
            LocalDate endLocalDate = LocalDate.parse(endDate);
            
            String filePath = exchangeOrderService.exportExchangeRecords(
                    startLocalDate, endLocalDate, exchangeType, status, departmentId);
            
            result.put("code", 200);
            result.put("message", "导出成功");
            result.put("data", filePath);
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "导出失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 获取换单概览
     */
    @GetMapping("/overview")
    public Map<String, Object> getExchangeOverview(
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) Long departmentId) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            Map<String, Object> overview = exchangeOrderService.getExchangeOverview(userId, departmentId);
            
            result.put("code", 200);
            result.put("message", "查询成功");
            result.put("data", overview);
        } catch (Exception e) {
            result.put("code", 500);
            result.put("message", "查询失败: " + e.getMessage());
        }
        return result;
    }
}