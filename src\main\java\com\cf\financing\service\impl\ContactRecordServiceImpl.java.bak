package com.cf.financing.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cf.financing.entity.ContactRecord;
import com.cf.financing.mapper.ContactRecordMapper;
import com.cf.financing.service.IContactRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 联系记录服务实现类
 *
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Slf4j
@Service
public class ContactRecordServiceImpl extends ServiceImpl<ContactRecordMapper, ContactRecord> implements IContactRecordService {

    @Override
    public IPage<ContactRecord> getContactPage(
            Page<ContactRecord> page,
            Long caseId,
            Long customerId,
            String contactType,
            String contactResult,
            LocalDateTime startTime,
            LocalDateTime endTime,
            Long operatorId) {
        
        return baseMapper.selectContactRecordPage(
                page, caseId, null, contactType, contactResult, 
                operatorId, startTime, endTime
        );
    }

    @Override
    public List<ContactRecord> getContactsByCaseId(Long caseId) {
        return baseMapper.selectContactRecordsByCaseId(caseId);
    }

    @Override
    public List<ContactRecord> getContactsByCustomerId(Long customerId) {
        return baseMapper.selectContactRecordsByCustomerId(customerId);
    }

    @Override
    public List<ContactRecord> getContactsByOperatorId(Long operatorId) {
        return baseMapper.selectContactRecordsByOperatorId(operatorId, null, null);
    }

    @Override
    public ContactRecord getLastContactRecord(Long caseId) {
        return baseMapper.selectLastContactRecordByCaseId(caseId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createContact(ContactRecord contact) {
        try {
            // 设置默认联系时间
            if (contact.getContactTime() == null) {
                contact.setContactTime(LocalDateTime.now());
            }
            
            // 设置默认联系结果
            if (!StringUtils.hasText(contact.getContactResult())) {
                contact.setContactResult("PENDING");
            }
            
            return save(contact);
        } catch (Exception e) {
            log.error("创建联系记录失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateContact(ContactRecord contact) {
        try {
            return updateById(contact);
        } catch (Exception e) {
            log.error("更新联系记录失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteContact(Long contactId) {
        try {
            return removeById(contactId);
        } catch (Exception e) {
            log.error("删除联系记录失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteContacts(List<Long> contactIds) {
        try {
            return removeByIds(contactIds);
        } catch (Exception e) {
            log.error("批量删除联系记录失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public Integer getContactCountByCaseId(Long caseId) {
        Integer count = baseMapper.selectContactCountByCaseId(caseId);
        return count != null ? count : 0;
    }

    @Override
    public Integer getContactCountByCustomerId(Long customerId) {
        Integer count = baseMapper.selectContactCountByCustomerId(customerId);
        return count != null ? count : 0;
    }

    @Override
    public Map<String, Object> getContactStatistics(LocalDate startDate, LocalDate endDate, Long operatorId) {
        return baseMapper.selectContactStatistics(startDate, endDate, operatorId);
    }

    @Override
    public Map<String, Object> getTodayContactStatistics() {
        return baseMapper.selectTodayContactStatistics();
    }

    @Override
    public List<Map<String, Object>> getContactResultStatistics(LocalDate startDate, LocalDate endDate) {
        LocalDateTime startTime = startDate != null ? startDate.atStartOfDay() : null;
        LocalDateTime endTime = endDate != null ? endDate.atTime(23, 59, 59) : null;
        return baseMapper.selectContactResultStats(null, startTime, endTime);
    }

    @Override
    public List<Map<String, Object>> getContactTypeStatistics(LocalDate startDate, LocalDate endDate) {
        LocalDateTime startTime = startDate != null ? startDate.atStartOfDay() : null;
        LocalDateTime endTime = endDate != null ? endDate.atTime(23, 59, 59) : null;
        return baseMapper.selectContactTypeStats(null, startTime, endTime);
    }

    @Override
    public List<Map<String, Object>> getContactTrend(Integer days) {
        return baseMapper.selectContactTrendData(null, days);
    }

    @Override
    public List<Map<String, Object>> getMonthlyContactStatistics(Integer months) {
        return baseMapper.selectMonthlyContactStats(months);
    }

    @Override
    public List<ContactRecord> getTodayContacts() {
        return baseMapper.selectTodayContactRecords(null, LocalDate.now());
    }

    @Override
    public List<ContactRecord> getPromiseRepaymentRecords(LocalDate promiseDate) {
        return baseMapper.selectPromiseRepaymentRecords(null, promiseDate, promiseDate);
    }

    @Override
    public List<Map<String, Object>> getOperatorContactRanking(
            LocalDate startDate, LocalDate endDate, Integer limit) {
        LocalDateTime startTime = startDate != null ? startDate.atStartOfDay() : null;
        LocalDateTime endTime = endDate != null ? endDate.atTime(23, 59, 59) : null;
        return baseMapper.selectOperatorContactRanking(startTime, endTime, limit);
    }

    @Override
    public Map<String, Object> getContactEffectAnalysis(LocalDate startDate, LocalDate endDate) {
        LocalDateTime startTime = startDate != null ? startDate.atStartOfDay() : null;
        LocalDateTime endTime = endDate != null ? endDate.atTime(23, 59, 59) : null;
        return baseMapper.selectContactEffectAnalysis(null, startTime, endTime);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteContactsByCondition(Long caseId, LocalDate startDate, LocalDate endDate) {
        try {
            int result = baseMapper.batchDeleteContactsByCondition(caseId, startDate, endDate);
            return result > 0;
        } catch (Exception e) {
            log.error("批量删除联系记录失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public List<ContactRecord> getFollowUpRecords(Long caseId, Integer days) {
        LocalDate followUpDate = LocalDate.now().plusDays(days != null ? days : 0);
        return baseMapper.selectFollowUpRecords(null, followUpDate);
    }
}