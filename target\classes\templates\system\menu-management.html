<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>菜单管理 - CF系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        .table-container {
            max-height: 600px;
            overflow-y: auto;
        }
        .modal {
            display: none;
        }
        .modal.show {
            display: flex;
        }
        .animate-fadeIn {
            animation: fadeIn 0.3s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .tree-indent {
            padding-left: 20px;
        }
        .tree-toggle {
            cursor: pointer;
            width: 16px;
            height: 16px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        .tree-row {
            border-left: 2px solid transparent;
        }
        .tree-row.level-1 {
            border-left-color: #3b82f6;
        }
        .tree-row.level-2 {
            border-left-color: #10b981;
        }
        .tree-row.level-3 {
            border-left-color: #f59e0b;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- 主容器 -->
    <div class="container mx-auto px-4 py-6">
        <!-- 页面标题 -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">菜单管理</h1>
                    <p class="text-gray-600 mt-1">管理系统菜单和权限</p>
                </div>
                <div class="flex gap-2">
                    <button onclick="expandAll()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
                        <i class="fas fa-expand-arrows-alt"></i>
                        展开全部
                    </button>
                    <button onclick="collapseAll()" class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
                        <i class="fas fa-compress-arrows-alt"></i>
                        收起全部
                    </button>
                    <button onclick="openAddMenuModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
                        <i class="fas fa-plus"></i>
                        新增菜单
                    </button>
                </div>
            </div>
        </div>

        <!-- 搜索和筛选 -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">菜单名称</label>
                    <input type="text" id="searchMenuName" placeholder="请输入菜单名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">菜单类型</label>
                    <select id="searchMenuType" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部类型</option>
                        <option value="M">目录</option>
                        <option value="C">菜单</option>
                        <option value="F">按钮</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">状态</label>
                    <select id="searchStatus" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部状态</option>
                        <option value="1">显示</option>
                        <option value="0">隐藏</option>
                    </select>
                </div>
                <div class="flex items-end gap-2">
                    <button onclick="searchMenus()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors">
                        <i class="fas fa-search mr-2"></i>搜索
                    </button>
                    <button onclick="resetSearch()" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md transition-colors">
                        <i class="fas fa-refresh mr-2"></i>重置
                    </button>
                </div>
            </div>
        </div>

        <!-- 菜单列表 -->
        <div class="bg-white rounded-lg shadow-sm">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h2 class="text-lg font-semibold text-gray-800">菜单列表</h2>
                    <div class="flex items-center gap-2">
                        <button onclick="refreshMenuCache()" class="bg-purple-600 hover:bg-purple-700 text-white px-3 py-2 rounded-md text-sm transition-colors">
                            <i class="fas fa-sync-alt mr-2"></i>刷新缓存
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="table-container">
                <table class="w-full">
                    <thead class="bg-gray-50 sticky top-0">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">菜单名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">图标</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">排序</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">权限标识</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">组件路径</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody id="menuTableBody" class="bg-white">
                        <!-- 菜单数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 新增/编辑菜单模态框 -->
    <div id="menuModal" class="modal fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-3xl mx-4 animate-fadeIn max-h-screen overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 id="modalTitle" class="text-lg font-semibold text-gray-800">新增菜单</h3>
            </div>
            <form id="menuForm" class="p-6">
                <input type="hidden" id="menuId">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700 mb-2">上级菜单</label>
                        <select id="parentId" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="0">主类目</option>
                            <!-- 父级菜单选项将动态加载 -->
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">菜单类型 <span class="text-red-500">*</span></label>
                        <select id="menuType" required onchange="onMenuTypeChange()" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="M">目录</option>
                            <option value="C">菜单</option>
                            <option value="F">按钮</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">菜单名称 <span class="text-red-500">*</span></label>
                        <input type="text" id="menuName" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">菜单编码</label>
                        <input type="text" id="menuCode" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">显示排序</label>
                        <input type="number" id="sortOrder" value="0" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div id="iconField">
                        <label class="block text-sm font-medium text-gray-700 mb-2">菜单图标</label>
                        <div class="flex">
                            <input type="text" id="icon" placeholder="如：fas fa-home" class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <button type="button" onclick="selectIcon()" class="px-3 py-2 bg-gray-200 border border-l-0 border-gray-300 rounded-r-md hover:bg-gray-300">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div id="routerPathField">
                        <label class="block text-sm font-medium text-gray-700 mb-2">路由地址</label>
                        <input type="text" id="path" placeholder="如：/system/user" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div id="componentField">
                        <label class="block text-sm font-medium text-gray-700 mb-2">组件路径</label>
                        <input type="text" id="component" placeholder="如：system/user/index" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div id="permissionField">
                        <label class="block text-sm font-medium text-gray-700 mb-2">权限标识</label>
                        <input type="text" id="permission" placeholder="如：system:user:list" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">显示状态</label>
                        <select id="visible" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="1">显示</option>
                            <option value="0">隐藏</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">菜单状态</label>
                        <select id="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="1">正常</option>
                            <option value="0">停用</option>
                        </select>
                    </div>
                    <div id="isFrameField">
                        <label class="block text-sm font-medium text-gray-700 mb-2">是否外链</label>
                        <select id="isFrame" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="0">否</option>
                            <option value="1">是</option>
                        </select>
                    </div>
                    <div id="isCacheField">
                        <label class="block text-sm font-medium text-gray-700 mb-2">是否缓存</label>
                        <select id="isCache" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="1">缓存</option>
                            <option value="0">不缓存</option>
                        </select>
                    </div>
                </div>
                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">备注</label>
                    <textarea id="remark" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                </div>
            </form>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end gap-3">
                <button onclick="closeMenuModal()" class="px-4 py-2 text-gray-600 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors">
                    取消
                </button>
                <button onclick="saveMenu()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                    保存
                </button>
            </div>
        </div>
    </div>

    <!-- 图标选择模态框 -->
    <div id="iconModal" class="modal fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl mx-4 animate-fadeIn max-h-screen overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">选择图标</h3>
            </div>
            <div class="p-6">
                <div class="mb-4">
                    <input type="text" id="iconSearch" placeholder="搜索图标..." class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div id="iconGrid" class="grid grid-cols-8 gap-4 max-h-96 overflow-y-auto">
                    <!-- 图标网格将动态生成 -->
                </div>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end gap-3">
                <button onclick="closeIconModal()" class="px-4 py-2 text-gray-600 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors">
                    取消
                </button>
            </div>
        </div>
    </div>

    <script th:src="@{/js/menu-management.js}"></script>
</body>
</html>