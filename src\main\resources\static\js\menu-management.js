// 菜单管理JavaScript
let menuTree = [];
let allMenus = [];
let selectedMenus = [];
let currentEditingMenu = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadMenuTree();
    initIconSelector();
});

// 加载菜单树
function loadMenuTree() {
    const searchKeyword = document.getElementById('searchKeyword').value;
    
    axios.get('/api/system/menu/tree', {
        params: { keyword: searchKeyword }
    })
        .then(response => {
            if (response.data.code === 200) {
                allMenus = response.data.data;
                menuTree = buildMenuTree(allMenus);
                renderMenuTree();
            } else {
                showMessage('加载菜单树失败：' + response.data.message, 'error');
            }
        })
        .catch(error => {
            console.error('加载菜单树失败:', error);
            showMessage('加载菜单树失败', 'error');
        });
}

// 构建菜单树
function buildMenuTree(menus) {
    const tree = [];
    const map = {};
    
    // 创建映射
    menus.forEach(menu => {
        map[menu.id] = { ...menu, children: [] };
    });
    
    // 构建树结构
    menus.forEach(menu => {
        if (menu.parentId === 0) {
            tree.push(map[menu.id]);
        } else if (map[menu.parentId]) {
            map[menu.parentId].children.push(map[menu.id]);
        }
    });
    
    return tree;
}

// 渲染菜单树
function renderMenuTree() {
    const treeContainer = document.getElementById('menuTreeContainer');
    treeContainer.innerHTML = '';
    
    function renderNode(node, level = 0) {
        const div = document.createElement('div');
        div.className = 'menu-tree-node';
        div.setAttribute('data-menu-id', node.id);
        
        const hasChildren = node.children && node.children.length > 0;
        const indent = level * 24;
        
        const statusBadge = node.status === '1' 
            ? '<span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">启用</span>'
            : '<span class="px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full">禁用</span>';
            
        const typeBadge = getMenuTypeBadge(node.menuType);
        
        div.innerHTML = `
            <div class="flex items-center py-2 px-4 hover:bg-gray-50 border-b" style="margin-left: ${indent}px">
                <div class="flex items-center flex-1">
                    ${hasChildren ? 
                        `<i class="fas fa-chevron-right expand-icon mr-2 cursor-pointer text-gray-400 hover:text-gray-600" onclick="toggleNode(this, ${node.id})"></i>` : 
                        '<span class="w-4 mr-2"></span>'
                    }
                    <input type="checkbox" class="menu-checkbox rounded mr-3" value="${node.id}" onchange="updateSelectedMenus()">
                    <i class="${node.icon || 'fas fa-circle'} mr-2 text-gray-500"></i>
                    <span class="font-medium text-gray-900">${node.menuName}</span>
                    <span class="ml-2 text-sm text-gray-500">(${node.path || ''})</span>
                </div>
                <div class="flex items-center space-x-4">
                    ${typeBadge}
                    ${statusBadge}
                    <span class="text-sm text-gray-500">${node.sort || 0}</span>
                    <div class="flex space-x-1">
                        <button onclick="addChildMenu(${node.id})" class="text-green-600 hover:text-green-900" title="添加子菜单">
                            <i class="fas fa-plus"></i>
                        </button>
                        <button onclick="editMenu(${node.id})" class="text-blue-600 hover:text-blue-900" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="deleteMenu(${node.id})" class="text-red-600 hover:text-red-900" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        treeContainer.appendChild(div);
        
        if (hasChildren) {
            const childrenContainer = document.createElement('div');
            childrenContainer.className = 'children-container';
            childrenContainer.id = `children-${node.id}`;
            childrenContainer.style.display = 'none';
            
            node.children.forEach(child => {
                const childElement = renderNode(child, level + 1);
                childrenContainer.appendChild(childElement);
            });
            
            treeContainer.appendChild(childrenContainer);
        }
        
        return div;
    }
    
    if (menuTree.length === 0) {
        treeContainer.innerHTML = '<div class="text-center py-8 text-gray-500">暂无菜单数据</div>';
    } else {
        menuTree.forEach(node => renderNode(node));
    }
}

// 获取菜单类型徽章
function getMenuTypeBadge(menuType) {
    switch (menuType) {
        case 'M':
            return '<span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">目录</span>';
        case 'C':
            return '<span class="px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded-full">菜单</span>';
        case 'F':
            return '<span class="px-2 py-1 text-xs bg-orange-100 text-orange-800 rounded-full">按钮</span>';
        default:
            return '<span class="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">未知</span>';
    }
}

// 切换节点展开/收起
function toggleNode(icon, menuId) {
    const isExpanded = icon.classList.contains('fa-chevron-down');
    const childrenContainer = document.getElementById(`children-${menuId}`);
    
    if (isExpanded) {
        icon.classList.remove('fa-chevron-down');
        icon.classList.add('fa-chevron-right');
        childrenContainer.style.display = 'none';
    } else {
        icon.classList.remove('fa-chevron-right');
        icon.classList.add('fa-chevron-down');
        childrenContainer.style.display = 'block';
    }
}

// 展开所有菜单
function expandAllMenus() {
    const expandIcons = document.querySelectorAll('.expand-icon.fa-chevron-right');
    expandIcons.forEach(icon => {
        const menuId = icon.closest('.menu-tree-node').getAttribute('data-menu-id');
        toggleNode(icon, menuId);
    });
}

// 收起所有菜单
function collapseAllMenus() {
    const expandIcons = document.querySelectorAll('.expand-icon.fa-chevron-down');
    expandIcons.forEach(icon => {
        const menuId = icon.closest('.menu-tree-node').getAttribute('data-menu-id');
        toggleNode(icon, menuId);
    });
}

// 搜索菜单
function searchMenus() {
    loadMenuTree();
}

// 重置搜索
function resetSearch() {
    document.getElementById('searchKeyword').value = '';
    loadMenuTree();
}

// 刷新缓存
function refreshCache() {
    axios.post('/api/system/menu/refresh-cache')
        .then(response => {
            if (response.data.code === 200) {
                showMessage('缓存刷新成功', 'success');
            } else {
                showMessage('缓存刷新失败：' + response.data.message, 'error');
            }
        })
        .catch(error => {
            console.error('缓存刷新失败:', error);
            showMessage('缓存刷新失败', 'error');
        });
}

// 更新选中的菜单
function updateSelectedMenus() {
    const checkboxes = document.querySelectorAll('.menu-checkbox:checked');
    selectedMenus = Array.from(checkboxes).map(cb => parseInt(cb.value));
    
    const batchDeleteBtn = document.getElementById('batchDeleteBtn');
    batchDeleteBtn.disabled = selectedMenus.length === 0;
}

// 全选/取消全选
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.menu-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    
    updateSelectedMenus();
}

// 打开新增菜单模态框
function openAddMenuModal() {
    currentEditingMenu = null;
    document.getElementById('modalTitle').textContent = '新增菜单';
    document.getElementById('menuForm').reset();
    document.getElementById('menuId').value = '';
    document.getElementById('parentId').value = '0';
    document.getElementById('menuType').value = 'M';
    document.getElementById('status').value = '1';
    document.getElementById('sort').value = '0';
    updateFormFields();
    loadParentMenuOptions();
    document.getElementById('menuModal').classList.add('show');
}

// 添加子菜单
function addChildMenu(parentId) {
    currentEditingMenu = null;
    document.getElementById('modalTitle').textContent = '新增子菜单';
    document.getElementById('menuForm').reset();
    document.getElementById('menuId').value = '';
    document.getElementById('parentId').value = parentId;
    document.getElementById('menuType').value = 'C';
    document.getElementById('status').value = '1';
    document.getElementById('sort').value = '0';
    updateFormFields();
    loadParentMenuOptions(parentId);
    document.getElementById('menuModal').classList.add('show');
}

// 编辑菜单
function editMenu(id) {
    axios.get(`/api/system/menu/${id}`)
        .then(response => {
            if (response.data.code === 200) {
                currentEditingMenu = response.data.data;
                document.getElementById('modalTitle').textContent = '编辑菜单';
                fillMenuForm(currentEditingMenu);
                updateFormFields();
                loadParentMenuOptions();
                document.getElementById('menuModal').classList.add('show');
            } else {
                showMessage('获取菜单信息失败：' + response.data.message, 'error');
            }
        })
        .catch(error => {
            console.error('获取菜单信息失败:', error);
            showMessage('获取菜单信息失败', 'error');
        });
}

// 填充菜单表单
function fillMenuForm(menu) {
    document.getElementById('menuId').value = menu.id;
    document.getElementById('menuName').value = menu.menuName;
    document.getElementById('parentId').value = menu.parentId || '0';
    document.getElementById('menuType').value = menu.menuType;
    document.getElementById('path').value = menu.path || '';
    document.getElementById('component').value = menu.component || '';
    document.getElementById('perms').value = menu.perms || '';
    document.getElementById('icon').value = menu.icon || '';
    document.getElementById('sort').value = menu.sort || 0;
    document.getElementById('status').value = menu.status;
    document.getElementById('visible').value = menu.visible || '1';
    document.getElementById('isFrame').value = menu.isFrame || '0';
    document.getElementById('isCache').value = menu.isCache || '1';
    document.getElementById('remark').value = menu.remark || '';
    
    // 更新图标显示
    updateIconDisplay();
}

// 加载父菜单选项
function loadParentMenuOptions(selectedParentId = null) {
    const parentSelect = document.getElementById('parentId');
    parentSelect.innerHTML = '<option value="0">主类目</option>';
    
    function addOptions(menus, level = 0) {
        menus.forEach(menu => {
            if (currentEditingMenu && menu.id === currentEditingMenu.id) {
                return; // 不能选择自己作为父菜单
            }
            
            const prefix = '　'.repeat(level);
            const option = document.createElement('option');
            option.value = menu.id;
            option.textContent = prefix + menu.menuName;
            
            if (selectedParentId && menu.id === selectedParentId) {
                option.selected = true;
            }
            
            parentSelect.appendChild(option);
            
            if (menu.children && menu.children.length > 0) {
                addOptions(menu.children, level + 1);
            }
        });
    }
    
    addOptions(menuTree);
}

// 根据菜单类型更新表单字段显示
function updateFormFields() {
    const menuType = document.getElementById('menuType').value;
    const pathField = document.getElementById('pathField');
    const componentField = document.getElementById('componentField');
    const permsField = document.getElementById('permsField');
    const frameFields = document.getElementById('frameFields');
    
    if (menuType === 'M') { // 目录
        pathField.style.display = 'block';
        componentField.style.display = 'none';
        permsField.style.display = 'none';
        frameFields.style.display = 'block';
    } else if (menuType === 'C') { // 菜单
        pathField.style.display = 'block';
        componentField.style.display = 'block';
        permsField.style.display = 'block';
        frameFields.style.display = 'block';
    } else if (menuType === 'F') { // 按钮
        pathField.style.display = 'none';
        componentField.style.display = 'none';
        permsField.style.display = 'block';
        frameFields.style.display = 'none';
    }
}

// 关闭菜单模态框
function closeMenuModal() {
    document.getElementById('menuModal').classList.remove('show');
    currentEditingMenu = null;
}

// 保存菜单
function saveMenu() {
    const form = document.getElementById('menuForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const menuId = document.getElementById('menuId').value;
    const menuData = {
        menuName: document.getElementById('menuName').value,
        parentId: parseInt(document.getElementById('parentId').value),
        menuType: document.getElementById('menuType').value,
        path: document.getElementById('path').value,
        component: document.getElementById('component').value,
        perms: document.getElementById('perms').value,
        icon: document.getElementById('icon').value,
        sort: parseInt(document.getElementById('sort').value) || 0,
        status: document.getElementById('status').value,
        visible: document.getElementById('visible').value,
        isFrame: document.getElementById('isFrame').value,
        isCache: document.getElementById('isCache').value,
        remark: document.getElementById('remark').value
    };

    if (menuId) {
        menuData.id = parseInt(menuId);
    }

    const url = '/api/system/menu';
    const method = menuId ? 'put' : 'post';

    axios[method](url, menuData)
        .then(response => {
            if (response.data.code === 200) {
                showMessage(menuId ? '菜单更新成功' : '菜单创建成功', 'success');
                closeMenuModal();
                loadMenuTree();
            } else {
                showMessage(response.data.message, 'error');
            }
        })
        .catch(error => {
            console.error('保存菜单失败:', error);
            showMessage('保存菜单失败', 'error');
        });
}

// 删除菜单
function deleteMenu(id) {
    if (confirm('确定要删除这个菜单吗？删除后不可恢复！')) {
        axios.delete(`/api/system/menu/${id}`)
            .then(response => {
                if (response.data.code === 200) {
                    showMessage('菜单删除成功', 'success');
                    loadMenuTree();
                } else {
                    showMessage('删除失败：' + response.data.message, 'error');
                }
            })
            .catch(error => {
                console.error('删除菜单失败:', error);
                showMessage('删除菜单失败', 'error');
            });
    }
}

// 批量删除菜单
function batchDeleteMenus() {
    if (selectedMenus.length === 0) {
        showMessage('请选择要删除的菜单', 'warning');
        return;
    }

    if (confirm(`确定要删除选中的 ${selectedMenus.length} 个菜单吗？删除后不可恢复！`)) {
        axios.delete('/api/system/menu/batch', { data: selectedMenus })
            .then(response => {
                if (response.data.code === 200) {
                    showMessage('批量删除成功', 'success');
                    selectedMenus = [];
                    document.getElementById('selectAll').checked = false;
                    loadMenuTree();
                } else {
                    showMessage('批量删除失败：' + response.data.message, 'error');
                }
            })
            .catch(error => {
                console.error('批量删除失败:', error);
                showMessage('批量删除失败', 'error');
            });
    }
}

// 初始化图标选择器
function initIconSelector() {
    const iconInput = document.getElementById('icon');
    const iconDisplay = document.getElementById('iconDisplay');
    const iconSelector = document.getElementById('iconSelector');
    
    // 图标输入框变化时更新显示
    iconInput.addEventListener('input', updateIconDisplay);
    
    // 点击图标显示区域打开选择器
    iconDisplay.addEventListener('click', () => {
        document.getElementById('iconModal').classList.add('show');
        renderIconGrid();
    });
}

// 更新图标显示
function updateIconDisplay() {
    const iconInput = document.getElementById('icon');
    const iconDisplay = document.getElementById('iconDisplay');
    const iconClass = iconInput.value || 'fas fa-circle';
    
    iconDisplay.innerHTML = `<i class="${iconClass} text-xl"></i>`;
}

// 渲染图标网格
function renderIconGrid() {
    const iconGrid = document.getElementById('iconGrid');
    
    // 常用图标列表
    const commonIcons = [
        'fas fa-home', 'fas fa-user', 'fas fa-users', 'fas fa-cog', 'fas fa-chart-bar',
        'fas fa-file', 'fas fa-folder', 'fas fa-edit', 'fas fa-trash', 'fas fa-plus',
        'fas fa-minus', 'fas fa-search', 'fas fa-filter', 'fas fa-download', 'fas fa-upload',
        'fas fa-save', 'fas fa-print', 'fas fa-copy', 'fas fa-cut', 'fas fa-paste',
        'fas fa-undo', 'fas fa-redo', 'fas fa-refresh', 'fas fa-lock', 'fas fa-unlock',
        'fas fa-key', 'fas fa-shield-alt', 'fas fa-eye', 'fas fa-eye-slash', 'fas fa-heart',
        'fas fa-star', 'fas fa-bookmark', 'fas fa-tag', 'fas fa-tags', 'fas fa-flag',
        'fas fa-bell', 'fas fa-envelope', 'fas fa-phone', 'fas fa-mobile-alt', 'fas fa-laptop',
        'fas fa-desktop', 'fas fa-tablet-alt', 'fas fa-camera', 'fas fa-image', 'fas fa-video',
        'fas fa-music', 'fas fa-headphones', 'fas fa-microphone', 'fas fa-volume-up', 'fas fa-volume-down',
        'fas fa-calendar', 'fas fa-clock', 'fas fa-map', 'fas fa-map-marker-alt', 'fas fa-compass',
        'fas fa-car', 'fas fa-plane', 'fas fa-train', 'fas fa-ship', 'fas fa-bicycle',
        'fas fa-shopping-cart', 'fas fa-credit-card', 'fas fa-money-bill-alt', 'fas fa-gift', 'fas fa-trophy',
        'fas fa-graduation-cap', 'fas fa-book', 'fas fa-bookmark', 'fas fa-library', 'fas fa-university'
    ];
    
    iconGrid.innerHTML = '';
    
    commonIcons.forEach(iconClass => {
        const iconDiv = document.createElement('div');
        iconDiv.className = 'icon-item p-3 text-center cursor-pointer hover:bg-gray-100 rounded';
        iconDiv.innerHTML = `<i class="${iconClass} text-xl mb-1"></i><div class="text-xs text-gray-600">${iconClass.split(' ')[1]}</div>`;
        iconDiv.onclick = () => selectIcon(iconClass);
        iconGrid.appendChild(iconDiv);
    });
}

// 选择图标
function selectIcon(iconClass) {
    document.getElementById('icon').value = iconClass;
    updateIconDisplay();
    closeIconModal();
}

// 关闭图标选择模态框
function closeIconModal() {
    document.getElementById('iconModal').classList.remove('show');
}

// 显示消息
function showMessage(message, type = 'info') {
    // 创建消息元素
    const messageDiv = document.createElement('div');
    messageDiv.className = `fixed top-4 right-4 px-4 py-2 rounded-md text-white z-50 ${
        type === 'success' ? 'bg-green-500' :
        type === 'error' ? 'bg-red-500' :
        type === 'warning' ? 'bg-yellow-500' :
        'bg-blue-500'
    }`;
    messageDiv.textContent = message;
    
    document.body.appendChild(messageDiv);
    
    // 3秒后自动移除
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.parentNode.removeChild(messageDiv);
        }
    }, 3000);
}